{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\vedika\\\\ai-generative\\\\client\\\\src\\\\components\\\\LengthSelector.jsx\",\n  _s = $RefreshSig$();\nimport { useState } from 'react';\nimport './LengthSelector.css';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst LengthSelector = ({\n  value,\n  onChange\n}) => {\n  _s();\n  const [customLength, setCustomLength] = useState(value);\n  const presetLengths = [{\n    value: 100,\n    label: 'Short',\n    icon: '📝',\n    description: '~100 words'\n  }, {\n    value: 300,\n    label: 'Medium',\n    icon: '📄',\n    description: '~300 words'\n  }, {\n    value: 500,\n    label: 'Long',\n    icon: '📚',\n    description: '~500 words'\n  }];\n  const handlePresetClick = length => {\n    setCustomLength(length);\n    onChange && onChange(length);\n  };\n  const handleCustomChange = e => {\n    const newValue = parseInt(e.target.value) || 100;\n    setCustomLength(newValue);\n    onChange && onChange(newValue);\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"length-selector\",\n    children: [/*#__PURE__*/_jsxDEV(\"label\", {\n      className: \"selector-label\",\n      children: [/*#__PURE__*/_jsxDEV(\"span\", {\n        className: \"label-icon\",\n        children: \"\\uD83D\\uDCCF\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 27,\n        columnNumber: 9\n      }, this), \"Content Length\"]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 26,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"length-presets\",\n      children: presetLengths.map(preset => /*#__PURE__*/_jsxDEV(\"button\", {\n        className: `length-preset ${value === preset.value ? 'active' : ''}`,\n        onClick: () => handlePresetClick(preset.value),\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"preset-icon\",\n          children: preset.icon\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 38,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"preset-content\",\n          children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n            className: \"preset-title\",\n            children: preset.label\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 40,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"preset-description\",\n            children: preset.description\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 41,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 39,\n          columnNumber: 13\n        }, this)]\n      }, preset.value, true, {\n        fileName: _jsxFileName,\n        lineNumber: 33,\n        columnNumber: 11\n      }, this))\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 31,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"custom-length\",\n      children: [/*#__PURE__*/_jsxDEV(\"label\", {\n        className: \"custom-label\",\n        children: [/*#__PURE__*/_jsxDEV(\"span\", {\n          className: \"custom-icon\",\n          children: \"\\uD83C\\uDFAF\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 49,\n          columnNumber: 11\n        }, this), \"Custom Length\"]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 48,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"custom-input-container\",\n        children: [/*#__PURE__*/_jsxDEV(\"input\", {\n          type: \"range\",\n          min: \"50\",\n          max: \"2000\",\n          step: \"50\",\n          value: customLength,\n          onChange: handleCustomChange,\n          className: \"length-slider\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 53,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"length-display\",\n          children: [/*#__PURE__*/_jsxDEV(\"input\", {\n            type: \"number\",\n            min: \"50\",\n            max: \"2000\",\n            value: customLength,\n            onChange: handleCustomChange,\n            className: \"length-input\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 63,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"length-unit\",\n            children: \"words\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 71,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 62,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 52,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 47,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 25,\n    columnNumber: 5\n  }, this);\n};\n_s(LengthSelector, \"iFHQC3qOxenDYOyw+c8E4PnavXQ=\");\n_c = LengthSelector;\nexport default LengthSelector;\nvar _c;\n$RefreshReg$(_c, \"LengthSelector\");", "map": {"version": 3, "names": ["useState", "jsxDEV", "_jsxDEV", "LengthSelector", "value", "onChange", "_s", "customLength", "setCustomLength", "presetLengths", "label", "icon", "description", "handlePresetClick", "length", "handleCustomChange", "e", "newValue", "parseInt", "target", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "map", "preset", "onClick", "type", "min", "max", "step", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/vedika/ai-generative/client/src/components/LengthSelector.jsx"], "sourcesContent": ["import { useState } from 'react';\r\nimport './LengthSelector.css';\r\n\r\nconst LengthSelector = ({ value, onChange }) => {\r\n  const [customLength, setCustomLength] = useState(value);\r\n\r\n  const presetLengths = [\r\n    { value: 100, label: 'Short', icon: '📝', description: '~100 words' },\r\n    { value: 300, label: 'Medium', icon: '📄', description: '~300 words' },\r\n    { value: 500, label: 'Long', icon: '📚', description: '~500 words' }\r\n  ];\r\n\r\n  const handlePresetClick = (length) => {\r\n    setCustomLength(length);\r\n    onChange && onChange(length);\r\n  };\r\n\r\n  const handleCustomChange = (e) => {\r\n    const newValue = parseInt(e.target.value) || 100;\r\n    setCustomLength(newValue);\r\n    onChange && onChange(newValue);\r\n  };\r\n\r\n  return (\r\n    <div className=\"length-selector\">\r\n      <label className=\"selector-label\">\r\n        <span className=\"label-icon\">📏</span>\r\n        Content Length\r\n      </label>\r\n\r\n      <div className=\"length-presets\">\r\n        {presetLengths.map((preset) => (\r\n          <button\r\n            key={preset.value}\r\n            className={`length-preset ${value === preset.value ? 'active' : ''}`}\r\n            onClick={() => handlePresetClick(preset.value)}\r\n          >\r\n            <div className=\"preset-icon\">{preset.icon}</div>\r\n            <div className=\"preset-content\">\r\n              <h4 className=\"preset-title\">{preset.label}</h4>\r\n              <p className=\"preset-description\">{preset.description}</p>\r\n            </div>\r\n          </button>\r\n        ))}\r\n      </div>\r\n\r\n      <div className=\"custom-length\">\r\n        <label className=\"custom-label\">\r\n          <span className=\"custom-icon\">🎯</span>\r\n          Custom Length\r\n        </label>\r\n        <div className=\"custom-input-container\">\r\n          <input\r\n            type=\"range\"\r\n            min=\"50\"\r\n            max=\"2000\"\r\n            step=\"50\"\r\n            value={customLength}\r\n            onChange={handleCustomChange}\r\n            className=\"length-slider\"\r\n          />\r\n          <div className=\"length-display\">\r\n            <input\r\n              type=\"number\"\r\n              min=\"50\"\r\n              max=\"2000\"\r\n              value={customLength}\r\n              onChange={handleCustomChange}\r\n              className=\"length-input\"\r\n            />\r\n            <span className=\"length-unit\">words</span>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default LengthSelector;"], "mappings": ";;AAAA,SAASA,QAAQ,QAAQ,OAAO;AAChC,OAAO,sBAAsB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE9B,MAAMC,cAAc,GAAGA,CAAC;EAAEC,KAAK;EAAEC;AAAS,CAAC,KAAK;EAAAC,EAAA;EAC9C,MAAM,CAACC,YAAY,EAAEC,eAAe,CAAC,GAAGR,QAAQ,CAACI,KAAK,CAAC;EAEvD,MAAMK,aAAa,GAAG,CACpB;IAAEL,KAAK,EAAE,GAAG;IAAEM,KAAK,EAAE,OAAO;IAAEC,IAAI,EAAE,IAAI;IAAEC,WAAW,EAAE;EAAa,CAAC,EACrE;IAAER,KAAK,EAAE,GAAG;IAAEM,KAAK,EAAE,QAAQ;IAAEC,IAAI,EAAE,IAAI;IAAEC,WAAW,EAAE;EAAa,CAAC,EACtE;IAAER,KAAK,EAAE,GAAG;IAAEM,KAAK,EAAE,MAAM;IAAEC,IAAI,EAAE,IAAI;IAAEC,WAAW,EAAE;EAAa,CAAC,CACrE;EAED,MAAMC,iBAAiB,GAAIC,MAAM,IAAK;IACpCN,eAAe,CAACM,MAAM,CAAC;IACvBT,QAAQ,IAAIA,QAAQ,CAACS,MAAM,CAAC;EAC9B,CAAC;EAED,MAAMC,kBAAkB,GAAIC,CAAC,IAAK;IAChC,MAAMC,QAAQ,GAAGC,QAAQ,CAACF,CAAC,CAACG,MAAM,CAACf,KAAK,CAAC,IAAI,GAAG;IAChDI,eAAe,CAACS,QAAQ,CAAC;IACzBZ,QAAQ,IAAIA,QAAQ,CAACY,QAAQ,CAAC;EAChC,CAAC;EAED,oBACEf,OAAA;IAAKkB,SAAS,EAAC,iBAAiB;IAAAC,QAAA,gBAC9BnB,OAAA;MAAOkB,SAAS,EAAC,gBAAgB;MAAAC,QAAA,gBAC/BnB,OAAA;QAAMkB,SAAS,EAAC,YAAY;QAAAC,QAAA,EAAC;MAAE;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM,CAAC,kBAExC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAO,CAAC,eAERvB,OAAA;MAAKkB,SAAS,EAAC,gBAAgB;MAAAC,QAAA,EAC5BZ,aAAa,CAACiB,GAAG,CAAEC,MAAM,iBACxBzB,OAAA;QAEEkB,SAAS,EAAE,iBAAiBhB,KAAK,KAAKuB,MAAM,CAACvB,KAAK,GAAG,QAAQ,GAAG,EAAE,EAAG;QACrEwB,OAAO,EAAEA,CAAA,KAAMf,iBAAiB,CAACc,MAAM,CAACvB,KAAK,CAAE;QAAAiB,QAAA,gBAE/CnB,OAAA;UAAKkB,SAAS,EAAC,aAAa;UAAAC,QAAA,EAAEM,MAAM,CAAChB;QAAI;UAAAW,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,eAChDvB,OAAA;UAAKkB,SAAS,EAAC,gBAAgB;UAAAC,QAAA,gBAC7BnB,OAAA;YAAIkB,SAAS,EAAC,cAAc;YAAAC,QAAA,EAAEM,MAAM,CAACjB;UAAK;YAAAY,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC,eAChDvB,OAAA;YAAGkB,SAAS,EAAC,oBAAoB;YAAAC,QAAA,EAAEM,MAAM,CAACf;UAAW;YAAAU,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACvD,CAAC;MAAA,GARDE,MAAM,CAACvB,KAAK;QAAAkB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OASX,CACT;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC,CAAC,eAENvB,OAAA;MAAKkB,SAAS,EAAC,eAAe;MAAAC,QAAA,gBAC5BnB,OAAA;QAAOkB,SAAS,EAAC,cAAc;QAAAC,QAAA,gBAC7BnB,OAAA;UAAMkB,SAAS,EAAC,aAAa;UAAAC,QAAA,EAAC;QAAE;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,iBAEzC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAO,CAAC,eACRvB,OAAA;QAAKkB,SAAS,EAAC,wBAAwB;QAAAC,QAAA,gBACrCnB,OAAA;UACE2B,IAAI,EAAC,OAAO;UACZC,GAAG,EAAC,IAAI;UACRC,GAAG,EAAC,MAAM;UACVC,IAAI,EAAC,IAAI;UACT5B,KAAK,EAAEG,YAAa;UACpBF,QAAQ,EAAEU,kBAAmB;UAC7BK,SAAS,EAAC;QAAe;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC1B,CAAC,eACFvB,OAAA;UAAKkB,SAAS,EAAC,gBAAgB;UAAAC,QAAA,gBAC7BnB,OAAA;YACE2B,IAAI,EAAC,QAAQ;YACbC,GAAG,EAAC,IAAI;YACRC,GAAG,EAAC,MAAM;YACV3B,KAAK,EAAEG,YAAa;YACpBF,QAAQ,EAAEU,kBAAmB;YAC7BK,SAAS,EAAC;UAAc;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACzB,CAAC,eACFvB,OAAA;YAAMkB,SAAS,EAAC,aAAa;YAAAC,QAAA,EAAC;UAAK;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACvC,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAACnB,EAAA,CAzEIH,cAAc;AAAA8B,EAAA,GAAd9B,cAAc;AA2EpB,eAAeA,cAAc;AAAC,IAAA8B,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}