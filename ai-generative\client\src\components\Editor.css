.editor-container {
  position: relative;
  background: white;
  border-radius: 16px;
  overflow: hidden;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  border: 2px solid #e2e8f0;
  transition: all 0.3s ease;
}

.editor-container:hover {
  box-shadow: 0 12px 40px rgba(0, 0, 0, 0.15);
  border-color: #667eea;
}

.editor-header {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  padding: 1.5rem;
  display: flex;
  justify-content: space-between;
  align-items: center;
  position: relative;
  overflow: hidden;
}

.editor-header::before {
  content: '✨';
  position: absolute;
  top: 10px;
  right: 20px;
  font-size: 1.5rem;
  opacity: 0.3;
  animation: twinkle 2s ease-in-out infinite;
}

.editor-title {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  font-size: 1.3rem;
  font-weight: 600;
  margin: 0;
}

.editor-icon {
  font-size: 1.5rem;
  animation: wiggle 3s ease-in-out infinite;
}

.editor-stats {
  display: flex;
  gap: 1.5rem;
  font-size: 0.9rem;
}

.stat {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  background: rgba(255, 255, 255, 0.2);
  padding: 0.5rem 1rem;
  border-radius: 20px;
  backdrop-filter: blur(10px);
}

.stat-icon {
  font-size: 1rem;
}

.editor-wrapper {
  position: relative;
  min-height: 400px;
}

.editor-textarea {
  width: 100%;
  min-height: 400px;
  padding: 2rem;
  border: none;
  outline: none;
  font-family: 'Georgia', serif;
  font-size: 1rem;
  line-height: 1.6;
  color: #1e293b;
  background: white;
  resize: vertical;
  transition: all 0.3s ease;
}

.editor-textarea::placeholder {
  color: #94a3b8;
  font-style: italic;
  text-align: center;
  padding-top: 2rem;
}

.editor-textarea:focus {
  background: #fafafa;
}

.editor-textarea:disabled {
  background: #f8fafc;
  color: #94a3b8;
  cursor: not-allowed;
}

/* Loading Overlay */
.loading-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(255, 255, 255, 0.95);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 10;
  backdrop-filter: blur(5px);
}

.loading-content {
  text-align: center;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 1rem;
}

.loading-spinner {
  width: 50px;
  height: 50px;
  border: 4px solid #e2e8f0;
  border-top: 4px solid #667eea;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

.loading-text {
  font-size: 1.1rem;
  color: #667eea;
  font-weight: 500;
}

.loading-emojis {
  display: flex;
  gap: 1rem;
}

.loading-emoji {
  font-size: 1.5rem;
  animation: bounce 1.5s ease-in-out infinite;
}

.loading-emoji:nth-child(1) {
  animation-delay: 0s;
}

.loading-emoji:nth-child(2) {
  animation-delay: 0.3s;
}

.loading-emoji:nth-child(3) {
  animation-delay: 0.6s;
}

/* Editor Actions */
.editor-actions {
  background: #f8fafc;
  padding: 1.5rem;
  display: flex;
  gap: 1rem;
  justify-content: center;
  border-top: 1px solid #e2e8f0;
}

.action-btn {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.75rem 1.5rem;
  border: 2px solid transparent;
  border-radius: 10px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.action-btn::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
  transition: left 0.5s ease;
}

.action-btn:hover::before {
  left: 100%;
}

.action-btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
  transform: none;
}

.copy-btn {
  background: linear-gradient(135deg, #10b981 0%, #059669 100%);
  color: white;
}

.copy-btn:hover:not(:disabled) {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(16, 185, 129, 0.3);
}

.clear-btn {
  background: linear-gradient(135deg, #ef4444 0%, #dc2626 100%);
  color: white;
}

.clear-btn:hover:not(:disabled) {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(239, 68, 68, 0.3);
}

.regenerate-btn {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
}

.regenerate-btn:hover:not(:disabled) {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(102, 126, 234, 0.3);
}

.btn-icon {
  font-size: 1.1rem;
}

/* Editor Footer */
.editor-footer {
  padding: 1rem 2rem;
  background: linear-gradient(135deg, #10b981 0%, #059669 100%);
}

.success-message {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  color: white;
  font-weight: 500;
  animation: slideIn 0.5s ease-out;
}

.success-icon {
  font-size: 1.3rem;
  animation: bounce 2s ease-in-out infinite;
}

/* Responsive Design */
@media (max-width: 768px) {
  .editor-header {
    flex-direction: column;
    gap: 1rem;
    text-align: center;
  }

  .editor-stats {
    justify-content: center;
  }

  .editor-textarea {
    padding: 1.5rem;
    min-height: 300px;
  }

  .editor-actions {
    flex-direction: column;
    gap: 0.75rem;
  }

  .action-btn {
    justify-content: center;
  }
}

/* Animation Keyframes */
@keyframes spin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

@keyframes bounce {
  0%, 100% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-10px);
  }
}

@keyframes wiggle {
  0%, 100% {
    transform: rotate(0deg);
  }
  25% {
    transform: rotate(3deg);
  }
  75% {
    transform: rotate(-3deg);
  }
}

@keyframes twinkle {
  0%, 100% {
    opacity: 0.3;
    transform: scale(0.8);
  }
  50% {
    opacity: 0.8;
    transform: scale(1.2);
  }
}

@keyframes slideIn {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}
