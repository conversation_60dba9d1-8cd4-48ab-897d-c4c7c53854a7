# AWS Deployment Guide

## Prerequisites
1. AWS Account
2. AWS CLI installed and configured
3. Basic knowledge of AWS services

## Architecture Overview
- **Frontend**: S3 + CloudFront
- **Backend**: EC2 or Elastic Beanstalk
- **Database**: RDS (optional)
- **Domain**: Route 53

## Frontend Deployment (S3 + CloudFront)

### Step 1: Build Frontend
```bash
cd ai-generative/client
npm run build
```

### Step 2: Create S3 Bucket
```bash
# Create bucket (replace with unique name)
aws s3 mb s3://your-app-name-frontend

# Enable static website hosting
aws s3 website s3://your-app-name-frontend \
  --index-document index.html \
  --error-document index.html

# Upload build files
aws s3 sync build/ s3://your-app-name-frontend --delete

# Set public read policy
aws s3api put-bucket-policy \
  --bucket your-app-name-frontend \
  --policy '{
    "Version": "2012-10-17",
    "Statement": [{
      "Sid": "PublicReadGetObject",
      "Effect": "Allow",
      "Principal": "*",
      "Action": "s3:GetObject",
      "Resource": "arn:aws:s3:::your-app-name-frontend/*"
    }]
  }'
```

### Step 3: Create CloudFront Distribution
```bash
# Create distribution (via AWS Console is easier)
# Point to S3 bucket
# Enable compression
# Set default root object to index.html
# Configure error pages to redirect to index.html
```

## Backend Deployment (Elastic Beanstalk)

### Step 1: Prepare Backend
```bash
cd ai-generative/backend
```

### Step 2: Create .ebextensions/nodecommand.config
```yaml
option_settings:
  aws:elasticbeanstalk:container:nodejs:
    NodeCommand: "npm start"
  aws:elasticbeanstalk:application:environment:
    NODE_ENV: production
    PORT: 8080
```

### Step 3: Deploy to Elastic Beanstalk
```bash
# Install EB CLI
pip install awsebcli

# Initialize EB application
eb init

# Create environment
eb create production

# Set environment variables
eb setenv OPENAI_API_KEY=your_key
eb setenv JWT_SECRET=your_secret

# Deploy
eb deploy
```

## Database Setup (RDS - Optional)

### Step 1: Create RDS Instance
```bash
# Create PostgreSQL instance
aws rds create-db-instance \
  --db-instance-identifier ai-content-db \
  --db-instance-class db.t3.micro \
  --engine postgres \
  --master-username admin \
  --master-user-password your_password \
  --allocated-storage 20
```

### Step 2: Update Backend for Database
```javascript
// Add to backend/index.js
const { Pool } = require('pg');

const pool = new Pool({
  connectionString: process.env.DATABASE_URL,
  ssl: process.env.NODE_ENV === 'production' ? { rejectUnauthorized: false } : false
});
```

## Domain Setup (Route 53)

### Step 1: Create Hosted Zone
```bash
# Create hosted zone for your domain
aws route53 create-hosted-zone \
  --name yourdomain.com \
  --caller-reference $(date +%s)
```

### Step 2: Create Records
```bash
# Point domain to CloudFront distribution
# Create A record for @ and www
# Point API subdomain to Elastic Beanstalk
```

## SSL Certificate (Certificate Manager)

### Step 1: Request Certificate
```bash
# Request SSL certificate
aws acm request-certificate \
  --domain-name yourdomain.com \
  --subject-alternative-names www.yourdomain.com api.yourdomain.com \
  --validation-method DNS
```

### Step 2: Validate Certificate
```bash
# Add DNS validation records to Route 53
# Certificate will be automatically validated
```

## Monitoring and Logging

### CloudWatch Setup
```bash
# Enable CloudWatch logs for Elastic Beanstalk
# Set up alarms for high CPU, memory usage
# Monitor API response times
```

### Cost Optimization
- Use t3.micro instances for low traffic
- Enable S3 lifecycle policies
- Use CloudFront caching
- Set up auto-scaling for backend

## Deployment Script
```bash
#!/bin/bash
# deploy-aws.sh

echo "Building frontend..."
cd ai-generative/client
npm run build

echo "Uploading to S3..."
aws s3 sync build/ s3://your-app-name-frontend --delete

echo "Invalidating CloudFront..."
aws cloudfront create-invalidation \
  --distribution-id YOUR_DISTRIBUTION_ID \
  --paths "/*"

echo "Deploying backend..."
cd ../backend
eb deploy

echo "Deployment complete!"
```
