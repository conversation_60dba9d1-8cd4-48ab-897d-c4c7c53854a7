import './ToneSelector.css';

const ToneSelector = ({ value, onChange }) => {
  const tones = [
    { value: 'professional', label: 'Professional', icon: '👔', description: 'Formal and business-like' },
    { value: 'casual', label: 'Casual', icon: '😊', description: 'Relaxed and friendly' },
    { value: 'friendly', label: 'Friendly', icon: '🤗', description: 'Warm and approachable' }
  ];

  return (
    <div className="tone-selector">
      <label className="selector-label">
        <span className="label-icon">🎭</span>
        Writing Tone
      </label>
      <div className="tone-grid">
        {tones.map((tone) => (
          <button
            key={tone.value}
            className={`tone-card ${value === tone.value ? 'active' : ''}`}
            onClick={() => onChange && onChange(tone.value)}
          >
            <div className="tone-icon">{tone.icon}</div>
            <div className="tone-content">
              <h4 className="tone-title">{tone.label}</h4>
              <p className="tone-description">{tone.description}</p>
            </div>
          </button>
        ))}
      </div>
    </div>
  );
};

export default ToneSelector;