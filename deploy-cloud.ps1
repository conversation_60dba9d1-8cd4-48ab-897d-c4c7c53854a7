# Cloud Deployment Helper Script
param(
    [Parameter(Mandatory=$true)]
    [ValidateSet("heroku", "vercel", "netlify", "aws", "digitalocean")]
    [string]$Platform,
    
    [Parameter(Mandatory=$false)]
    [string]$AppName = "ai-content-generator",
    
    [Parameter(Mandatory=$false)]
    [switch]$BackendOnly,
    
    [Parameter(Mandatory=$false)]
    [switch]$FrontendOnly
)

Write-Host "Cloud Deployment Script for AI Content Generator" -ForegroundColor Cyan
Write-Host "Platform: $Platform" -ForegroundColor Yellow
Write-Host "App Name: $AppName" -ForegroundColor Yellow

# Check prerequisites
function Test-Command($cmdname) {
    return [bool](Get-Command -Name $cmdname -ErrorAction SilentlyContinue)
}

switch ($Platform) {
    "heroku" {
        Write-Host "`nPreparing Heroku deployment..." -ForegroundColor Green
        
        if (-not (Test-Command "heroku")) {
            Write-Error "Heroku CLI not found. Please install from https://devcenter.heroku.com/articles/heroku-cli"
            exit 1
        }
        
        if (-not $FrontendOnly) {
            Write-Host "Deploying backend to Heroku..." -ForegroundColor Yellow
            Set-Location "ai-generative/backend"
            
            # Create Procfile
            "web: npm start" | Out-File -FilePath "Procfile" -Encoding ASCII
            
            # Update package.json for Heroku
            $packageJson = Get-Content "package.json" | ConvertFrom-Json
            $packageJson.engines = @{ node = "18.x" }
            $packageJson | ConvertTo-Json -Depth 10 | Out-File "package.json" -Encoding UTF8
            
            Write-Host "Created Heroku configuration files" -ForegroundColor Green
            Write-Host "Next steps:" -ForegroundColor Cyan
            Write-Host "1. heroku login" -ForegroundColor White
            Write-Host "2. heroku create $AppName-api" -ForegroundColor White
            Write-Host "3. heroku config:set OPENAI_API_KEY=your_key" -ForegroundColor White
            Write-Host "4. heroku config:set JWT_SECRET=your_secret" -ForegroundColor White
            Write-Host "5. git init && git add . && git commit -m 'Deploy'" -ForegroundColor White
            Write-Host "6. git push heroku main" -ForegroundColor White
            
            Set-Location "../.."
        }
        
        if (-not $BackendOnly) {
            Write-Host "Frontend deployment options:" -ForegroundColor Yellow
            Write-Host "Option 1: Netlify (Recommended)" -ForegroundColor Green
            Write-Host "  1. cd ai-generative/client && npm run build" -ForegroundColor White
            Write-Host "  2. Go to netlify.com and drag/drop the 'build' folder" -ForegroundColor White
            Write-Host "Option 2: Heroku" -ForegroundColor Green
            Write-Host "  1. Add serve dependency and Procfile" -ForegroundColor White
            Write-Host "  2. heroku create $AppName-frontend" -ForegroundColor White
        }
    }
    
    "vercel" {
        Write-Host "`nPreparing Vercel deployment..." -ForegroundColor Green
        
        if (-not (Test-Command "vercel")) {
            Write-Host "Installing Vercel CLI..." -ForegroundColor Yellow
            npm install -g vercel
        }
        
        Set-Location "ai-generative/client"
        
        # Create vercel.json
        $vercelConfig = @{
            version = 2
            builds = @(
                @{
                    src = "package.json"
                    use = "@vercel/static-build"
                    config = @{ distDir = "build" }
                }
            )
            routes = @(
                @{
                    src = "/static/(.*)"
                    headers = @{ "cache-control" = "s-maxage=31536000,immutable" }
                }
                @{
                    src = "/(.*)"
                    dest = "/index.html"
                }
            )
        }
        
        $vercelConfig | ConvertTo-Json -Depth 10 | Out-File "vercel.json" -Encoding UTF8
        
        Write-Host "Created vercel.json configuration" -ForegroundColor Green
        Write-Host "Next steps:" -ForegroundColor Cyan
        Write-Host "1. vercel login" -ForegroundColor White
        Write-Host "2. vercel" -ForegroundColor White
        Write-Host "3. vercel env add REACT_APP_API_URL" -ForegroundColor White
        Write-Host "4. vercel --prod" -ForegroundColor White
        
        Set-Location "../.."
    }
    
    "netlify" {
        Write-Host "`nPreparing Netlify deployment..." -ForegroundColor Green
        
        Set-Location "ai-generative/client"
        
        # Create _redirects file for SPA routing
        "/*    /index.html   200" | Out-File -FilePath "public/_redirects" -Encoding ASCII
        
        # Create netlify.toml
        $netlifyConfig = @"
[build]
  publish = "build"
  command = "npm run build"

[build.environment]
  NODE_VERSION = "18"

[[redirects]]
  from = "/*"
  to = "/index.html"
  status = 200
"@
        
        $netlifyConfig | Out-File -FilePath "netlify.toml" -Encoding UTF8
        
        Write-Host "Created Netlify configuration" -ForegroundColor Green
        Write-Host "Next steps:" -ForegroundColor Cyan
        Write-Host "1. npm run build" -ForegroundColor White
        Write-Host "2. Go to netlify.com" -ForegroundColor White
        Write-Host "3. Drag and drop the 'build' folder" -ForegroundColor White
        Write-Host "4. Set environment variable REACT_APP_API_URL" -ForegroundColor White
        
        Set-Location "../.."
    }
    
    "aws" {
        Write-Host "`nPreparing AWS deployment..." -ForegroundColor Green
        Write-Host "AWS deployment requires manual setup." -ForegroundColor Yellow
        Write-Host "Please refer to deploy-aws.md for detailed instructions." -ForegroundColor White
        Write-Host "Key services:" -ForegroundColor Cyan
        Write-Host "- S3 + CloudFront for frontend" -ForegroundColor White
        Write-Host "- Elastic Beanstalk for backend" -ForegroundColor White
        Write-Host "- Route 53 for domain" -ForegroundColor White
        Write-Host "- Certificate Manager for SSL" -ForegroundColor White
    }
    
    "digitalocean" {
        Write-Host "`nPreparing DigitalOcean deployment..." -ForegroundColor Green
        Write-Host "DigitalOcean deployment options:" -ForegroundColor Yellow
        Write-Host "Option 1: App Platform (Easiest)" -ForegroundColor Green
        Write-Host "  1. Push code to GitHub" -ForegroundColor White
        Write-Host "  2. Connect repository in DigitalOcean App Platform" -ForegroundColor White
        Write-Host "  3. Configure build settings" -ForegroundColor White
        Write-Host "Option 2: Docker Droplet (More control)" -ForegroundColor Green
        Write-Host "  1. Create Ubuntu droplet" -ForegroundColor White
        Write-Host "  2. Use docker-compose.yml for deployment" -ForegroundColor White
        Write-Host "  3. Set up SSL with Let's Encrypt" -ForegroundColor White
        Write-Host "`nRefer to deploy-digitalocean.md for detailed steps." -ForegroundColor Cyan
    }
}

Write-Host "`nDeployment preparation complete!" -ForegroundColor Green
Write-Host "Check the respective deployment guide for detailed instructions." -ForegroundColor Yellow
