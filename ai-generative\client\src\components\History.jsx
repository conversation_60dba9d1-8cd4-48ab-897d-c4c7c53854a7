import { useState, useEffect } from 'react';
import './History.css';

const History = ({ user, onNavigate }) => {
  const [history, setHistory] = useState([]);
  const [loading, setLoading] = useState(false);
  const [filter, setFilter] = useState('all');
  const [sortBy, setSortBy] = useState('newest');

  // Mock history data for demonstration (in real app, this would come from API)
  const mockHistory = [
    {
      id: 1,
      contentType: 'blog',
      tone: 'professional',
      length: 300,
      topic: 'AI in Healthcare',
      content: 'Artificial Intelligence is revolutionizing healthcare by enabling faster diagnoses, personalized treatments, and improved patient outcomes. From medical imaging to drug discovery, AI technologies are transforming how we approach medicine...',
      createdAt: new Date('2024-01-15T10:30:00'),
      wordCount: 287
    },
    {
      id: 2,
      contentType: 'social',
      tone: 'casual',
      length: 100,
      topic: 'Morning Motivation',
      content: 'Good morning, creators! ☀️ Today is another chance to turn your ideas into reality. Remember, every expert was once a beginner. What amazing thing will you create today? #MondayMotivation #CreateDaily',
      createdAt: new Date('2024-01-14T08:15:00'),
      wordCount: 34
    },
    {
      id: 3,
      contentType: 'ad',
      tone: 'friendly',
      length: 150,
      topic: 'Eco-Friendly Products',
      content: 'Make a difference with every purchase! 🌱 Our eco-friendly products help you live sustainably without compromising on quality. Join thousands of happy customers who are making the planet greener, one choice at a time. Shop now and get 20% off your first order!',
      createdAt: new Date('2024-01-13T16:45:00'),
      wordCount: 48
    },
    {
      id: 4,
      contentType: 'blog',
      tone: 'casual',
      length: 500,
      topic: 'Remote Work Tips',
      content: 'Working from home can be amazing, but it definitely comes with its challenges! Here are some game-changing tips that have helped me stay productive and sane while working remotely...',
      createdAt: new Date('2024-01-12T14:20:00'),
      wordCount: 456
    }
  ];

  useEffect(() => {
    // Check for user in localStorage if prop is null
    let currentUser = user;
    if (!currentUser) {
      const storedUserData = localStorage.getItem('userData');
      if (storedUserData) {
        try {
          currentUser = JSON.parse(storedUserData);
          console.log('History: Found user in localStorage:', currentUser);
        } catch (error) {
          console.error('Error parsing stored user data:', error);
        }
      }
    }

    console.log('History component - final user:', currentUser);

    // Simulate loading
    setLoading(true);
    setTimeout(() => {
      if (currentUser && currentUser.username) {
        console.log('Setting mock history for user:', currentUser.username);
        setHistory(mockHistory);
      } else {
        console.log('No user found, setting empty history');
        setHistory([]);
      }
      setLoading(false);
    }, 500);
  }, [user]);

  const getFilteredAndSortedHistory = () => {
    let filtered = history;

    if (filter !== 'all') {
      filtered = history.filter(item => item.contentType === filter);
    }

    return filtered.sort((a, b) => {
      if (sortBy === 'newest') {
        return new Date(b.createdAt) - new Date(a.createdAt);
      } else if (sortBy === 'oldest') {
        return new Date(a.createdAt) - new Date(b.createdAt);
      } else if (sortBy === 'longest') {
        return b.wordCount - a.wordCount;
      } else if (sortBy === 'shortest') {
        return a.wordCount - b.wordCount;
      }
      return 0;
    });
  };

  const formatDate = (date) => {
    return new Date(date).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  const getContentTypeIcon = (type) => {
    switch (type) {
      case 'blog': return '📝';
      case 'social': return '📱';
      case 'ad': return '📢';
      default: return '📄';
    }
  };

  const getToneColor = (tone) => {
    switch (tone) {
      case 'professional': return '#667eea';
      case 'casual': return '#ff6b6b';
      case 'friendly': return '#10b981';
      default: return '#64748b';
    }
  };

  const copyToClipboard = async (content) => {
    try {
      await navigator.clipboard.writeText(content);
      // You could add a toast notification here
    } catch (err) {
      console.error('Failed to copy text: ', err);
    }
  };

  // Check user from prop or localStorage
  const getCurrentUser = () => {
    if (user && user.username) return user;

    const storedUserData = localStorage.getItem('userData');
    if (storedUserData) {
      try {
        const parsedUser = JSON.parse(storedUserData);
        if (parsedUser && parsedUser.username) return parsedUser;
      } catch (error) {
        console.error('Error parsing stored user data:', error);
      }
    }
    return null;
  };

  const currentUser = getCurrentUser();

  if (!currentUser) {
    console.log('History: No user found, showing demo with sign-up prompt');
    return (
      <div className="history-container">
        <div className="demo-banner">
          <div className="banner-content">
            <h3 className="banner-title">
              <span className="banner-icon">🎯</span>
              Preview Mode - Sign Up to Save Your Content!
            </h3>
            <p className="banner-subtitle">
              Create an account to automatically save and manage all your generated content
            </p>
            <button
              className="banner-cta"
              onClick={() => {
                console.log('Create account button clicked');
                if (onNavigate) {
                  onNavigate('auth');
                } else {
                  console.error('onNavigate function not available');
                }
              }}
            >
              <span className="cta-icon">�</span>
              Create Free Account
            </button>
          </div>
        </div>

        <div className="demo-content">
          <h4 className="demo-title">
            <span className="demo-icon">👀</span>
            Here's what your history will look like:
          </h4>

          <div className="history-grid">
            {mockHistory.slice(0, 2).map((item) => (
              <div key={item.id} className="history-card demo-card">
                <div className="demo-overlay">
                  <div className="demo-lock">
                    <span className="lock-icon">🔒</span>
                    <span className="lock-text">Sign up to unlock</span>
                  </div>
                </div>

                <div className="card-header">
                  <div className="card-type">
                    <span className="type-icon">{getContentTypeIcon(item.contentType)}</span>
                    <span className="type-text">{item.contentType}</span>
                  </div>
                </div>

                <div className="card-content">
                  <h4 className="content-topic">{item.topic}</h4>
                  <p className="content-preview">
                    {item.content.length > 100
                      ? `${item.content.substring(0, 100)}...`
                      : item.content
                    }
                  </p>
                </div>

                <div className="card-meta">
                  <div className="meta-tags">
                    <span
                      className="tone-tag"
                      style={{ backgroundColor: getToneColor(item.tone) }}
                    >
                      {item.tone}
                    </span>
                    <span className="word-count">
                      {item.wordCount} words
                    </span>
                  </div>
                  <div className="meta-date">
                    {formatDate(item.createdAt)}
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>
      </div>
    );
  }

  if (loading) {
    return (
      <div className="history-container">
        <div className="history-loading">
          <div className="loading-spinner"></div>
          <p className="loading-text">Loading your creative history...</p>
          <div className="loading-emojis">
            <span className="loading-emoji">📝</span>
            <span className="loading-emoji">✨</span>
            <span className="loading-emoji">📚</span>
          </div>
        </div>
      </div>
    );
  }

  const filteredHistory = getFilteredAndSortedHistory();

  return (
    <div className="history-container">
      <div className="history-header">
        <div className="history-stats">
          <div className="stat-item">
            <span className="stat-number">{history.length}</span>
            <span className="stat-label">Total Created</span>
          </div>
          <div className="stat-item">
            <span className="stat-number">{history.reduce((sum, item) => sum + item.wordCount, 0)}</span>
            <span className="stat-label">Words Written</span>
          </div>
          <div className="stat-item">
            <span className="stat-number">{new Set(history.map(item => item.contentType)).size}</span>
            <span className="stat-label">Content Types</span>
          </div>
        </div>

        <div className="history-controls">
          <div className="filter-group">
            <label className="control-label">
              <span className="label-icon">🎯</span>
              Filter by Type
            </label>
            <select
              value={filter}
              onChange={(e) => setFilter(e.target.value)}
              className="control-select"
            >
              <option value="all">All Content</option>
              <option value="blog">📝 Blog Posts</option>
              <option value="social">📱 Social Posts</option>
              <option value="ad">📢 Advertisements</option>
            </select>
          </div>

          <div className="filter-group">
            <label className="control-label">
              <span className="label-icon">📊</span>
              Sort by
            </label>
            <select
              value={sortBy}
              onChange={(e) => setSortBy(e.target.value)}
              className="control-select"
            >
              <option value="newest">Newest First</option>
              <option value="oldest">Oldest First</option>
              <option value="longest">Longest First</option>
              <option value="shortest">Shortest First</option>
            </select>
          </div>
        </div>
      </div>

      {filteredHistory.length === 0 ? (
        <div className="history-empty">
          <div className="empty-illustration">
            <span className="empty-emoji">📝</span>
          </div>
          <h3 className="empty-title">No Content Yet</h3>
          <p className="empty-subtitle">
            Start creating amazing content to see your history here!
          </p>
        </div>
      ) : (
        <div className="history-grid">
          {filteredHistory.map((item) => (
            <div key={item.id} className="history-card">
              <div className="card-header">
                <div className="card-type">
                  <span className="type-icon">{getContentTypeIcon(item.contentType)}</span>
                  <span className="type-text">{item.contentType}</span>
                </div>
                <div className="card-actions">
                  <button
                    className="action-btn copy-btn"
                    onClick={() => copyToClipboard(item.content)}
                    title="Copy content"
                  >
                    📋
                  </button>
                </div>
              </div>

              <div className="card-content">
                <h4 className="content-topic">{item.topic}</h4>
                <p className="content-preview">
                  {item.content.length > 150
                    ? `${item.content.substring(0, 150)}...`
                    : item.content
                  }
                </p>
              </div>

              <div className="card-meta">
                <div className="meta-tags">
                  <span
                    className="tone-tag"
                    style={{ backgroundColor: getToneColor(item.tone) }}
                  >
                    {item.tone}
                  </span>
                  <span className="word-count">
                    {item.wordCount} words
                  </span>
                </div>
                <div className="meta-date">
                  {formatDate(item.createdAt)}
                </div>
              </div>
            </div>
          ))}
        </div>
      )}
    </div>
  );
};

export default History;