{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\vedika\\\\ai-generative\\\\client\\\\src\\\\components\\\\BlogGenerator.jsx\",\n  _s = $RefreshSig$();\nimport React, { useState } from 'react';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst BlogGenerator = () => {\n  _s();\n  const [tone, setTone] = useState('casual');\n  const [wordCount, setWordCount] = useState(600);\n  const [topic, setTopic] = useState('The Power of AI in Everyday Life');\n  const [blog, setBlog] = useState('');\n  const [loading, setLoading] = useState(false);\n  const [history, setHistory] = useState([]);\n  const [adProduct, setAdProduct] = useState('SmartWater Bottle');\n  const [adAudience, setAdAudience] = useState('health-conscious professionals');\n  const [adTone, setAdTone] = useState('energetic');\n  const [ads, setAds] = useState([]);\n  const [adLoading, setAdLoading] = useState(false);\n  const [liTopic, setLiTopic] = useState('The Power of AI in Everyday Life');\n  const [liPosts, setLiPosts] = useState([]);\n  const [liLoading, setLiLoading] = useState(false);\n  const handleGenerate = async e => {\n    e.preventDefault();\n    setLoading(true);\n    setBlog('');\n    try {\n      const response = await fetch('http://localhost:5000/api/generate', {\n        method: 'POST',\n        headers: {\n          'Content-Type': 'application/json'\n        },\n        body: JSON.stringify({\n          contentType: 'blog',\n          tone,\n          length: wordCount,\n          topic\n        })\n      });\n      const data = await response.json();\n      const generated = data.content || 'No content generated.';\n      setBlog(generated);\n      setHistory(prev => [{\n        tone,\n        wordCount,\n        topic,\n        blog: generated,\n        date: new Date().toLocaleString()\n      }, ...prev]);\n    } catch (error) {\n      setBlog('Error generating blog post.');\n    } finally {\n      setLoading(false);\n    }\n  };\n  const handleCopy = () => {\n    navigator.clipboard.writeText(blog);\n  };\n  const handleDownload = () => {\n    const element = document.createElement('a');\n    const file = new Blob([blog], {\n      type: 'text/plain'\n    });\n    element.href = URL.createObjectURL(file);\n    element.download = `${topic.replace(/\\s+/g, '_')}_blog.txt`;\n    document.body.appendChild(element);\n    element.click();\n    document.body.removeChild(element);\n  };\n  const handleAdGenerate = async e => {\n    e.preventDefault();\n    setAdLoading(true);\n    setAds([]);\n    try {\n      const response = await fetch('http://localhost:5000/api/generate-ad', {\n        method: 'POST',\n        headers: {\n          'Content-Type': 'application/json'\n        },\n        body: JSON.stringify({\n          product: adProduct,\n          audience: adAudience,\n          tone: adTone\n        })\n      });\n      const data = await response.json();\n      setAds(data.ads || []);\n    } catch (error) {\n      setAds(['Error generating ads.']);\n    } finally {\n      setAdLoading(false);\n    }\n  };\n  const handleLiGenerate = async e => {\n    e.preventDefault();\n    setLiLoading(true);\n    setLiPosts([]);\n    try {\n      const response = await fetch('http://localhost:5000/api/generate-linkedin', {\n        method: 'POST',\n        headers: {\n          'Content-Type': 'application/json'\n        },\n        body: JSON.stringify({\n          topic: liTopic\n        })\n      });\n      const data = await response.json();\n      setLiPosts(data.ideas || []);\n    } catch (error) {\n      setLiPosts([{\n        hook: 'Error generating LinkedIn posts.',\n        value: '',\n        hashtag: ''\n      }]);\n    } finally {\n      setLiLoading(false);\n    }\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n      children: \"AI Blog Generator\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 103,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"form\", {\n      onSubmit: handleGenerate,\n      children: [/*#__PURE__*/_jsxDEV(\"label\", {\n        children: [\"Tone:\", /*#__PURE__*/_jsxDEV(\"select\", {\n          value: tone,\n          onChange: e => setTone(e.target.value),\n          children: [/*#__PURE__*/_jsxDEV(\"option\", {\n            value: \"casual\",\n            children: \"Casual\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 108,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n            value: \"formal\",\n            children: \"Formal\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 109,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n            value: \"witty\",\n            children: \"Witty\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 110,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n            value: \"inspirational\",\n            children: \"Inspirational\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 111,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 107,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 105,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"br\", {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 114,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"label\", {\n        children: [\"Word Count:\", /*#__PURE__*/_jsxDEV(\"input\", {\n          type: \"number\",\n          value: wordCount,\n          onChange: e => setWordCount(e.target.value),\n          min: 300,\n          max: 2000\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 117,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 115,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"br\", {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 125,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"label\", {\n        children: [\"Topic:\", /*#__PURE__*/_jsxDEV(\"input\", {\n          type: \"text\",\n          value: topic,\n          onChange: e => setTopic(e.target.value),\n          placeholder: \"Enter blog topic...\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 128,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 126,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"br\", {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 135,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n        type: \"submit\",\n        disabled: loading,\n        children: loading ? 'Generating...' : 'Generate Blog'\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 136,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 104,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"hr\", {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 140,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n      children: \"Generated Blog Post:\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 141,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        whiteSpace: 'pre-line',\n        background: '#f9f9f9',\n        padding: '1em',\n        borderRadius: '6px'\n      },\n      children: blog\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 142,\n      columnNumber: 7\n    }, this), blog && /*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        marginTop: '1em'\n      },\n      children: [/*#__PURE__*/_jsxDEV(\"button\", {\n        onClick: handleCopy,\n        children: \"Copy to Clipboard\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 147,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n        onClick: handleDownload,\n        style: {\n          marginLeft: '1em'\n        },\n        children: \"Download as .txt\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 148,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 146,\n      columnNumber: 9\n    }, this), /*#__PURE__*/_jsxDEV(\"hr\", {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 151,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n      children: \"History\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 152,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"ul\", {\n      children: history.map((item, idx) => /*#__PURE__*/_jsxDEV(\"li\", {\n        style: {\n          marginBottom: '1em',\n          background: '#f1f1f1',\n          padding: '0.5em',\n          borderRadius: '4px'\n        },\n        children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n          children: item.topic\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 156,\n          columnNumber: 13\n        }, this), \" (\", item.tone, \", \", item.wordCount, \" words) \", /*#__PURE__*/_jsxDEV(\"em\", {\n          children: item.date\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 156,\n          columnNumber: 81\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            whiteSpace: 'pre-line',\n            marginTop: '0.5em'\n          },\n          children: item.blog\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 157,\n          columnNumber: 13\n        }, this)]\n      }, idx, true, {\n        fileName: _jsxFileName,\n        lineNumber: 155,\n        columnNumber: 11\n      }, this))\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 153,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"hr\", {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 161,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"h2\", {\n      children: \"Ad Generator\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 162,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"form\", {\n      onSubmit: handleAdGenerate,\n      children: [/*#__PURE__*/_jsxDEV(\"label\", {\n        children: [\"Product:\", /*#__PURE__*/_jsxDEV(\"input\", {\n          type: \"text\",\n          value: adProduct,\n          onChange: e => setAdProduct(e.target.value),\n          placeholder: \"Enter product name...\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 166,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 164,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"br\", {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 173,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"label\", {\n        children: [\"Audience:\", /*#__PURE__*/_jsxDEV(\"input\", {\n          type: \"text\",\n          value: adAudience,\n          onChange: e => setAdAudience(e.target.value),\n          placeholder: \"Enter target audience...\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 176,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 174,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"br\", {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 183,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"label\", {\n        children: [\"Tone:\", /*#__PURE__*/_jsxDEV(\"select\", {\n          value: adTone,\n          onChange: e => setAdTone(e.target.value),\n          children: [/*#__PURE__*/_jsxDEV(\"option\", {\n            value: \"energetic\",\n            children: \"Energetic\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 187,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n            value: \"trustworthy\",\n            children: \"Trustworthy\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 188,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n            value: \"playful\",\n            children: \"Playful\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 189,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n            value: \"luxurious\",\n            children: \"Luxurious\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 190,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 186,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 184,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"br\", {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 193,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n        type: \"submit\",\n        disabled: adLoading,\n        children: adLoading ? 'Generating...' : 'Generate Ads'\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 194,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 163,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n      children: \"Ad Variations:\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 198,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"ul\", {\n      children: ads.map((ad, idx) => /*#__PURE__*/_jsxDEV(\"li\", {\n        style: {\n          marginBottom: '1em',\n          background: '#e6f7ff',\n          padding: '0.5em',\n          borderRadius: '4px'\n        },\n        children: ad\n      }, idx, false, {\n        fileName: _jsxFileName,\n        lineNumber: 201,\n        columnNumber: 11\n      }, this))\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 199,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"hr\", {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 204,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"h2\", {\n      children: \"LinkedIn Post Ideas Generator\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 205,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"form\", {\n      onSubmit: handleLiGenerate,\n      children: [/*#__PURE__*/_jsxDEV(\"label\", {\n        children: [\"Topic:\", /*#__PURE__*/_jsxDEV(\"input\", {\n          type: \"text\",\n          value: liTopic,\n          onChange: e => setLiTopic(e.target.value),\n          placeholder: \"Enter LinkedIn post topic...\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 209,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 207,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"br\", {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 216,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n        type: \"submit\",\n        disabled: liLoading,\n        children: liLoading ? 'Generating...' : 'Generate LinkedIn Posts'\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 217,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 206,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n      children: \"Post Ideas:\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 221,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"ul\", {\n      children: liPosts.map((post, idx) => /*#__PURE__*/_jsxDEV(\"li\", {\n        style: {\n          marginBottom: '1em',\n          background: '#fffbe6',\n          padding: '0.5em',\n          borderRadius: '4px'\n        },\n        children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n          children: post.hook\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 225,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"br\", {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 225,\n          columnNumber: 41\n        }, this), post.value, /*#__PURE__*/_jsxDEV(\"br\", {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 226,\n          columnNumber: 25\n        }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n          style: {\n            color: '#888'\n          },\n          children: post.hashtag\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 227,\n          columnNumber: 13\n        }, this)]\n      }, idx, true, {\n        fileName: _jsxFileName,\n        lineNumber: 224,\n        columnNumber: 11\n      }, this))\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 222,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 102,\n    columnNumber: 5\n  }, this);\n};\n_s(BlogGenerator, \"0wKBuVOK+8965Xp2FUxPcpCWYL4=\");\n_c = BlogGenerator;\nexport default BlogGenerator;\nvar _c;\n$RefreshReg$(_c, \"BlogGenerator\");", "map": {"version": 3, "names": ["React", "useState", "jsxDEV", "_jsxDEV", "BlogGenerator", "_s", "tone", "setTone", "wordCount", "setWordCount", "topic", "setTopic", "blog", "setBlog", "loading", "setLoading", "history", "setHistory", "adProduct", "setAdProduct", "adAudience", "setAdAudience", "adTone", "setAdTone", "ads", "setAds", "adLoading", "setAdLoading", "liTopic", "setLiTopic", "liPosts", "setLiPosts", "li<PERSON><PERSON><PERSON>", "setLiLoading", "handleGenerate", "e", "preventDefault", "response", "fetch", "method", "headers", "body", "JSON", "stringify", "contentType", "length", "data", "json", "generated", "content", "prev", "date", "Date", "toLocaleString", "error", "handleCopy", "navigator", "clipboard", "writeText", "handleDownload", "element", "document", "createElement", "file", "Blob", "type", "href", "URL", "createObjectURL", "download", "replace", "append<PERSON><PERSON><PERSON>", "click", "<PERSON><PERSON><PERSON><PERSON>", "handleAdGenerate", "product", "audience", "handleLiGenerate", "ideas", "hook", "value", "hashtag", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "onSubmit", "onChange", "target", "min", "max", "placeholder", "disabled", "style", "whiteSpace", "background", "padding", "borderRadius", "marginTop", "onClick", "marginLeft", "map", "item", "idx", "marginBottom", "ad", "post", "color", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/vedika/ai-generative/client/src/components/BlogGenerator.jsx"], "sourcesContent": ["import React, { useState } from 'react';\r\n\r\nconst BlogGenerator = () => {\r\n  const [tone, setTone] = useState('casual');\r\n  const [wordCount, setWordCount] = useState(600);\r\n  const [topic, setTopic] = useState('The Power of AI in Everyday Life');\r\n  const [blog, setBlog] = useState('');\r\n  const [loading, setLoading] = useState(false);\r\n  const [history, setHistory] = useState([]);\r\n  const [adProduct, setAdProduct] = useState('SmartWater Bottle');\r\n  const [adAudience, setAdAudience] = useState('health-conscious professionals');\r\n  const [adTone, setAdTone] = useState('energetic');\r\n  const [ads, setAds] = useState([]);\r\n  const [adLoading, setAdLoading] = useState(false);\r\n  const [liTopic, setLiTopic] = useState('The Power of AI in Everyday Life');\r\n  const [liPosts, setLiPosts] = useState([]);\r\n  const [liLoading, setLiLoading] = useState(false);\r\n\r\n  const handleGenerate = async (e) => {\r\n    e.preventDefault();\r\n    setLoading(true);\r\n    setBlog('');\r\n    try {\r\n      const response = await fetch('http://localhost:5000/api/generate', {\r\n        method: 'POST',\r\n        headers: { 'Content-Type': 'application/json' },\r\n        body: JSON.stringify({\r\n          contentType: 'blog',\r\n          tone,\r\n          length: wordCount,\r\n          topic\r\n        })\r\n      });\r\n      const data = await response.json();\r\n      const generated = data.content || 'No content generated.';\r\n      setBlog(generated);\r\n      setHistory(prev => [{ tone, wordCount, topic, blog: generated, date: new Date().toLocaleString() }, ...prev]);\r\n    } catch (error) {\r\n      setBlog('Error generating blog post.');\r\n    } finally {\r\n      setLoading(false);\r\n    }\r\n  };\r\n\r\n  const handleCopy = () => {\r\n    navigator.clipboard.writeText(blog);\r\n  };\r\n\r\n  const handleDownload = () => {\r\n    const element = document.createElement('a');\r\n    const file = new Blob([blog], { type: 'text/plain' });\r\n    element.href = URL.createObjectURL(file);\r\n    element.download = `${topic.replace(/\\s+/g, '_')}_blog.txt`;\r\n    document.body.appendChild(element);\r\n    element.click();\r\n    document.body.removeChild(element);\r\n  };\r\n\r\n  const handleAdGenerate = async (e) => {\r\n    e.preventDefault();\r\n    setAdLoading(true);\r\n    setAds([]);\r\n    try {\r\n      const response = await fetch('http://localhost:5000/api/generate-ad', {\r\n        method: 'POST',\r\n        headers: { 'Content-Type': 'application/json' },\r\n        body: JSON.stringify({\r\n          product: adProduct,\r\n          audience: adAudience,\r\n          tone: adTone\r\n        })\r\n      });\r\n      const data = await response.json();\r\n      setAds(data.ads || []);\r\n    } catch (error) {\r\n      setAds(['Error generating ads.']);\r\n    } finally {\r\n      setAdLoading(false);\r\n    }\r\n  };\r\n\r\n  const handleLiGenerate = async (e) => {\r\n    e.preventDefault();\r\n    setLiLoading(true);\r\n    setLiPosts([]);\r\n    try {\r\n      const response = await fetch('http://localhost:5000/api/generate-linkedin', {\r\n        method: 'POST',\r\n        headers: { 'Content-Type': 'application/json' },\r\n        body: JSON.stringify({ topic: liTopic })\r\n      });\r\n      const data = await response.json();\r\n      setLiPosts(data.ideas || []);\r\n    } catch (error) {\r\n      setLiPosts([{ hook: 'Error generating LinkedIn posts.', value: '', hashtag: '' }]);\r\n    } finally {\r\n      setLiLoading(false);\r\n    }\r\n  };\r\n\r\n  return (\r\n    <div>\r\n      <h2>AI Blog Generator</h2>\r\n      <form onSubmit={handleGenerate}>\r\n        <label>\r\n          Tone:\r\n          <select value={tone} onChange={e => setTone(e.target.value)}>\r\n            <option value=\"casual\">Casual</option>\r\n            <option value=\"formal\">Formal</option>\r\n            <option value=\"witty\">Witty</option>\r\n            <option value=\"inspirational\">Inspirational</option>\r\n          </select>\r\n        </label>\r\n        <br />\r\n        <label>\r\n          Word Count:\r\n          <input\r\n            type=\"number\"\r\n            value={wordCount}\r\n            onChange={e => setWordCount(e.target.value)}\r\n            min={300}\r\n            max={2000}\r\n          />\r\n        </label>\r\n        <br />\r\n        <label>\r\n          Topic:\r\n          <input\r\n            type=\"text\"\r\n            value={topic}\r\n            onChange={e => setTopic(e.target.value)}\r\n            placeholder=\"Enter blog topic...\"\r\n          />\r\n        </label>\r\n        <br />\r\n        <button type=\"submit\" disabled={loading}>\r\n          {loading ? 'Generating...' : 'Generate Blog'}\r\n        </button>\r\n      </form>\r\n      <hr />\r\n      <h3>Generated Blog Post:</h3>\r\n      <div style={{ whiteSpace: 'pre-line', background: '#f9f9f9', padding: '1em', borderRadius: '6px' }}>\r\n        {blog}\r\n      </div>\r\n      {blog && (\r\n        <div style={{ marginTop: '1em' }}>\r\n          <button onClick={handleCopy}>Copy to Clipboard</button>\r\n          <button onClick={handleDownload} style={{ marginLeft: '1em' }}>Download as .txt</button>\r\n        </div>\r\n      )}\r\n      <hr />\r\n      <h3>History</h3>\r\n      <ul>\r\n        {history.map((item, idx) => (\r\n          <li key={idx} style={{ marginBottom: '1em', background: '#f1f1f1', padding: '0.5em', borderRadius: '4px' }}>\r\n            <strong>{item.topic}</strong> ({item.tone}, {item.wordCount} words) <em>{item.date}</em>\r\n            <div style={{ whiteSpace: 'pre-line', marginTop: '0.5em' }}>{item.blog}</div>\r\n          </li>\r\n        ))}\r\n      </ul>\r\n      <hr />\r\n      <h2>Ad Generator</h2>\r\n      <form onSubmit={handleAdGenerate}>\r\n        <label>\r\n          Product:\r\n          <input\r\n            type=\"text\"\r\n            value={adProduct}\r\n            onChange={e => setAdProduct(e.target.value)}\r\n            placeholder=\"Enter product name...\"\r\n          />\r\n        </label>\r\n        <br />\r\n        <label>\r\n          Audience:\r\n          <input\r\n            type=\"text\"\r\n            value={adAudience}\r\n            onChange={e => setAdAudience(e.target.value)}\r\n            placeholder=\"Enter target audience...\"\r\n          />\r\n        </label>\r\n        <br />\r\n        <label>\r\n          Tone:\r\n          <select value={adTone} onChange={e => setAdTone(e.target.value)}>\r\n            <option value=\"energetic\">Energetic</option>\r\n            <option value=\"trustworthy\">Trustworthy</option>\r\n            <option value=\"playful\">Playful</option>\r\n            <option value=\"luxurious\">Luxurious</option>\r\n          </select>\r\n        </label>\r\n        <br />\r\n        <button type=\"submit\" disabled={adLoading}>\r\n          {adLoading ? 'Generating...' : 'Generate Ads'}\r\n        </button>\r\n      </form>\r\n      <h3>Ad Variations:</h3>\r\n      <ul>\r\n        {ads.map((ad, idx) => (\r\n          <li key={idx} style={{ marginBottom: '1em', background: '#e6f7ff', padding: '0.5em', borderRadius: '4px' }}>{ad}</li>\r\n        ))}\r\n      </ul>\r\n      <hr />\r\n      <h2>LinkedIn Post Ideas Generator</h2>\r\n      <form onSubmit={handleLiGenerate}>\r\n        <label>\r\n          Topic:\r\n          <input\r\n            type=\"text\"\r\n            value={liTopic}\r\n            onChange={e => setLiTopic(e.target.value)}\r\n            placeholder=\"Enter LinkedIn post topic...\"\r\n          />\r\n        </label>\r\n        <br />\r\n        <button type=\"submit\" disabled={liLoading}>\r\n          {liLoading ? 'Generating...' : 'Generate LinkedIn Posts'}\r\n        </button>\r\n      </form>\r\n      <h3>Post Ideas:</h3>\r\n      <ul>\r\n        {liPosts.map((post, idx) => (\r\n          <li key={idx} style={{ marginBottom: '1em', background: '#fffbe6', padding: '0.5em', borderRadius: '4px' }}>\r\n            <strong>{post.hook}</strong><br />\r\n            {post.value}<br />\r\n            <span style={{ color: '#888' }}>{post.hashtag}</span>\r\n          </li>\r\n        ))}\r\n      </ul>\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default BlogGenerator;\r\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,QAAQ,OAAO;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAExC,MAAMC,aAAa,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAC1B,MAAM,CAACC,IAAI,EAAEC,OAAO,CAAC,GAAGN,QAAQ,CAAC,QAAQ,CAAC;EAC1C,MAAM,CAACO,SAAS,EAAEC,YAAY,CAAC,GAAGR,QAAQ,CAAC,GAAG,CAAC;EAC/C,MAAM,CAACS,KAAK,EAAEC,QAAQ,CAAC,GAAGV,QAAQ,CAAC,kCAAkC,CAAC;EACtE,MAAM,CAACW,IAAI,EAAEC,OAAO,CAAC,GAAGZ,QAAQ,CAAC,EAAE,CAAC;EACpC,MAAM,CAACa,OAAO,EAAEC,UAAU,CAAC,GAAGd,QAAQ,CAAC,KAAK,CAAC;EAC7C,MAAM,CAACe,OAAO,EAAEC,UAAU,CAAC,GAAGhB,QAAQ,CAAC,EAAE,CAAC;EAC1C,MAAM,CAACiB,SAAS,EAAEC,YAAY,CAAC,GAAGlB,QAAQ,CAAC,mBAAmB,CAAC;EAC/D,MAAM,CAACmB,UAAU,EAAEC,aAAa,CAAC,GAAGpB,QAAQ,CAAC,gCAAgC,CAAC;EAC9E,MAAM,CAACqB,MAAM,EAAEC,SAAS,CAAC,GAAGtB,QAAQ,CAAC,WAAW,CAAC;EACjD,MAAM,CAACuB,GAAG,EAAEC,MAAM,CAAC,GAAGxB,QAAQ,CAAC,EAAE,CAAC;EAClC,MAAM,CAACyB,SAAS,EAAEC,YAAY,CAAC,GAAG1B,QAAQ,CAAC,KAAK,CAAC;EACjD,MAAM,CAAC2B,OAAO,EAAEC,UAAU,CAAC,GAAG5B,QAAQ,CAAC,kCAAkC,CAAC;EAC1E,MAAM,CAAC6B,OAAO,EAAEC,UAAU,CAAC,GAAG9B,QAAQ,CAAC,EAAE,CAAC;EAC1C,MAAM,CAAC+B,SAAS,EAAEC,YAAY,CAAC,GAAGhC,QAAQ,CAAC,KAAK,CAAC;EAEjD,MAAMiC,cAAc,GAAG,MAAOC,CAAC,IAAK;IAClCA,CAAC,CAACC,cAAc,CAAC,CAAC;IAClBrB,UAAU,CAAC,IAAI,CAAC;IAChBF,OAAO,CAAC,EAAE,CAAC;IACX,IAAI;MACF,MAAMwB,QAAQ,GAAG,MAAMC,KAAK,CAAC,oCAAoC,EAAE;QACjEC,MAAM,EAAE,MAAM;QACdC,OAAO,EAAE;UAAE,cAAc,EAAE;QAAmB,CAAC;QAC/CC,IAAI,EAAEC,IAAI,CAACC,SAAS,CAAC;UACnBC,WAAW,EAAE,MAAM;UACnBtC,IAAI;UACJuC,MAAM,EAAErC,SAAS;UACjBE;QACF,CAAC;MACH,CAAC,CAAC;MACF,MAAMoC,IAAI,GAAG,MAAMT,QAAQ,CAACU,IAAI,CAAC,CAAC;MAClC,MAAMC,SAAS,GAAGF,IAAI,CAACG,OAAO,IAAI,uBAAuB;MACzDpC,OAAO,CAACmC,SAAS,CAAC;MAClB/B,UAAU,CAACiC,IAAI,IAAI,CAAC;QAAE5C,IAAI;QAAEE,SAAS;QAAEE,KAAK;QAAEE,IAAI,EAAEoC,SAAS;QAAEG,IAAI,EAAE,IAAIC,IAAI,CAAC,CAAC,CAACC,cAAc,CAAC;MAAE,CAAC,EAAE,GAAGH,IAAI,CAAC,CAAC;IAC/G,CAAC,CAAC,OAAOI,KAAK,EAAE;MACdzC,OAAO,CAAC,6BAA6B,CAAC;IACxC,CAAC,SAAS;MACRE,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;EAED,MAAMwC,UAAU,GAAGA,CAAA,KAAM;IACvBC,SAAS,CAACC,SAAS,CAACC,SAAS,CAAC9C,IAAI,CAAC;EACrC,CAAC;EAED,MAAM+C,cAAc,GAAGA,CAAA,KAAM;IAC3B,MAAMC,OAAO,GAAGC,QAAQ,CAACC,aAAa,CAAC,GAAG,CAAC;IAC3C,MAAMC,IAAI,GAAG,IAAIC,IAAI,CAAC,CAACpD,IAAI,CAAC,EAAE;MAAEqD,IAAI,EAAE;IAAa,CAAC,CAAC;IACrDL,OAAO,CAACM,IAAI,GAAGC,GAAG,CAACC,eAAe,CAACL,IAAI,CAAC;IACxCH,OAAO,CAACS,QAAQ,GAAG,GAAG3D,KAAK,CAAC4D,OAAO,CAAC,MAAM,EAAE,GAAG,CAAC,WAAW;IAC3DT,QAAQ,CAACpB,IAAI,CAAC8B,WAAW,CAACX,OAAO,CAAC;IAClCA,OAAO,CAACY,KAAK,CAAC,CAAC;IACfX,QAAQ,CAACpB,IAAI,CAACgC,WAAW,CAACb,OAAO,CAAC;EACpC,CAAC;EAED,MAAMc,gBAAgB,GAAG,MAAOvC,CAAC,IAAK;IACpCA,CAAC,CAACC,cAAc,CAAC,CAAC;IAClBT,YAAY,CAAC,IAAI,CAAC;IAClBF,MAAM,CAAC,EAAE,CAAC;IACV,IAAI;MACF,MAAMY,QAAQ,GAAG,MAAMC,KAAK,CAAC,uCAAuC,EAAE;QACpEC,MAAM,EAAE,MAAM;QACdC,OAAO,EAAE;UAAE,cAAc,EAAE;QAAmB,CAAC;QAC/CC,IAAI,EAAEC,IAAI,CAACC,SAAS,CAAC;UACnBgC,OAAO,EAAEzD,SAAS;UAClB0D,QAAQ,EAAExD,UAAU;UACpBd,IAAI,EAAEgB;QACR,CAAC;MACH,CAAC,CAAC;MACF,MAAMwB,IAAI,GAAG,MAAMT,QAAQ,CAACU,IAAI,CAAC,CAAC;MAClCtB,MAAM,CAACqB,IAAI,CAACtB,GAAG,IAAI,EAAE,CAAC;IACxB,CAAC,CAAC,OAAO8B,KAAK,EAAE;MACd7B,MAAM,CAAC,CAAC,uBAAuB,CAAC,CAAC;IACnC,CAAC,SAAS;MACRE,YAAY,CAAC,KAAK,CAAC;IACrB;EACF,CAAC;EAED,MAAMkD,gBAAgB,GAAG,MAAO1C,CAAC,IAAK;IACpCA,CAAC,CAACC,cAAc,CAAC,CAAC;IAClBH,YAAY,CAAC,IAAI,CAAC;IAClBF,UAAU,CAAC,EAAE,CAAC;IACd,IAAI;MACF,MAAMM,QAAQ,GAAG,MAAMC,KAAK,CAAC,6CAA6C,EAAE;QAC1EC,MAAM,EAAE,MAAM;QACdC,OAAO,EAAE;UAAE,cAAc,EAAE;QAAmB,CAAC;QAC/CC,IAAI,EAAEC,IAAI,CAACC,SAAS,CAAC;UAAEjC,KAAK,EAAEkB;QAAQ,CAAC;MACzC,CAAC,CAAC;MACF,MAAMkB,IAAI,GAAG,MAAMT,QAAQ,CAACU,IAAI,CAAC,CAAC;MAClChB,UAAU,CAACe,IAAI,CAACgC,KAAK,IAAI,EAAE,CAAC;IAC9B,CAAC,CAAC,OAAOxB,KAAK,EAAE;MACdvB,UAAU,CAAC,CAAC;QAAEgD,IAAI,EAAE,kCAAkC;QAAEC,KAAK,EAAE,EAAE;QAAEC,OAAO,EAAE;MAAG,CAAC,CAAC,CAAC;IACpF,CAAC,SAAS;MACRhD,YAAY,CAAC,KAAK,CAAC;IACrB;EACF,CAAC;EAED,oBACE9B,OAAA;IAAA+E,QAAA,gBACE/E,OAAA;MAAA+E,QAAA,EAAI;IAAiB;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAI,CAAC,eAC1BnF,OAAA;MAAMoF,QAAQ,EAAErD,cAAe;MAAAgD,QAAA,gBAC7B/E,OAAA;QAAA+E,QAAA,GAAO,OAEL,eAAA/E,OAAA;UAAQ6E,KAAK,EAAE1E,IAAK;UAACkF,QAAQ,EAAErD,CAAC,IAAI5B,OAAO,CAAC4B,CAAC,CAACsD,MAAM,CAACT,KAAK,CAAE;UAAAE,QAAA,gBAC1D/E,OAAA;YAAQ6E,KAAK,EAAC,QAAQ;YAAAE,QAAA,EAAC;UAAM;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eACtCnF,OAAA;YAAQ6E,KAAK,EAAC,QAAQ;YAAAE,QAAA,EAAC;UAAM;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eACtCnF,OAAA;YAAQ6E,KAAK,EAAC,OAAO;YAAAE,QAAA,EAAC;UAAK;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eACpCnF,OAAA;YAAQ6E,KAAK,EAAC,eAAe;YAAAE,QAAA,EAAC;UAAa;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC9C,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC,eACRnF,OAAA;QAAAgF,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAK,CAAC,eACNnF,OAAA;QAAA+E,QAAA,GAAO,aAEL,eAAA/E,OAAA;UACE8D,IAAI,EAAC,QAAQ;UACbe,KAAK,EAAExE,SAAU;UACjBgF,QAAQ,EAAErD,CAAC,IAAI1B,YAAY,CAAC0B,CAAC,CAACsD,MAAM,CAACT,KAAK,CAAE;UAC5CU,GAAG,EAAE,GAAI;UACTC,GAAG,EAAE;QAAK;UAAAR,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACX,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACG,CAAC,eACRnF,OAAA;QAAAgF,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAK,CAAC,eACNnF,OAAA;QAAA+E,QAAA,GAAO,QAEL,eAAA/E,OAAA;UACE8D,IAAI,EAAC,MAAM;UACXe,KAAK,EAAEtE,KAAM;UACb8E,QAAQ,EAAErD,CAAC,IAAIxB,QAAQ,CAACwB,CAAC,CAACsD,MAAM,CAACT,KAAK,CAAE;UACxCY,WAAW,EAAC;QAAqB;UAAAT,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAClC,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACG,CAAC,eACRnF,OAAA;QAAAgF,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAK,CAAC,eACNnF,OAAA;QAAQ8D,IAAI,EAAC,QAAQ;QAAC4B,QAAQ,EAAE/E,OAAQ;QAAAoE,QAAA,EACrCpE,OAAO,GAAG,eAAe,GAAG;MAAe;QAAAqE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACtC,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACL,CAAC,eACPnF,OAAA;MAAAgF,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAK,CAAC,eACNnF,OAAA;MAAA+E,QAAA,EAAI;IAAoB;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAI,CAAC,eAC7BnF,OAAA;MAAK2F,KAAK,EAAE;QAAEC,UAAU,EAAE,UAAU;QAAEC,UAAU,EAAE,SAAS;QAAEC,OAAO,EAAE,KAAK;QAAEC,YAAY,EAAE;MAAM,CAAE;MAAAhB,QAAA,EAChGtE;IAAI;MAAAuE,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACF,CAAC,EACL1E,IAAI,iBACHT,OAAA;MAAK2F,KAAK,EAAE;QAAEK,SAAS,EAAE;MAAM,CAAE;MAAAjB,QAAA,gBAC/B/E,OAAA;QAAQiG,OAAO,EAAE7C,UAAW;QAAA2B,QAAA,EAAC;MAAiB;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC,eACvDnF,OAAA;QAAQiG,OAAO,EAAEzC,cAAe;QAACmC,KAAK,EAAE;UAAEO,UAAU,EAAE;QAAM,CAAE;QAAAnB,QAAA,EAAC;MAAgB;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACrF,CACN,eACDnF,OAAA;MAAAgF,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAK,CAAC,eACNnF,OAAA;MAAA+E,QAAA,EAAI;IAAO;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAI,CAAC,eAChBnF,OAAA;MAAA+E,QAAA,EACGlE,OAAO,CAACsF,GAAG,CAAC,CAACC,IAAI,EAAEC,GAAG,kBACrBrG,OAAA;QAAc2F,KAAK,EAAE;UAAEW,YAAY,EAAE,KAAK;UAAET,UAAU,EAAE,SAAS;UAAEC,OAAO,EAAE,OAAO;UAAEC,YAAY,EAAE;QAAM,CAAE;QAAAhB,QAAA,gBACzG/E,OAAA;UAAA+E,QAAA,EAASqB,IAAI,CAAC7F;QAAK;UAAAyE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAS,CAAC,MAAE,EAACiB,IAAI,CAACjG,IAAI,EAAC,IAAE,EAACiG,IAAI,CAAC/F,SAAS,EAAC,UAAQ,eAAAL,OAAA;UAAA+E,QAAA,EAAKqB,IAAI,CAACpD;QAAI;UAAAgC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC,eACxFnF,OAAA;UAAK2F,KAAK,EAAE;YAAEC,UAAU,EAAE,UAAU;YAAEI,SAAS,EAAE;UAAQ,CAAE;UAAAjB,QAAA,EAAEqB,IAAI,CAAC3F;QAAI;UAAAuE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC;MAAA,GAFtEkB,GAAG;QAAArB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAGR,CACL;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACA,CAAC,eACLnF,OAAA;MAAAgF,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAK,CAAC,eACNnF,OAAA;MAAA+E,QAAA,EAAI;IAAY;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAI,CAAC,eACrBnF,OAAA;MAAMoF,QAAQ,EAAEb,gBAAiB;MAAAQ,QAAA,gBAC/B/E,OAAA;QAAA+E,QAAA,GAAO,UAEL,eAAA/E,OAAA;UACE8D,IAAI,EAAC,MAAM;UACXe,KAAK,EAAE9D,SAAU;UACjBsE,QAAQ,EAAErD,CAAC,IAAIhB,YAAY,CAACgB,CAAC,CAACsD,MAAM,CAACT,KAAK,CAAE;UAC5CY,WAAW,EAAC;QAAuB;UAAAT,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACpC,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACG,CAAC,eACRnF,OAAA;QAAAgF,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAK,CAAC,eACNnF,OAAA;QAAA+E,QAAA,GAAO,WAEL,eAAA/E,OAAA;UACE8D,IAAI,EAAC,MAAM;UACXe,KAAK,EAAE5D,UAAW;UAClBoE,QAAQ,EAAErD,CAAC,IAAId,aAAa,CAACc,CAAC,CAACsD,MAAM,CAACT,KAAK,CAAE;UAC7CY,WAAW,EAAC;QAA0B;UAAAT,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACvC,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACG,CAAC,eACRnF,OAAA;QAAAgF,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAK,CAAC,eACNnF,OAAA;QAAA+E,QAAA,GAAO,OAEL,eAAA/E,OAAA;UAAQ6E,KAAK,EAAE1D,MAAO;UAACkE,QAAQ,EAAErD,CAAC,IAAIZ,SAAS,CAACY,CAAC,CAACsD,MAAM,CAACT,KAAK,CAAE;UAAAE,QAAA,gBAC9D/E,OAAA;YAAQ6E,KAAK,EAAC,WAAW;YAAAE,QAAA,EAAC;UAAS;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eAC5CnF,OAAA;YAAQ6E,KAAK,EAAC,aAAa;YAAAE,QAAA,EAAC;UAAW;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eAChDnF,OAAA;YAAQ6E,KAAK,EAAC,SAAS;YAAAE,QAAA,EAAC;UAAO;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eACxCnF,OAAA;YAAQ6E,KAAK,EAAC,WAAW;YAAAE,QAAA,EAAC;UAAS;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACtC,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC,eACRnF,OAAA;QAAAgF,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAK,CAAC,eACNnF,OAAA;QAAQ8D,IAAI,EAAC,QAAQ;QAAC4B,QAAQ,EAAEnE,SAAU;QAAAwD,QAAA,EACvCxD,SAAS,GAAG,eAAe,GAAG;MAAc;QAAAyD,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACvC,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACL,CAAC,eACPnF,OAAA;MAAA+E,QAAA,EAAI;IAAc;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAI,CAAC,eACvBnF,OAAA;MAAA+E,QAAA,EACG1D,GAAG,CAAC8E,GAAG,CAAC,CAACI,EAAE,EAAEF,GAAG,kBACfrG,OAAA;QAAc2F,KAAK,EAAE;UAAEW,YAAY,EAAE,KAAK;UAAET,UAAU,EAAE,SAAS;UAAEC,OAAO,EAAE,OAAO;UAAEC,YAAY,EAAE;QAAM,CAAE;QAAAhB,QAAA,EAAEwB;MAAE,GAAtGF,GAAG;QAAArB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAwG,CACrH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACA,CAAC,eACLnF,OAAA;MAAAgF,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAK,CAAC,eACNnF,OAAA;MAAA+E,QAAA,EAAI;IAA6B;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAI,CAAC,eACtCnF,OAAA;MAAMoF,QAAQ,EAAEV,gBAAiB;MAAAK,QAAA,gBAC/B/E,OAAA;QAAA+E,QAAA,GAAO,QAEL,eAAA/E,OAAA;UACE8D,IAAI,EAAC,MAAM;UACXe,KAAK,EAAEpD,OAAQ;UACf4D,QAAQ,EAAErD,CAAC,IAAIN,UAAU,CAACM,CAAC,CAACsD,MAAM,CAACT,KAAK,CAAE;UAC1CY,WAAW,EAAC;QAA8B;UAAAT,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC3C,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACG,CAAC,eACRnF,OAAA;QAAAgF,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAK,CAAC,eACNnF,OAAA;QAAQ8D,IAAI,EAAC,QAAQ;QAAC4B,QAAQ,EAAE7D,SAAU;QAAAkD,QAAA,EACvClD,SAAS,GAAG,eAAe,GAAG;MAAyB;QAAAmD,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAClD,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACL,CAAC,eACPnF,OAAA;MAAA+E,QAAA,EAAI;IAAW;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAI,CAAC,eACpBnF,OAAA;MAAA+E,QAAA,EACGpD,OAAO,CAACwE,GAAG,CAAC,CAACK,IAAI,EAAEH,GAAG,kBACrBrG,OAAA;QAAc2F,KAAK,EAAE;UAAEW,YAAY,EAAE,KAAK;UAAET,UAAU,EAAE,SAAS;UAAEC,OAAO,EAAE,OAAO;UAAEC,YAAY,EAAE;QAAM,CAAE;QAAAhB,QAAA,gBACzG/E,OAAA;UAAA+E,QAAA,EAASyB,IAAI,CAAC5B;QAAI;UAAAI,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAS,CAAC,eAAAnF,OAAA;UAAAgF,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC,EACjCqB,IAAI,CAAC3B,KAAK,eAAC7E,OAAA;UAAAgF,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC,eAClBnF,OAAA;UAAM2F,KAAK,EAAE;YAAEc,KAAK,EAAE;UAAO,CAAE;UAAA1B,QAAA,EAAEyB,IAAI,CAAC1B;QAAO;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CAAC;MAAA,GAH9CkB,GAAG;QAAArB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAIR,CACL;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACA,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACF,CAAC;AAEV,CAAC;AAACjF,EAAA,CAtOID,aAAa;AAAAyG,EAAA,GAAbzG,aAAa;AAwOnB,eAAeA,aAAa;AAAC,IAAAyG,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}