.auth-container {
  max-width: 600px;
  margin: 0 auto;
  padding: 2rem;
  position: relative;
}

.auth-form-wrapper {
  background: linear-gradient(145deg, rgba(255, 255, 255, 0.95), rgba(255, 255, 255, 0.85));
  backdrop-filter: blur(20px);
  border-radius: 30px;
  padding: 3rem;
  box-shadow:
    0 25px 80px rgba(0, 0, 0, 0.15),
    0 0 0 1px rgba(255, 255, 255, 0.3),
    inset 0 1px 0 rgba(255, 255, 255, 0.6);
  position: relative;
  overflow: hidden;
  border: 2px solid transparent;
  background-clip: padding-box;
}

.auth-form-wrapper::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(45deg, #ff6b6b, #feca57, #48cae4, #7209b7);
  background-size: 400% 400%;
  animation: gradientShift 8s ease infinite;
  opacity: 0.1;
  z-index: -1;
}

.auth-form-wrapper::after {
  content: '';
  position: absolute;
  top: -2px;
  left: -2px;
  right: -2px;
  bottom: -2px;
  background: linear-gradient(45deg, #667eea, #764ba2, #ff6b6b, #feca57);
  background-size: 400% 400%;
  animation: gradientShift 6s ease infinite;
  border-radius: 32px;
  z-index: -2;
}

.auth-header {
  text-align: center;
  margin-bottom: 3rem;
  position: relative;
}

.auth-illustration {
  position: relative;
  height: 200px;
  margin-bottom: 2rem;
  display: flex;
  align-items: center;
  justify-content: center;
}

.magic-circle {
  position: relative;
  width: 180px;
  height: 180px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.orbit {
  position: absolute;
  border-radius: 50%;
  border: 2px solid rgba(102, 126, 234, 0.2);
  display: flex;
  align-items: center;
  justify-content: center;
}

.orbit-1 {
  width: 180px;
  height: 180px;
  animation: rotate 20s linear infinite;
}

.orbit-2 {
  width: 140px;
  height: 140px;
  animation: rotate 15s linear infinite reverse;
}

.orbit-3 {
  width: 100px;
  height: 100px;
  animation: rotate 10s linear infinite;
}

.orbit-emoji {
  position: absolute;
  top: -15px;
  font-size: 2rem;
  filter: drop-shadow(0 0 20px rgba(102, 126, 234, 0.6));
  animation: counterRotate 20s linear infinite, glow 2s ease-in-out infinite alternate;
}

.orbit-2 .orbit-emoji {
  animation: counterRotate 15s linear infinite reverse, glow 2s ease-in-out infinite alternate;
}

.orbit-3 .orbit-emoji {
  animation: counterRotate 10s linear infinite, glow 2s ease-in-out infinite alternate;
}

.center-logo {
  position: relative;
  width: 80px;
  height: 80px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(145deg, #667eea, #764ba2);
  border-radius: 50%;
  box-shadow:
    0 0 40px rgba(102, 126, 234, 0.6),
    inset 0 2px 10px rgba(255, 255, 255, 0.3);
}

.logo-main {
  font-size: 3rem;
  filter: drop-shadow(0 0 10px rgba(255, 255, 255, 0.8));
  animation: pulse 3s ease-in-out infinite;
}

.energy-rings {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
}

.ring {
  position: absolute;
  border-radius: 50%;
  border: 1px solid rgba(255, 255, 255, 0.4);
  animation: energyPulse 2s ease-in-out infinite;
}

.ring-1 {
  width: 90px;
  height: 90px;
  top: -45px;
  left: -45px;
  animation-delay: 0s;
}

.ring-2 {
  width: 110px;
  height: 110px;
  top: -55px;
  left: -55px;
  animation-delay: 0.7s;
}

.ring-3 {
  width: 130px;
  height: 130px;
  top: -65px;
  left: -65px;
  animation-delay: 1.4s;
}

.title-container {
  position: relative;
  margin-bottom: 2rem;
}

.auth-title {
  font-size: 2.5rem;
  font-weight: 900;
  margin-bottom: 1.5rem;
  position: relative;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 1rem;
}

.title-text {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 50%, #ff6b6b 100%);
  background-size: 200% 200%;
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  animation: gradientShift 4s ease infinite;
  text-shadow: 0 0 30px rgba(102, 126, 234, 0.3);
  letter-spacing: -1px;
}

.title-effects {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.title-decoration {
  font-size: 2rem;
  animation: explosive 2s ease-in-out infinite;
  filter: drop-shadow(0 0 20px rgba(255, 107, 107, 0.8));
}

.sparkle-trail {
  display: flex;
  gap: 0.5rem;
}

.sparkle {
  font-size: 1.2rem;
  animation: sparkleTrail 3s ease-in-out infinite;
  filter: drop-shadow(0 0 10px rgba(255, 215, 0, 0.8));
}

.sparkle:nth-child(1) { animation-delay: 0s; }
.sparkle:nth-child(2) { animation-delay: 0.5s; }
.sparkle:nth-child(3) { animation-delay: 1s; }

.subtitle-container {
  position: relative;
}

.auth-subtitle {
  color: #1e293b;
  font-size: 1.2rem;
  font-weight: 600;
  line-height: 1.6;
  max-width: 500px;
  margin: 0 auto 2rem;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.impact-stats {
  display: flex;
  justify-content: center;
  gap: 1.5rem;
  margin-top: 1.5rem;
}

.stat-bubble {
  background: linear-gradient(145deg, rgba(255, 255, 255, 0.9), rgba(255, 255, 255, 0.7));
  backdrop-filter: blur(10px);
  border-radius: 20px;
  padding: 1rem 1.5rem;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 0.25rem;
  box-shadow:
    0 8px 25px rgba(0, 0, 0, 0.1),
    inset 0 1px 0 rgba(255, 255, 255, 0.6);
  border: 1px solid rgba(255, 255, 255, 0.3);
  animation: statFloat 4s ease-in-out infinite;
  position: relative;
  overflow: hidden;
}

.stat-bubble::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(102, 126, 234, 0.2), transparent);
  animation: shimmer 3s ease-in-out infinite;
}

.stat-bubble:nth-child(1) { animation-delay: 0s; }
.stat-bubble:nth-child(2) { animation-delay: 0.5s; }
.stat-bubble:nth-child(3) { animation-delay: 1s; }

.stat-number {
  font-size: 1.5rem;
  font-weight: 900;
  background: linear-gradient(135deg, #667eea, #764ba2);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.stat-label {
  font-size: 0.8rem;
  color: #64748b;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.auth-form {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
}

.form-group {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.form-label {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-weight: 600;
  color: #1e293b;
  font-size: 0.95rem;
}

.label-icon {
  font-size: 1.1rem;
  animation: wiggle 3s ease-in-out infinite;
}

.form-input {
  padding: 1rem 1.25rem;
  border: 2px solid #e2e8f0;
  border-radius: 12px;
  font-size: 1rem;
  transition: all 0.3s ease;
  background: white;
  color: #1e293b;
}

.form-input:focus {
  outline: none;
  border-color: #667eea;
  box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
  transform: translateY(-2px);
}

.form-input:disabled {
  background: #f8fafc;
  color: #94a3b8;
  cursor: not-allowed;
}

.form-input::placeholder {
  color: #94a3b8;
}

.message {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  padding: 1rem 1.25rem;
  border-radius: 12px;
  font-weight: 500;
  animation: slideIn 0.3s ease-out;
}

.message.success {
  background: linear-gradient(135deg, #10b981 0%, #059669 100%);
  color: white;
  position: relative;
  overflow: hidden;
  animation: successCelebration 0.6s ease-out, successPulse 2s ease-in-out infinite 0.6s;
}

.message.success::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.4), transparent);
  animation: successShimmer 1.5s ease-in-out infinite;
}

.message.success::after {
  content: '🎊';
  position: absolute;
  top: -10px;
  right: 10px;
  font-size: 1.5rem;
  animation: confetti 2s ease-in-out infinite;
}

.message.error {
  background: linear-gradient(135deg, #ef4444 0%, #dc2626 100%);
  color: white;
}

.message-icon {
  font-size: 1.2rem;
}

.mega-button {
  position: relative;
  padding: 2rem 3rem;
  background: linear-gradient(145deg, #667eea 0%, #764ba2 50%, #ff6b6b 100%);
  background-size: 300% 300%;
  border: none;
  border-radius: 25px;
  font-size: 1.3rem;
  font-weight: 900;
  cursor: pointer;
  overflow: hidden;
  box-shadow:
    0 15px 40px rgba(102, 126, 234, 0.4),
    0 0 0 1px rgba(255, 255, 255, 0.2),
    inset 0 2px 0 rgba(255, 255, 255, 0.3);
  animation: megaButtonPulse 3s ease-in-out infinite;
  transition: all 0.3s ease;
}

.button-bg-effect {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(45deg, transparent, rgba(255, 255, 255, 0.3), transparent);
  transform: translateX(-100%);
  transition: transform 0.6s ease;
}

.mega-button:hover:not(:disabled) .button-bg-effect {
  transform: translateX(100%);
}

.mega-button:hover:not(:disabled) {
  transform: translateY(-5px) scale(1.02);
  box-shadow:
    0 25px 60px rgba(102, 126, 234, 0.6),
    0 0 0 1px rgba(255, 255, 255, 0.3),
    inset 0 2px 0 rgba(255, 255, 255, 0.4);
  background-position: 100% 100%;
}

.mega-button:active {
  transform: translateY(-2px) scale(0.98);
}

.mega-button:disabled {
  opacity: 0.8;
  cursor: not-allowed;
  transform: none;
  animation: none;
}

.button-content {
  position: relative;
  z-index: 2;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 1rem;
  color: white;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

.mega-icon {
  font-size: 2rem;
  animation: iconPulse 2s ease-in-out infinite;
  filter: drop-shadow(0 0 15px rgba(255, 255, 255, 0.8));
}

.btn-text {
  font-size: 1.2rem;
  letter-spacing: 1px;
  text-transform: uppercase;
}

.button-particles {
  position: absolute;
  top: 50%;
  right: 20px;
  transform: translateY(-50%);
  display: flex;
  flex-direction: column;
  gap: 5px;
}

.btn-particle {
  font-size: 1rem;
  animation: particleFloat 2s ease-in-out infinite;
  filter: drop-shadow(0 0 10px rgba(255, 255, 255, 0.6));
}

.btn-particle:nth-child(1) { animation-delay: 0s; }
.btn-particle:nth-child(2) { animation-delay: 0.7s; }
.btn-particle:nth-child(3) { animation-delay: 1.4s; }

.loading-animation {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.loading-orb {
  width: 40px;
  height: 40px;
  background: linear-gradient(45deg, #fff, rgba(255, 255, 255, 0.7));
  border-radius: 50%;
  animation: orbPulse 1.5s ease-in-out infinite;
  box-shadow:
    0 0 20px rgba(255, 255, 255, 0.8),
    inset 0 2px 5px rgba(255, 255, 255, 0.3);
}

.loading-particles {
  display: flex;
  gap: 0.5rem;
}

.particle {
  font-size: 1.2rem;
  animation: particleSpin 2s linear infinite;
  filter: drop-shadow(0 0 10px rgba(255, 255, 255, 0.8));
}

.particle:nth-child(1) { animation-delay: 0s; }
.particle:nth-child(2) { animation-delay: 0.7s; }
.particle:nth-child(3) { animation-delay: 1.4s; }

.loading-text {
  font-size: 1.1rem;
  font-weight: 700;
  letter-spacing: 0.5px;
}

/* Power Features */
.power-features {
  margin-top: 2rem;
  padding: 2rem;
  background: linear-gradient(145deg, rgba(16, 185, 129, 0.1), rgba(5, 150, 105, 0.05));
  border-radius: 20px;
  border: 2px solid rgba(16, 185, 129, 0.2);
  position: relative;
  overflow: hidden;
}

.power-features::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(45deg, transparent, rgba(16, 185, 129, 0.1), transparent);
  animation: shimmer 4s ease-in-out infinite;
}

.features-title {
  text-align: center;
  font-size: 1.3rem;
  font-weight: 700;
  color: #059669;
  margin-bottom: 1.5rem;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
}

.features-icon {
  font-size: 1.5rem;
  animation: energyBurst 2s ease-in-out infinite;
}

.features-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 1rem;
}

.feature-item {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  padding: 1rem;
  background: rgba(255, 255, 255, 0.8);
  border-radius: 12px;
  border: 1px solid rgba(16, 185, 129, 0.2);
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.feature-item::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(16, 185, 129, 0.2), transparent);
  transition: left 0.5s ease;
}

.feature-item:hover::before {
  left: 100%;
}

.feature-item:hover {
  transform: translateY(-3px);
  box-shadow: 0 8px 25px rgba(16, 185, 129, 0.2);
  border-color: #10b981;
}

.feature-emoji {
  font-size: 1.5rem;
  animation: featureGlow 3s ease-in-out infinite;
}

.feature-text {
  font-weight: 600;
  color: #059669;
  font-size: 0.9rem;
}

.btn-icon {
  font-size: 1.2rem;
}

.spinner {
  width: 20px;
  height: 20px;
  border: 2px solid rgba(255, 255, 255, 0.3);
  border-top: 2px solid white;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

.auth-footer {
  margin-top: 3rem;
  text-align: center;
  position: relative;
}

.switch-container {
  margin-bottom: 2rem;
}

.auth-switch-text {
  color: #1e293b;
  margin-bottom: 1.5rem;
  font-size: 1.1rem;
  font-weight: 600;
}

.impact-switch {
  position: relative;
  padding: 1.5rem 2.5rem;
  background: linear-gradient(145deg, #ff6b6b 0%, #feca57 50%, #48cae4 100%);
  background-size: 300% 300%;
  border: none;
  border-radius: 20px;
  font-size: 1.1rem;
  font-weight: 800;
  cursor: pointer;
  overflow: hidden;
  box-shadow:
    0 10px 30px rgba(255, 107, 107, 0.4),
    0 0 0 1px rgba(255, 255, 255, 0.2),
    inset 0 2px 0 rgba(255, 255, 255, 0.3);
  animation: switchPulse 4s ease-in-out infinite;
  transition: all 0.3s ease;
}

.switch-bg-effect {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(45deg, transparent, rgba(255, 255, 255, 0.4), transparent);
  transform: translateX(-100%);
  transition: transform 0.6s ease;
}

.impact-switch:hover:not(:disabled) .switch-bg-effect {
  transform: translateX(100%);
}

.impact-switch:hover:not(:disabled) {
  transform: translateY(-3px) scale(1.05);
  box-shadow:
    0 15px 45px rgba(255, 107, 107, 0.6),
    0 0 0 1px rgba(255, 255, 255, 0.3),
    inset 0 2px 0 rgba(255, 255, 255, 0.4);
  background-position: 100% 100%;
}

.impact-switch:disabled {
  opacity: 0.7;
  cursor: not-allowed;
  transform: none;
  animation: none;
}

.switch-content {
  position: relative;
  z-index: 2;
  display: flex;
  align-items: center;
  gap: 0.75rem;
  color: white;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

.switch-icon {
  font-size: 1.5rem;
  animation: explosive 2s ease-in-out infinite;
  filter: drop-shadow(0 0 15px rgba(255, 255, 255, 0.8));
}

.switch-text {
  font-size: 1rem;
  letter-spacing: 0.5px;
  text-transform: uppercase;
}

.trust-indicators {
  display: flex;
  justify-content: center;
  gap: 2rem;
  margin-top: 2rem;
  padding: 1.5rem;
  background: linear-gradient(145deg, rgba(255, 255, 255, 0.8), rgba(255, 255, 255, 0.6));
  border-radius: 15px;
  border: 1px solid rgba(255, 255, 255, 0.3);
  backdrop-filter: blur(10px);
}

.trust-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 0.5rem;
  opacity: 0.8;
  transition: all 0.3s ease;
}

.trust-item:hover {
  opacity: 1;
  transform: translateY(-2px);
}

.trust-icon {
  font-size: 1.5rem;
  animation: trustGlow 3s ease-in-out infinite;
}

.trust-item:nth-child(1) .trust-icon { animation-delay: 0s; }
.trust-item:nth-child(2) .trust-icon { animation-delay: 1s; }
.trust-item:nth-child(3) .trust-icon { animation-delay: 2s; }

.trust-text {
  font-size: 0.8rem;
  font-weight: 600;
  color: #64748b;
  text-align: center;
}

@keyframes switchPulse {
  0%, 100% {
    background-position: 0% 50%;
    box-shadow:
      0 10px 30px rgba(255, 107, 107, 0.4),
      0 0 0 1px rgba(255, 255, 255, 0.2),
      inset 0 2px 0 rgba(255, 255, 255, 0.3);
  }
  50% {
    background-position: 100% 50%;
    box-shadow:
      0 15px 40px rgba(255, 107, 107, 0.6),
      0 0 0 1px rgba(255, 255, 255, 0.3),
      inset 0 2px 0 rgba(255, 255, 255, 0.4);
  }
}

@keyframes trustGlow {
  0%, 100% {
    filter: drop-shadow(0 0 5px rgba(102, 126, 234, 0.4));
  }
  50% {
    filter: drop-shadow(0 0 15px rgba(102, 126, 234, 0.8));
  }
}

@keyframes successCelebration {
  0% {
    transform: scale(0.8) translateY(20px);
    opacity: 0;
  }
  50% {
    transform: scale(1.05) translateY(-5px);
    opacity: 1;
  }
  100% {
    transform: scale(1) translateY(0);
    opacity: 1;
  }
}

@keyframes successPulse {
  0%, 100% {
    box-shadow: 0 0 20px rgba(16, 185, 129, 0.4);
  }
  50% {
    box-shadow: 0 0 40px rgba(16, 185, 129, 0.8);
  }
}

@keyframes successShimmer {
  0% {
    left: -100%;
  }
  100% {
    left: 100%;
  }
}

@keyframes confetti {
  0%, 100% {
    transform: rotate(0deg) scale(1);
  }
  25% {
    transform: rotate(10deg) scale(1.2);
  }
  50% {
    transform: rotate(-5deg) scale(1.1);
  }
  75% {
    transform: rotate(5deg) scale(1.15);
  }
}

/* User Profile Styles */
.user-profile {
  background: linear-gradient(145deg, rgba(255, 255, 255, 0.95), rgba(255, 255, 255, 0.85));
  backdrop-filter: blur(20px);
  border-radius: 30px;
  padding: 3rem;
  box-shadow:
    0 25px 80px rgba(0, 0, 0, 0.15),
    0 0 0 1px rgba(255, 255, 255, 0.3),
    inset 0 1px 0 rgba(255, 255, 255, 0.6);
  border: 2px solid transparent;
  background-clip: padding-box;
  text-align: center;
  position: relative;
  overflow: hidden;
}

.user-profile::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(45deg, #10b981, #059669, #667eea, #764ba2);
  background-size: 400% 400%;
  animation: gradientShift 8s ease infinite;
  opacity: 0.1;
  z-index: -1;
}

.profile-celebration {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  pointer-events: none;
  z-index: 1;
}

.celebration-emojis {
  position: relative;
  width: 100%;
  height: 100%;
}

.celebration-emoji {
  position: absolute;
  font-size: 2rem;
  animation: celebrationFloat 6s ease-in-out infinite;
  opacity: 0.7;
}

.celebration-emoji:nth-child(1) {
  top: 10%;
  left: 10%;
  animation-delay: 0s;
}

.celebration-emoji:nth-child(2) {
  top: 20%;
  right: 15%;
  animation-delay: 1.5s;
}

.celebration-emoji:nth-child(3) {
  bottom: 30%;
  left: 20%;
  animation-delay: 3s;
}

.celebration-emoji:nth-child(4) {
  bottom: 20%;
  right: 10%;
  animation-delay: 4.5s;
}

.profile-header {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 1.5rem;
  margin-bottom: 2rem;
  position: relative;
  z-index: 2;
}

.profile-avatar {
  width: 120px;
  height: 120px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow:
    0 15px 40px rgba(102, 126, 234, 0.4),
    0 0 0 4px rgba(255, 255, 255, 0.3),
    inset 0 2px 10px rgba(255, 255, 255, 0.2);
  position: relative;
  animation: avatarPulse 3s ease-in-out infinite;
}

.avatar-glow {
  position: absolute;
  top: -10px;
  left: -10px;
  right: -10px;
  bottom: -10px;
  background: linear-gradient(45deg, #667eea, #764ba2, #10b981, #feca57);
  background-size: 400% 400%;
  border-radius: 50%;
  opacity: 0.3;
  animation: gradientShift 4s ease infinite;
  z-index: -1;
}

.avatar-emoji {
  font-size: 3.5rem;
  filter: drop-shadow(0 0 15px rgba(255, 255, 255, 0.8));
  animation: avatarSpin 8s linear infinite;
}

.profile-info {
  text-align: center;
}

.profile-name {
  font-size: 1.5rem;
  font-weight: 700;
  color: #1e293b;
  margin-bottom: 0.5rem;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.welcome-emoji {
  animation: wave 2s ease-in-out infinite;
}

.profile-email {
  color: #64748b;
  font-size: 1rem;
  margin-bottom: 1rem;
}

.profile-badge {
  display: inline-flex;
  align-items: center;
  gap: 0.5rem;
  background: linear-gradient(135deg, #10b981 0%, #059669 100%);
  color: white;
  padding: 0.5rem 1rem;
  border-radius: 20px;
  font-size: 0.9rem;
  font-weight: 600;
  box-shadow: 0 4px 15px rgba(16, 185, 129, 0.3);
  animation: badgeGlow 2s ease-in-out infinite;
}

.badge-icon {
  font-size: 1rem;
  animation: badgeSpark 1.5s ease-in-out infinite;
}

.badge-text {
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.profile-stats {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 1rem;
  margin-bottom: 2rem;
}

.stat-card {
  background: white;
  border: 2px solid #e2e8f0;
  border-radius: 12px;
  padding: 1.5rem;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 0.75rem;
  transition: all 0.3s ease;
}

.stat-card:hover {
  border-color: #667eea;
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(102, 126, 234, 0.1);
}

.stat-icon {
  font-size: 2rem;
  animation: pulse 3s ease-in-out infinite;
}

.stat-content h4 {
  font-size: 0.9rem;
  font-weight: 600;
  color: #1e293b;
  margin-bottom: 0.25rem;
}

.stat-content p {
  font-size: 0.8rem;
  color: #64748b;
}

.profile-actions {
  display: flex;
  gap: 1rem;
  justify-content: center;
}

.profile-btn {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 1rem 1.5rem;
  border: none;
  border-radius: 12px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
}

.generate-btn {
  background: linear-gradient(135deg, #10b981 0%, #059669 100%);
  color: white;
}

.history-btn {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
}

.profile-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.2);
}

/* Responsive Design */
@media (max-width: 768px) {
  .auth-container {
    padding: 1rem;
  }
  
  .auth-form-wrapper,
  .user-profile {
    padding: 2rem;
  }
  
  .profile-stats {
    grid-template-columns: 1fr;
  }
  
  .profile-actions {
    flex-direction: column;
  }
}

/* Spectacular Animation Keyframes */
@keyframes gradientShift {
  0%, 100% {
    background-position: 0% 50%;
  }
  50% {
    background-position: 100% 50%;
  }
}

@keyframes rotate {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

@keyframes counterRotate {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(-360deg);
  }
}

@keyframes glow {
  0%, 100% {
    filter: drop-shadow(0 0 20px rgba(102, 126, 234, 0.6));
  }
  50% {
    filter: drop-shadow(0 0 30px rgba(255, 107, 107, 0.8));
  }
}

@keyframes pulse {
  0%, 100% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.1);
  }
}

@keyframes energyPulse {
  0% {
    opacity: 0;
    transform: scale(0.8);
  }
  50% {
    opacity: 1;
    transform: scale(1.2);
  }
  100% {
    opacity: 0;
    transform: scale(1.5);
  }
}

@keyframes explosive {
  0%, 100% {
    transform: scale(1) rotate(0deg);
  }
  25% {
    transform: scale(1.3) rotate(10deg);
  }
  50% {
    transform: scale(1.1) rotate(-5deg);
  }
  75% {
    transform: scale(1.2) rotate(5deg);
  }
}

@keyframes sparkleTrail {
  0%, 100% {
    opacity: 0.3;
    transform: scale(0.8) translateY(0px);
  }
  50% {
    opacity: 1;
    transform: scale(1.2) translateY(-10px);
  }
}

@keyframes statFloat {
  0%, 100% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-8px);
  }
}

@keyframes shimmer {
  0% {
    left: -100%;
  }
  100% {
    left: 100%;
  }
}

@keyframes megaButtonPulse {
  0%, 100% {
    box-shadow:
      0 15px 40px rgba(102, 126, 234, 0.4),
      0 0 0 1px rgba(255, 255, 255, 0.2),
      inset 0 2px 0 rgba(255, 255, 255, 0.3);
  }
  50% {
    box-shadow:
      0 20px 60px rgba(102, 126, 234, 0.6),
      0 0 0 1px rgba(255, 255, 255, 0.3),
      inset 0 2px 0 rgba(255, 255, 255, 0.4);
  }
}

@keyframes iconPulse {
  0%, 100% {
    transform: scale(1);
    filter: drop-shadow(0 0 15px rgba(255, 255, 255, 0.8));
  }
  50% {
    transform: scale(1.2);
    filter: drop-shadow(0 0 25px rgba(255, 255, 255, 1));
  }
}

@keyframes particleFloat {
  0%, 100% {
    transform: translateY(0px) rotate(0deg);
  }
  33% {
    transform: translateY(-15px) rotate(120deg);
  }
  66% {
    transform: translateY(-8px) rotate(240deg);
  }
}

@keyframes orbPulse {
  0%, 100% {
    transform: scale(1);
    box-shadow:
      0 0 20px rgba(255, 255, 255, 0.8),
      inset 0 2px 5px rgba(255, 255, 255, 0.3);
  }
  50% {
    transform: scale(1.2);
    box-shadow:
      0 0 40px rgba(255, 255, 255, 1),
      inset 0 2px 5px rgba(255, 255, 255, 0.5);
  }
}

@keyframes particleSpin {
  0% {
    transform: rotate(0deg) translateX(20px) rotate(0deg);
  }
  100% {
    transform: rotate(360deg) translateX(20px) rotate(-360deg);
  }
}

@keyframes energyBurst {
  0%, 100% {
    transform: scale(1);
    filter: drop-shadow(0 0 10px rgba(5, 150, 105, 0.6));
  }
  50% {
    transform: scale(1.3);
    filter: drop-shadow(0 0 20px rgba(5, 150, 105, 1));
  }
}

@keyframes featureGlow {
  0%, 100% {
    filter: drop-shadow(0 0 5px rgba(16, 185, 129, 0.4));
  }
  50% {
    filter: drop-shadow(0 0 15px rgba(16, 185, 129, 0.8));
  }
}

@keyframes impactShake {
  0%, 100% {
    transform: translateX(0);
  }
  25% {
    transform: translateX(-5px);
  }
  75% {
    transform: translateX(5px);
  }
}

@keyframes slideIn {
  from {
    opacity: 0;
    transform: translateY(-20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes wave {
  0%, 100% {
    transform: rotate(0deg);
  }
  25% {
    transform: rotate(20deg);
  }
  75% {
    transform: rotate(-10deg);
  }
}
