{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\vedika\\\\ai-generative\\\\client\\\\src\\\\components\\\\History.jsx\";\nimport React from 'react';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst History = () => /*#__PURE__*/_jsxDEV(\"div\", {\n  children: /*#__PURE__*/_jsxDEV(\"p\", {\n    children: \"(User's generated content history will appear here.)\"\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 5,\n    columnNumber: 5\n  }, this)\n}, void 0, false, {\n  fileName: _jsxFileName,\n  lineNumber: 4,\n  columnNumber: 3\n}, this);\n_c = History;\nexport default History;\nvar _c;\n$RefreshReg$(_c, \"History\");", "map": {"version": 3, "names": ["React", "jsxDEV", "_jsxDEV", "History", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/vedika/ai-generative/client/src/components/History.jsx"], "sourcesContent": ["import React from 'react';\r\n\r\nconst History = () => (\r\n  <div>\r\n    <p>(User's generated content history will appear here.)</p>\r\n  </div>\r\n);\r\n\r\nexport default History; "], "mappings": ";AAAA,OAAOA,KAAK,MAAM,OAAO;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE1B,MAAMC,OAAO,GAAGA,CAAA,kBACdD,OAAA;EAAAE,QAAA,eACEF,OAAA;IAAAE,QAAA,EAAG;EAAoD;IAAAC,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAAG;AAAC;EAAAH,QAAA,EAAAC,YAAA;EAAAC,UAAA;EAAAC,YAAA;AAAA,OACxD,CACN;AAACC,EAAA,GAJIN,OAAO;AAMb,eAAeA,OAAO;AAAC,IAAAM,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}