const express = require('express');
const cors = require('cors');
const OpenAI = require('openai');
require('dotenv').config();

const app = express();
const port = 5000; // Changed to match frontend expectations

// Initialize OpenAI client
const openai = new OpenAI({
  apiKey: process.env.OPENAI_API_KEY,
});

// Middleware
app.use(cors());
app.use(express.json());

// Content generation prompts for different types
const getPrompt = (contentType, tone, length, topic) => {
  const basePrompts = {
    blog: `Write a comprehensive blog post about "${topic}" in a ${tone} tone. The blog post should be approximately ${length} words long. Include an engaging introduction, well-structured body paragraphs with valuable insights, and a compelling conclusion. Make it informative and engaging for readers.`,

    'social post': `Create an engaging social media post about "${topic}" in a ${tone} tone. The post should be approximately ${length} words long. Make it shareable, include relevant hashtags if appropriate, and ensure it captures attention in a social media feed.`,

    ad: `Write a compelling advertisement copy about "${topic}" in a ${tone} tone. The ad should be approximately ${length} words long. Focus on benefits, include a clear call-to-action, and make it persuasive to drive engagement or sales.`
  };

  return basePrompts[contentType] || basePrompts.blog;
};

// API Routes
app.get('/', (req, res) => {
  res.json({ message: 'AI Content Generator API is running!' });
});

app.post('/api/generate', async (req, res) => {
  try {
    const { contentType, tone, length, topic } = req.body;

    // Validate required fields
    if (!contentType || !tone || !length || !topic) {
      return res.status(400).json({
        error: 'Missing required fields: contentType, tone, length, and topic are required'
      });
    }

    // Generate the prompt
    const prompt = getPrompt(contentType, tone, length, topic);

    let generatedContent;

    try {
      // Call OpenAI API
      const completion = await openai.chat.completions.create({
        model: "gpt-3.5-turbo",
        messages: [
          {
            role: "system",
            content: "You are a professional content writer who creates high-quality, engaging content for various purposes. Always follow the specified tone, length, and content type requirements."
          },
          {
            role: "user",
            content: prompt
          }
        ],
        max_tokens: Math.min(Math.ceil(length * 1.5), 4000), // Estimate tokens needed
        temperature: 0.7,
      });

      generatedContent = completion.choices[0].message.content;

    } catch (openaiError) {
      console.log('OpenAI API error, using mock content:', openaiError.message);

      // Fallback to mock content for demonstration
      const mockContent = generateMockContent(contentType, tone, length, topic);
      generatedContent = mockContent;
    }

    // Basic response structure (can be extended for grammar checking later)
    const response = {
      content: generatedContent,
      grammar: {
        suggestions: [], // Placeholder for future grammar checking
        readability: 'Good' // Placeholder for future readability analysis
      }
    };

    res.json(response);

  } catch (error) {
    console.error('Error generating content:', error);
    res.status(500).json({ error: 'Failed to generate content. Please try again.' });
  }
});

// Mock content generator for testing when OpenAI API is not available
const generateMockContent = (contentType, tone, length, topic) => {
  const mockTemplates = {
    blog: {
      professional: `# ${topic.charAt(0).toUpperCase() + topic.slice(1)}: A Professional Analysis

In today's rapidly evolving landscape, ${topic} has emerged as a critical factor that organizations and individuals must understand and leverage effectively.

## Understanding the Impact

The significance of ${topic} cannot be overstated. Research indicates that businesses and professionals who embrace this concept are better positioned for success in the modern marketplace.

## Key Considerations

When examining ${topic}, several important factors come into play:

1. **Strategic Implementation**: Developing a comprehensive approach
2. **Best Practices**: Following industry-standard methodologies
3. **Future Outlook**: Preparing for upcoming trends and developments

## Conclusion

As we move forward, ${topic} will continue to shape our professional landscape. Organizations that invest in understanding and implementing related strategies will find themselves at a competitive advantage.

*This content is approximately ${length} words and maintains a ${tone} tone throughout.*`,

      casual: `Hey there! Let's talk about ${topic} - it's actually pretty fascinating stuff!

So here's the thing about ${topic}: it's everywhere these days, and honestly, it's kind of amazing how much it's changing the way we do things.

## What's the Big Deal?

You might be wondering why everyone's talking about ${topic}. Well, it turns out there are some pretty good reasons:

- It's making life easier in ways we never expected
- People are finding creative new uses for it all the time
- The possibilities seem pretty much endless

## My Take on It

From what I've seen, ${topic} is one of those things that starts small but then suddenly you realize it's everywhere. Kind of like how smartphones went from being this cool gadget to something we literally can't live without.

## What's Next?

The future looks pretty exciting for ${topic}. I'm curious to see where it goes from here!

*Hope you enjoyed this ${length}-word casual take on ${topic}!*`,

      friendly: `Hello! I'm excited to share some thoughts about ${topic} with you today.

${topic.charAt(0).toUpperCase() + topic.slice(1)} is such an interesting subject, and I think you'll find it as fascinating as I do!

## Why This Matters

There's something really special about ${topic} that I want to highlight. It's not just another trend - it's something that's genuinely making a difference in people's lives.

## What I've Learned

Through my research and experience, I've discovered that ${topic} offers some wonderful benefits:

• It brings people together in meaningful ways
• It opens up new opportunities for growth and learning
• It helps solve real problems that matter to us all

## Looking Ahead

I'm genuinely optimistic about where ${topic} is heading. The potential for positive impact is enormous, and I believe we're just getting started.

Thank you for taking the time to explore ${topic} with me. I hope this ${tone}, ${length}-word piece has given you some valuable insights!`
    },

    'social post': {
      professional: `🚀 Exploring the impact of ${topic} in today's business landscape

Key insights:
✅ Strategic implementation drives results
✅ Industry leaders are already adapting
✅ Future opportunities are emerging

What's your experience with ${topic}? Share your thoughts below!

#${topic.replace(/\s+/g, '')} #Business #Innovation #ProfessionalDevelopment`,

      casual: `Just discovered something cool about ${topic}! 🤯

Seriously, this stuff is everywhere now and it's actually pretty awesome. Anyone else been following this trend?

Drop a comment if you've got thoughts! 👇

#${topic.replace(/\s+/g, '')} #TrendAlert #JustSaying`,

      friendly: `Hi friends! 👋

I've been learning about ${topic} lately and wanted to share some excitement with you all! It's amazing how this is creating new possibilities for everyone.

Would love to hear your thoughts and experiences! Let's chat in the comments 💬

#${topic.replace(/\s+/g, '')} #Community #Learning #Sharing`
    },

    ad: {
      professional: `Transform Your Business with ${topic}

Industry leaders trust our expertise in ${topic} to drive measurable results. Our proven methodology delivers:

✓ Increased efficiency
✓ Competitive advantage
✓ Sustainable growth

Ready to elevate your business? Contact our team of experts today.

📞 Call Now | 🌐 Visit Our Website | 📧 Get Free Consultation

*Professional ${topic} solutions for forward-thinking organizations*`,

      casual: `Hey! Tired of missing out on ${topic}?

We've got you covered! 🙌

Our super easy approach to ${topic} means you can:
• Get started in minutes
• See results fast
• Actually enjoy the process

No complicated stuff. No headaches. Just results.

👆 Click the link and see what everyone's talking about!

*Limited time offer - because good things don't last forever! 😉*`,

      friendly: `Discover the Joy of ${topic}! 🌟

We believe ${topic} should be accessible, enjoyable, and rewarding for everyone. That's why we've created a welcoming community where you can:

💫 Learn at your own pace
💫 Connect with like-minded people
💫 Achieve your goals with support

Join thousands of happy customers who've transformed their experience with ${topic}.

🎯 Start Your Journey Today - We're Here to Help!

*Because everyone deserves to succeed with ${topic}* ❤️`
    }
  };

  const template = mockTemplates[contentType]?.[tone] || mockTemplates.blog.professional;
  return template;
};

app.listen(port, () => {
  console.log(`AI Content Generator API listening at http://localhost:${port}`);
  console.log(`OpenAI API Key configured: ${process.env.OPENAI_API_KEY ? 'Yes' : 'No'}`);
});