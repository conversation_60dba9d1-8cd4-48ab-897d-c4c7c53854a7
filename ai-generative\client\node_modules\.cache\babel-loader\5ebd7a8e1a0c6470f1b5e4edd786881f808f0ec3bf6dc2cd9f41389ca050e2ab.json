{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\vedika\\\\ai-generative\\\\client\\\\src\\\\components\\\\LengthSelector.jsx\";\nimport React from 'react';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst LengthSelector = ({\n  value,\n  onChange\n}) => /*#__PURE__*/_jsxDEV(\"div\", {\n  children: [/*#__PURE__*/_jsxDEV(\"label\", {\n    children: \"Length: \"\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 5,\n    columnNumber: 5\n  }, this), /*#__PURE__*/_jsxDEV(\"select\", {\n    value: value,\n    onChange: e => onChange && onChange(e.target.value),\n    children: [/*#__PURE__*/_jsxDEV(\"option\", {\n      value: \"short\",\n      children: \"Short\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 7,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n      value: \"medium\",\n      children: \"Medium\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 8,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n      value: \"long\",\n      children: \"Long\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 9,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 6,\n    columnNumber: 5\n  }, this)]\n}, void 0, true, {\n  fileName: _jsxFileName,\n  lineNumber: 4,\n  columnNumber: 3\n}, this);\n_c = LengthSelector;\nexport default LengthSelector;\nvar _c;\n$RefreshReg$(_c, \"LengthSelector\");", "map": {"version": 3, "names": ["React", "jsxDEV", "_jsxDEV", "LengthSelector", "value", "onChange", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "e", "target", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/vedika/ai-generative/client/src/components/LengthSelector.jsx"], "sourcesContent": ["import React from 'react';\r\n\r\nconst LengthSelector = ({ value, onChange }) => (\r\n  <div>\r\n    <label>Length: </label>\r\n    <select value={value} onChange={e => onChange && onChange(e.target.value)}>\r\n      <option value=\"short\">Short</option>\r\n      <option value=\"medium\">Medium</option>\r\n      <option value=\"long\">Long</option>\r\n    </select>\r\n  </div>\r\n);\r\n\r\nexport default LengthSelector; "], "mappings": ";AAAA,OAAOA,KAAK,MAAM,OAAO;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE1B,MAAMC,cAAc,GAAGA,CAAC;EAAEC,KAAK;EAAEC;AAAS,CAAC,kBACzCH,OAAA;EAAAI,QAAA,gBACEJ,OAAA;IAAAI,QAAA,EAAO;EAAQ;IAAAC,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAAO,CAAC,eACvBR,OAAA;IAAQE,KAAK,EAAEA,KAAM;IAACC,QAAQ,EAAEM,CAAC,IAAIN,QAAQ,IAAIA,QAAQ,CAACM,CAAC,CAACC,MAAM,CAACR,KAAK,CAAE;IAAAE,QAAA,gBACxEJ,OAAA;MAAQE,KAAK,EAAC,OAAO;MAAAE,QAAA,EAAC;IAAK;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAQ,CAAC,eACpCR,OAAA;MAAQE,KAAK,EAAC,QAAQ;MAAAE,QAAA,EAAC;IAAM;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAQ,CAAC,eACtCR,OAAA;MAAQE,KAAK,EAAC,MAAM;MAAAE,QAAA,EAAC;IAAI;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAQ,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAC5B,CAAC;AAAA;EAAAH,QAAA,EAAAC,YAAA;EAAAC,UAAA;EAAAC,YAAA;AAAA,OACN,CACN;AAACG,EAAA,GATIV,cAAc;AAWpB,eAAeA,cAAc;AAAC,IAAAU,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}