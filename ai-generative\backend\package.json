{"name": "backend", "version": "1.0.0", "description": "", "main": "index.js", "scripts": {"test": "jest", "test:watch": "jest --watch", "start": "node index.js", "dev": "node index.js"}, "keywords": [], "author": "", "license": "ISC", "type": "commonjs", "dependencies": {"bcryptjs": "^3.0.2", "cors": "^2.8.5", "dotenv": "^17.2.0", "express": "^5.1.0", "jsonwebtoken": "^9.0.2", "openai": "^5.10.1"}, "devDependencies": {"jest": "^30.0.4", "supertest": "^7.1.3"}}