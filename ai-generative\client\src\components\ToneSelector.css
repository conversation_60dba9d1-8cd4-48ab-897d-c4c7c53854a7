.tone-selector {
  margin-bottom: 1.5rem;
}

.selector-label {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-size: 1.1rem;
  font-weight: 600;
  color: #1e293b;
  margin-bottom: 1rem;
}

.label-icon {
  font-size: 1.3rem;
  animation: bounce 2s ease-in-out infinite;
}

.tone-grid {
  display: grid;
  grid-template-columns: 1fr;
  gap: 0.75rem;
}

.tone-card {
  display: flex;
  align-items: center;
  gap: 1rem;
  padding: 1rem;
  background: white;
  border: 2px solid #e2e8f0;
  border-radius: 12px;
  cursor: pointer;
  transition: all 0.3s ease;
  text-align: left;
  position: relative;
  overflow: hidden;
}

.tone-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 107, 107, 0.1), transparent);
  transition: left 0.5s ease;
}

.tone-card:hover::before {
  left: 100%;
}

.tone-card:hover {
  border-color: #ff6b6b;
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(255, 107, 107, 0.15);
}

.tone-card.active {
  border-color: #ff6b6b;
  background: linear-gradient(135deg, #ff6b6b 0%, #feca57 100%);
  color: white;
  transform: scale(1.02);
  box-shadow: 0 10px 30px rgba(255, 107, 107, 0.3);
}

.tone-card.active .tone-title,
.tone-card.active .tone-description {
  color: white;
}

.tone-icon {
  font-size: 2.5rem;
  animation: wiggle 3s ease-in-out infinite;
  filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.1));
}

.tone-card.active .tone-icon {
  animation: dance 2s ease-in-out infinite;
  filter: drop-shadow(0 0 10px rgba(255, 255, 255, 0.5));
}

.tone-content {
  flex: 1;
}

.tone-title {
  font-size: 1.1rem;
  font-weight: 600;
  color: #1e293b;
  margin-bottom: 0.25rem;
  transition: color 0.3s ease;
}

.tone-description {
  font-size: 0.9rem;
  color: #64748b;
  transition: color 0.3s ease;
}

/* Cartoon decorations */
.tone-card::after {
  content: '🎨';
  position: absolute;
  top: 8px;
  right: 8px;
  font-size: 0.8rem;
  opacity: 0;
  transition: opacity 0.3s ease;
  animation: twinkle 2s ease-in-out infinite;
}

.tone-card.active::after {
  opacity: 1;
}

/* Responsive design */
@media (min-width: 768px) {
  .tone-grid {
    grid-template-columns: repeat(3, 1fr);
  }
  
  .tone-card {
    flex-direction: column;
    text-align: center;
    padding: 1.5rem 1rem;
  }
  
  .tone-icon {
    font-size: 3rem;
    margin-bottom: 0.5rem;
  }
}

/* Animation keyframes */
@keyframes wiggle {
  0%, 100% {
    transform: rotate(0deg);
  }
  25% {
    transform: rotate(3deg);
  }
  75% {
    transform: rotate(-3deg);
  }
}

@keyframes dance {
  0%, 100% {
    transform: scale(1) rotate(0deg);
  }
  25% {
    transform: scale(1.1) rotate(5deg);
  }
  50% {
    transform: scale(1.05) rotate(0deg);
  }
  75% {
    transform: scale(1.1) rotate(-5deg);
  }
}

@keyframes twinkle {
  0%, 100% {
    opacity: 0.3;
    transform: scale(0.8);
  }
  50% {
    opacity: 1;
    transform: scale(1.2);
  }
}

@keyframes bounce {
  0%, 100% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-5px);
  }
}
