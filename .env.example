# AI Content Generator Environment Variables
# Copy this file to .env and update with your actual values

# OpenAI API Configuration
OPENAI_API_KEY=sk-your-openai-api-key-here

# JWT Secret for authentication (use a strong, random string)
JWT_SECRET=your-very-strong-jwt-secret-here-minimum-32-characters

# Server Configuration
NODE_ENV=development
PORT=5000

# Frontend URL (for CORS in production)
FRONTEND_URL=http://localhost:3000

# Optional: Database Configuration (if implementing persistent storage)
# DATABASE_URL=postgresql://username:password@localhost:5432/ai_content

# Optional: Redis Configuration (if implementing caching)
# REDIS_URL=redis://localhost:6379

# Optional: Email Configuration (if implementing email features)
# SMTP_HOST=smtp.gmail.com
# SMTP_PORT=587
# SMTP_USER=<EMAIL>
# SMTP_PASS=your-app-password
