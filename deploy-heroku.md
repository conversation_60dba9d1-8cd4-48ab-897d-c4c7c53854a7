# Heroku Deployment Guide

## Prerequisites
1. Create a Heroku account at https://heroku.com
2. Install Heroku CLI from https://devcenter.heroku.com/articles/heroku-cli
3. Install Git if not already installed

## Backend Deployment

### Step 1: Prepare Backend for Heroku
```bash
cd ai-generative/backend
```

### Step 2: Create Procfile
Create a file named `<PERSON>cfile` (no extension) with:
```
web: npm start
```

### Step 3: Update package.json
Ensure your package.json has:
```json
{
  "engines": {
    "node": "18.x"
  },
  "scripts": {
    "start": "node index.js"
  }
}
```

### Step 4: Deploy Backend
```bash
# Login to Heroku
heroku login

# Create Heroku app for backend
heroku create your-app-name-api

# Set environment variables
heroku config:set OPENAI_API_KEY=your_openai_key
heroku config:set JWT_SECRET=your_jwt_secret_32_chars_minimum
heroku config:set NODE_ENV=production

# Initialize git if not already done
git init
git add .
git commit -m "Initial backend deployment"

# Deploy to Heroku
git push heroku main
```

### Step 5: Test Backend
```bash
# Open your backend URL
heroku open
# Should show: {"message":"AI Content Generator API is running!"}
```

## Frontend Deployment

### Option A: Netlify (Recommended for React)

1. Build the frontend:
```bash
cd ai-generative/client
npm run build
```

2. Go to https://netlify.com and sign up
3. Drag and drop the `build` folder to Netlify
4. Update API endpoints in your React app to point to Heroku backend

### Option B: Heroku for Frontend

```bash
cd ai-generative/client

# Create static server setup
npm install -g serve
echo "web: serve -s build -p $PORT" > Procfile

# Build the app
npm run build

# Create Heroku app for frontend
heroku create your-app-name-frontend

# Deploy
git init
git add .
git commit -m "Frontend deployment"
git push heroku main
```

## Environment Configuration

### Backend Environment Variables on Heroku:
```bash
heroku config:set OPENAI_API_KEY=sk-your-key-here
heroku config:set JWT_SECRET=your-32-char-secret
heroku config:set NODE_ENV=production
heroku config:set FRONTEND_URL=https://your-frontend-app.netlify.app
```

### Frontend Environment Variables:
Create `.env.production` in client folder:
```env
REACT_APP_API_URL=https://your-backend-app.herokuapp.com
```

## Custom Domain (Optional)
```bash
# Add custom domain to Heroku
heroku domains:add yourdomain.com
heroku domains:add www.yourdomain.com

# Configure DNS with your domain provider
# Point CNAME to your-app-name.herokuapp.com
```

## Monitoring and Logs
```bash
# View logs
heroku logs --tail

# Monitor app performance
heroku ps
heroku addons:create papertrail:choklad
```
