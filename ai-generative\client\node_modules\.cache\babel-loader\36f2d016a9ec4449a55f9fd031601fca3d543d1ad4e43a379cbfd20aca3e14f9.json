{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\vedika\\\\ai-generative\\\\client\\\\src\\\\components\\\\AuthForm.jsx\",\n  _s = $RefreshSig$();\nimport { useState } from 'react';\nimport './AuthForm.css';\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst AuthForm = ({\n  user,\n  setUser\n}) => {\n  _s();\n  const [isLogin, setIsLogin] = useState(true);\n  const [formData, setFormData] = useState({\n    username: '',\n    email: '',\n    password: '',\n    confirmPassword: ''\n  });\n  const [loading, setLoading] = useState(false);\n  const [message, setMessage] = useState('');\n  const [messageType, setMessageType] = useState(''); // 'success' or 'error'\n\n  // Get API base URL\n  const getApiUrl = () => {\n    return process.env.NODE_ENV === 'production' ? '/api' : 'http://localhost:5000/api';\n  };\n  const handleInputChange = e => {\n    setFormData({\n      ...formData,\n      [e.target.name]: e.target.value\n    });\n  };\n  const showMessage = (text, type) => {\n    setMessage(text);\n    setMessageType(type);\n    setTimeout(() => {\n      setMessage('');\n      setMessageType('');\n    }, 5000);\n  };\n  const handleSubmit = async e => {\n    e.preventDefault();\n    setLoading(true);\n    try {\n      if (!isLogin) {\n        // Registration validation\n        if (formData.password !== formData.confirmPassword) {\n          showMessage('Passwords do not match!', 'error');\n          setLoading(false);\n          return;\n        }\n        if (formData.password.length < 6) {\n          showMessage('Password must be at least 6 characters long!', 'error');\n          setLoading(false);\n          return;\n        }\n      }\n      const endpoint = isLogin ? '/auth/login' : '/auth/register';\n      const payload = isLogin ? {\n        email: formData.email,\n        password: formData.password\n      } : {\n        username: formData.username,\n        email: formData.email,\n        password: formData.password\n      };\n      const response = await fetch(`${getApiUrl()}${endpoint}`, {\n        method: 'POST',\n        headers: {\n          'Content-Type': 'application/json'\n        },\n        body: JSON.stringify(payload)\n      });\n      const data = await response.json();\n      if (response.ok) {\n        // Success\n        localStorage.setItem('authToken', data.token);\n        localStorage.setItem('userData', JSON.stringify(data.user));\n        setUser(data.user);\n        showMessage(isLogin ? `Welcome back, ${data.user.username}! 🎉` : `Account created successfully! Welcome, ${data.user.username}! 🎉`, 'success');\n\n        // Clear form\n        setFormData({\n          username: '',\n          email: '',\n          password: '',\n          confirmPassword: ''\n        });\n      } else {\n        // Error\n        showMessage(data.error || 'Something went wrong!', 'error');\n      }\n    } catch (error) {\n      showMessage('Unable to connect to the server. Please try again later.', 'error');\n    } finally {\n      setLoading(false);\n    }\n  };\n  const toggleMode = () => {\n    setIsLogin(!isLogin);\n    setMessage('');\n    setFormData({\n      username: '',\n      email: '',\n      password: '',\n      confirmPassword: ''\n    });\n  };\n  if (user) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"auth-container\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"user-profile\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"profile-header\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"profile-avatar\",\n            children: /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"avatar-emoji\",\n              children: \"\\uD83D\\uDC64\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 124,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 123,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"profile-info\",\n            children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n              className: \"profile-name\",\n              children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"welcome-emoji\",\n                children: \"\\uD83D\\uDC4B\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 128,\n                columnNumber: 17\n              }, this), \"Welcome, \", user.username, \"!\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 127,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"profile-email\",\n              children: user.email\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 131,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 126,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 122,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"profile-stats\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"stat-card\",\n            children: [/*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"stat-icon\",\n              children: \"\\uD83D\\uDCDD\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 137,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"stat-content\",\n              children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n                children: \"Content Generated\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 139,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                children: \"Ready to create!\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 140,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 138,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 136,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"stat-card\",\n            children: [/*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"stat-icon\",\n              children: \"\\u2B50\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 144,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"stat-content\",\n              children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n                children: \"Account Status\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 146,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                children: \"Active Member\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 147,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 145,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 143,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 135,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"profile-actions\",\n          children: [/*#__PURE__*/_jsxDEV(\"button\", {\n            className: \"profile-btn generate-btn\",\n            children: [/*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"btn-icon\",\n              children: \"\\uD83D\\uDE80\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 154,\n              columnNumber: 15\n            }, this), \"Start Creating\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 153,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            className: \"profile-btn history-btn\",\n            children: [/*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"btn-icon\",\n              children: \"\\uD83D\\uDCDA\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 158,\n              columnNumber: 15\n            }, this), \"View History\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 157,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 152,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 121,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 120,\n      columnNumber: 7\n    }, this);\n  }\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"auth-container\",\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"auth-form-wrapper\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"auth-header\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"auth-illustration\",\n          children: [/*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"auth-emoji auth-emoji-1\",\n            children: \"\\uD83C\\uDFA8\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 172,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"auth-emoji auth-emoji-2\",\n            children: \"\\u2728\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 173,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"auth-emoji auth-emoji-3\",\n            children: \"\\uD83D\\uDE80\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 174,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 171,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"h2\", {\n          className: \"auth-title\",\n          children: [isLogin ? 'Welcome Back!' : 'Join the Creative Community!', /*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"title-decoration\",\n            children: isLogin ? '👋' : '🎉'\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 178,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 176,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"auth-subtitle\",\n          children: isLogin ? 'Sign in to continue creating amazing content with AI' : 'Create your account and start generating incredible content'\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 182,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 170,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"form\", {\n        onSubmit: handleSubmit,\n        className: \"auth-form\",\n        children: [!isLogin && /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"form-group\",\n          children: [/*#__PURE__*/_jsxDEV(\"label\", {\n            className: \"form-label\",\n            children: [/*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"label-icon\",\n              children: \"\\uD83D\\uDC64\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 194,\n              columnNumber: 17\n            }, this), \"Username\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 193,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n            type: \"text\",\n            name: \"username\",\n            value: formData.username,\n            onChange: handleInputChange,\n            placeholder: \"Choose a creative username\",\n            className: \"form-input\",\n            required: !isLogin,\n            disabled: loading\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 197,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 192,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"form-group\",\n          children: [/*#__PURE__*/_jsxDEV(\"label\", {\n            className: \"form-label\",\n            children: [/*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"label-icon\",\n              children: \"\\uD83D\\uDCE7\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 212,\n              columnNumber: 15\n            }, this), \"Email\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 211,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n            type: \"email\",\n            name: \"email\",\n            value: formData.email,\n            onChange: handleInputChange,\n            placeholder: \"<EMAIL>\",\n            className: \"form-input\",\n            required: true,\n            disabled: loading\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 215,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 210,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"form-group\",\n          children: [/*#__PURE__*/_jsxDEV(\"label\", {\n            className: \"form-label\",\n            children: [/*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"label-icon\",\n              children: \"\\uD83D\\uDD12\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 229,\n              columnNumber: 15\n            }, this), \"Password\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 228,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n            type: \"password\",\n            name: \"password\",\n            value: formData.password,\n            onChange: handleInputChange,\n            placeholder: isLogin ? \"Enter your password\" : \"Create a secure password (6+ characters)\",\n            className: \"form-input\",\n            required: true,\n            disabled: loading,\n            minLength: isLogin ? undefined : 6\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 232,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 227,\n          columnNumber: 11\n        }, this), !isLogin && /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"form-group\",\n          children: [/*#__PURE__*/_jsxDEV(\"label\", {\n            className: \"form-label\",\n            children: [/*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"label-icon\",\n              children: \"\\uD83D\\uDD10\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 248,\n              columnNumber: 17\n            }, this), \"Confirm Password\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 247,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n            type: \"password\",\n            name: \"confirmPassword\",\n            value: formData.confirmPassword,\n            onChange: handleInputChange,\n            placeholder: \"Confirm your password\",\n            className: \"form-input\",\n            required: !isLogin,\n            disabled: loading\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 251,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 246,\n          columnNumber: 13\n        }, this), message && /*#__PURE__*/_jsxDEV(\"div\", {\n          className: `message ${messageType}`,\n          children: [/*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"message-icon\",\n            children: messageType === 'success' ? '✅' : '❌'\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 266,\n            columnNumber: 15\n          }, this), message]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 265,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          type: \"submit\",\n          className: \"auth-submit-btn\",\n          disabled: loading,\n          children: loading ? /*#__PURE__*/_jsxDEV(_Fragment, {\n            children: [/*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"spinner\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 280,\n              columnNumber: 17\n            }, this), isLogin ? 'Signing In...' : 'Creating Account...']\n          }, void 0, true) : /*#__PURE__*/_jsxDEV(_Fragment, {\n            children: [/*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"btn-icon\",\n              children: isLogin ? '🚪' : '🎯'\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 285,\n              columnNumber: 17\n            }, this), isLogin ? 'Sign In' : 'Create Account']\n          }, void 0, true)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 273,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 190,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"auth-footer\",\n        children: [/*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"auth-switch-text\",\n          children: isLogin ? \"Don't have an account?\" : \"Already have an account?\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 295,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          type: \"button\",\n          onClick: toggleMode,\n          className: \"auth-switch-btn\",\n          disabled: loading,\n          children: [/*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"switch-icon\",\n            children: isLogin ? '🆕' : '🔄'\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 304,\n            columnNumber: 13\n          }, this), isLogin ? 'Create Account' : 'Sign In']\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 298,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 294,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 169,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 168,\n    columnNumber: 5\n  }, this);\n};\n_s(AuthForm, \"FzuKKKgrko/pj98k0+aL3Zo/XfA=\");\n_c = AuthForm;\nexport default AuthForm;\nvar _c;\n$RefreshReg$(_c, \"AuthForm\");", "map": {"version": 3, "names": ["useState", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "AuthForm", "user", "setUser", "_s", "is<PERSON>ogin", "setIsLogin", "formData", "setFormData", "username", "email", "password", "confirmPassword", "loading", "setLoading", "message", "setMessage", "messageType", "setMessageType", "getApiUrl", "process", "env", "NODE_ENV", "handleInputChange", "e", "target", "name", "value", "showMessage", "text", "type", "setTimeout", "handleSubmit", "preventDefault", "length", "endpoint", "payload", "response", "fetch", "method", "headers", "body", "JSON", "stringify", "data", "json", "ok", "localStorage", "setItem", "token", "error", "toggleMode", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "onSubmit", "onChange", "placeholder", "required", "disabled", "<PERSON><PERSON><PERSON><PERSON>", "undefined", "onClick", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/vedika/ai-generative/client/src/components/AuthForm.jsx"], "sourcesContent": ["import { useState } from 'react';\r\nimport './AuthForm.css';\r\n\r\nconst AuthForm = ({ user, setUser }) => {\r\n  const [isLogin, setIsLogin] = useState(true);\r\n  const [formData, setFormData] = useState({\r\n    username: '',\r\n    email: '',\r\n    password: '',\r\n    confirmPassword: ''\r\n  });\r\n  const [loading, setLoading] = useState(false);\r\n  const [message, setMessage] = useState('');\r\n  const [messageType, setMessageType] = useState(''); // 'success' or 'error'\r\n\r\n  // Get API base URL\r\n  const getApiUrl = () => {\r\n    return process.env.NODE_ENV === 'production'\r\n      ? '/api'\r\n      : 'http://localhost:5000/api';\r\n  };\r\n\r\n  const handleInputChange = (e) => {\r\n    setFormData({\r\n      ...formData,\r\n      [e.target.name]: e.target.value\r\n    });\r\n  };\r\n\r\n  const showMessage = (text, type) => {\r\n    setMessage(text);\r\n    setMessageType(type);\r\n    setTimeout(() => {\r\n      setMessage('');\r\n      setMessageType('');\r\n    }, 5000);\r\n  };\r\n\r\n  const handleSubmit = async (e) => {\r\n    e.preventDefault();\r\n    setLoading(true);\r\n\r\n    try {\r\n      if (!isLogin) {\r\n        // Registration validation\r\n        if (formData.password !== formData.confirmPassword) {\r\n          showMessage('Passwords do not match!', 'error');\r\n          setLoading(false);\r\n          return;\r\n        }\r\n        if (formData.password.length < 6) {\r\n          showMessage('Password must be at least 6 characters long!', 'error');\r\n          setLoading(false);\r\n          return;\r\n        }\r\n      }\r\n\r\n      const endpoint = isLogin ? '/auth/login' : '/auth/register';\r\n      const payload = isLogin\r\n        ? { email: formData.email, password: formData.password }\r\n        : {\r\n            username: formData.username,\r\n            email: formData.email,\r\n            password: formData.password\r\n          };\r\n\r\n      const response = await fetch(`${getApiUrl()}${endpoint}`, {\r\n        method: 'POST',\r\n        headers: {\r\n          'Content-Type': 'application/json',\r\n        },\r\n        body: JSON.stringify(payload),\r\n      });\r\n\r\n      const data = await response.json();\r\n\r\n      if (response.ok) {\r\n        // Success\r\n        localStorage.setItem('authToken', data.token);\r\n        localStorage.setItem('userData', JSON.stringify(data.user));\r\n        setUser(data.user);\r\n        showMessage(\r\n          isLogin\r\n            ? `Welcome back, ${data.user.username}! 🎉`\r\n            : `Account created successfully! Welcome, ${data.user.username}! 🎉`,\r\n          'success'\r\n        );\r\n\r\n        // Clear form\r\n        setFormData({\r\n          username: '',\r\n          email: '',\r\n          password: '',\r\n          confirmPassword: ''\r\n        });\r\n      } else {\r\n        // Error\r\n        showMessage(data.error || 'Something went wrong!', 'error');\r\n      }\r\n    } catch (error) {\r\n      showMessage('Unable to connect to the server. Please try again later.', 'error');\r\n    } finally {\r\n      setLoading(false);\r\n    }\r\n  };\r\n\r\n  const toggleMode = () => {\r\n    setIsLogin(!isLogin);\r\n    setMessage('');\r\n    setFormData({\r\n      username: '',\r\n      email: '',\r\n      password: '',\r\n      confirmPassword: ''\r\n    });\r\n  };\r\n\r\n  if (user) {\r\n    return (\r\n      <div className=\"auth-container\">\r\n        <div className=\"user-profile\">\r\n          <div className=\"profile-header\">\r\n            <div className=\"profile-avatar\">\r\n              <span className=\"avatar-emoji\">👤</span>\r\n            </div>\r\n            <div className=\"profile-info\">\r\n              <h3 className=\"profile-name\">\r\n                <span className=\"welcome-emoji\">👋</span>\r\n                Welcome, {user.username}!\r\n              </h3>\r\n              <p className=\"profile-email\">{user.email}</p>\r\n            </div>\r\n          </div>\r\n\r\n          <div className=\"profile-stats\">\r\n            <div className=\"stat-card\">\r\n              <span className=\"stat-icon\">📝</span>\r\n              <div className=\"stat-content\">\r\n                <h4>Content Generated</h4>\r\n                <p>Ready to create!</p>\r\n              </div>\r\n            </div>\r\n            <div className=\"stat-card\">\r\n              <span className=\"stat-icon\">⭐</span>\r\n              <div className=\"stat-content\">\r\n                <h4>Account Status</h4>\r\n                <p>Active Member</p>\r\n              </div>\r\n            </div>\r\n          </div>\r\n\r\n          <div className=\"profile-actions\">\r\n            <button className=\"profile-btn generate-btn\">\r\n              <span className=\"btn-icon\">🚀</span>\r\n              Start Creating\r\n            </button>\r\n            <button className=\"profile-btn history-btn\">\r\n              <span className=\"btn-icon\">📚</span>\r\n              View History\r\n            </button>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    );\r\n  }\r\n\r\n  return (\r\n    <div className=\"auth-container\">\r\n      <div className=\"auth-form-wrapper\">\r\n        <div className=\"auth-header\">\r\n          <div className=\"auth-illustration\">\r\n            <span className=\"auth-emoji auth-emoji-1\">🎨</span>\r\n            <span className=\"auth-emoji auth-emoji-2\">✨</span>\r\n            <span className=\"auth-emoji auth-emoji-3\">🚀</span>\r\n          </div>\r\n          <h2 className=\"auth-title\">\r\n            {isLogin ? 'Welcome Back!' : 'Join the Creative Community!'}\r\n            <span className=\"title-decoration\">\r\n              {isLogin ? '👋' : '🎉'}\r\n            </span>\r\n          </h2>\r\n          <p className=\"auth-subtitle\">\r\n            {isLogin\r\n              ? 'Sign in to continue creating amazing content with AI'\r\n              : 'Create your account and start generating incredible content'\r\n            }\r\n          </p>\r\n        </div>\r\n\r\n        <form onSubmit={handleSubmit} className=\"auth-form\">\r\n          {!isLogin && (\r\n            <div className=\"form-group\">\r\n              <label className=\"form-label\">\r\n                <span className=\"label-icon\">👤</span>\r\n                Username\r\n              </label>\r\n              <input\r\n                type=\"text\"\r\n                name=\"username\"\r\n                value={formData.username}\r\n                onChange={handleInputChange}\r\n                placeholder=\"Choose a creative username\"\r\n                className=\"form-input\"\r\n                required={!isLogin}\r\n                disabled={loading}\r\n              />\r\n            </div>\r\n          )}\r\n\r\n          <div className=\"form-group\">\r\n            <label className=\"form-label\">\r\n              <span className=\"label-icon\">📧</span>\r\n              Email\r\n            </label>\r\n            <input\r\n              type=\"email\"\r\n              name=\"email\"\r\n              value={formData.email}\r\n              onChange={handleInputChange}\r\n              placeholder=\"<EMAIL>\"\r\n              className=\"form-input\"\r\n              required\r\n              disabled={loading}\r\n            />\r\n          </div>\r\n\r\n          <div className=\"form-group\">\r\n            <label className=\"form-label\">\r\n              <span className=\"label-icon\">🔒</span>\r\n              Password\r\n            </label>\r\n            <input\r\n              type=\"password\"\r\n              name=\"password\"\r\n              value={formData.password}\r\n              onChange={handleInputChange}\r\n              placeholder={isLogin ? \"Enter your password\" : \"Create a secure password (6+ characters)\"}\r\n              className=\"form-input\"\r\n              required\r\n              disabled={loading}\r\n              minLength={isLogin ? undefined : 6}\r\n            />\r\n          </div>\r\n\r\n          {!isLogin && (\r\n            <div className=\"form-group\">\r\n              <label className=\"form-label\">\r\n                <span className=\"label-icon\">🔐</span>\r\n                Confirm Password\r\n              </label>\r\n              <input\r\n                type=\"password\"\r\n                name=\"confirmPassword\"\r\n                value={formData.confirmPassword}\r\n                onChange={handleInputChange}\r\n                placeholder=\"Confirm your password\"\r\n                className=\"form-input\"\r\n                required={!isLogin}\r\n                disabled={loading}\r\n              />\r\n            </div>\r\n          )}\r\n\r\n          {message && (\r\n            <div className={`message ${messageType}`}>\r\n              <span className=\"message-icon\">\r\n                {messageType === 'success' ? '✅' : '❌'}\r\n              </span>\r\n              {message}\r\n            </div>\r\n          )}\r\n\r\n          <button\r\n            type=\"submit\"\r\n            className=\"auth-submit-btn\"\r\n            disabled={loading}\r\n          >\r\n            {loading ? (\r\n              <>\r\n                <span className=\"spinner\"></span>\r\n                {isLogin ? 'Signing In...' : 'Creating Account...'}\r\n              </>\r\n            ) : (\r\n              <>\r\n                <span className=\"btn-icon\">\r\n                  {isLogin ? '🚪' : '🎯'}\r\n                </span>\r\n                {isLogin ? 'Sign In' : 'Create Account'}\r\n              </>\r\n            )}\r\n          </button>\r\n        </form>\r\n\r\n        <div className=\"auth-footer\">\r\n          <p className=\"auth-switch-text\">\r\n            {isLogin ? \"Don't have an account?\" : \"Already have an account?\"}\r\n          </p>\r\n          <button\r\n            type=\"button\"\r\n            onClick={toggleMode}\r\n            className=\"auth-switch-btn\"\r\n            disabled={loading}\r\n          >\r\n            <span className=\"switch-icon\">\r\n              {isLogin ? '🆕' : '🔄'}\r\n            </span>\r\n            {isLogin ? 'Create Account' : 'Sign In'}\r\n          </button>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default AuthForm;"], "mappings": ";;AAAA,SAASA,QAAQ,QAAQ,OAAO;AAChC,OAAO,gBAAgB;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAExB,MAAMC,QAAQ,GAAGA,CAAC;EAAEC,IAAI;EAAEC;AAAQ,CAAC,KAAK;EAAAC,EAAA;EACtC,MAAM,CAACC,OAAO,EAAEC,UAAU,CAAC,GAAGV,QAAQ,CAAC,IAAI,CAAC;EAC5C,MAAM,CAACW,QAAQ,EAAEC,WAAW,CAAC,GAAGZ,QAAQ,CAAC;IACvCa,QAAQ,EAAE,EAAE;IACZC,KAAK,EAAE,EAAE;IACTC,QAAQ,EAAE,EAAE;IACZC,eAAe,EAAE;EACnB,CAAC,CAAC;EACF,MAAM,CAACC,OAAO,EAAEC,UAAU,CAAC,GAAGlB,QAAQ,CAAC,KAAK,CAAC;EAC7C,MAAM,CAACmB,OAAO,EAAEC,UAAU,CAAC,GAAGpB,QAAQ,CAAC,EAAE,CAAC;EAC1C,MAAM,CAACqB,WAAW,EAAEC,cAAc,CAAC,GAAGtB,QAAQ,CAAC,EAAE,CAAC,CAAC,CAAC;;EAEpD;EACA,MAAMuB,SAAS,GAAGA,CAAA,KAAM;IACtB,OAAOC,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,GACxC,MAAM,GACN,2BAA2B;EACjC,CAAC;EAED,MAAMC,iBAAiB,GAAIC,CAAC,IAAK;IAC/BhB,WAAW,CAAC;MACV,GAAGD,QAAQ;MACX,CAACiB,CAAC,CAACC,MAAM,CAACC,IAAI,GAAGF,CAAC,CAACC,MAAM,CAACE;IAC5B,CAAC,CAAC;EACJ,CAAC;EAED,MAAMC,WAAW,GAAGA,CAACC,IAAI,EAAEC,IAAI,KAAK;IAClCd,UAAU,CAACa,IAAI,CAAC;IAChBX,cAAc,CAACY,IAAI,CAAC;IACpBC,UAAU,CAAC,MAAM;MACff,UAAU,CAAC,EAAE,CAAC;MACdE,cAAc,CAAC,EAAE,CAAC;IACpB,CAAC,EAAE,IAAI,CAAC;EACV,CAAC;EAED,MAAMc,YAAY,GAAG,MAAOR,CAAC,IAAK;IAChCA,CAAC,CAACS,cAAc,CAAC,CAAC;IAClBnB,UAAU,CAAC,IAAI,CAAC;IAEhB,IAAI;MACF,IAAI,CAACT,OAAO,EAAE;QACZ;QACA,IAAIE,QAAQ,CAACI,QAAQ,KAAKJ,QAAQ,CAACK,eAAe,EAAE;UAClDgB,WAAW,CAAC,yBAAyB,EAAE,OAAO,CAAC;UAC/Cd,UAAU,CAAC,KAAK,CAAC;UACjB;QACF;QACA,IAAIP,QAAQ,CAACI,QAAQ,CAACuB,MAAM,GAAG,CAAC,EAAE;UAChCN,WAAW,CAAC,8CAA8C,EAAE,OAAO,CAAC;UACpEd,UAAU,CAAC,KAAK,CAAC;UACjB;QACF;MACF;MAEA,MAAMqB,QAAQ,GAAG9B,OAAO,GAAG,aAAa,GAAG,gBAAgB;MAC3D,MAAM+B,OAAO,GAAG/B,OAAO,GACnB;QAAEK,KAAK,EAAEH,QAAQ,CAACG,KAAK;QAAEC,QAAQ,EAAEJ,QAAQ,CAACI;MAAS,CAAC,GACtD;QACEF,QAAQ,EAAEF,QAAQ,CAACE,QAAQ;QAC3BC,KAAK,EAAEH,QAAQ,CAACG,KAAK;QACrBC,QAAQ,EAAEJ,QAAQ,CAACI;MACrB,CAAC;MAEL,MAAM0B,QAAQ,GAAG,MAAMC,KAAK,CAAC,GAAGnB,SAAS,CAAC,CAAC,GAAGgB,QAAQ,EAAE,EAAE;QACxDI,MAAM,EAAE,MAAM;QACdC,OAAO,EAAE;UACP,cAAc,EAAE;QAClB,CAAC;QACDC,IAAI,EAAEC,IAAI,CAACC,SAAS,CAACP,OAAO;MAC9B,CAAC,CAAC;MAEF,MAAMQ,IAAI,GAAG,MAAMP,QAAQ,CAACQ,IAAI,CAAC,CAAC;MAElC,IAAIR,QAAQ,CAACS,EAAE,EAAE;QACf;QACAC,YAAY,CAACC,OAAO,CAAC,WAAW,EAAEJ,IAAI,CAACK,KAAK,CAAC;QAC7CF,YAAY,CAACC,OAAO,CAAC,UAAU,EAAEN,IAAI,CAACC,SAAS,CAACC,IAAI,CAAC1C,IAAI,CAAC,CAAC;QAC3DC,OAAO,CAACyC,IAAI,CAAC1C,IAAI,CAAC;QAClB0B,WAAW,CACTvB,OAAO,GACH,iBAAiBuC,IAAI,CAAC1C,IAAI,CAACO,QAAQ,MAAM,GACzC,0CAA0CmC,IAAI,CAAC1C,IAAI,CAACO,QAAQ,MAAM,EACtE,SACF,CAAC;;QAED;QACAD,WAAW,CAAC;UACVC,QAAQ,EAAE,EAAE;UACZC,KAAK,EAAE,EAAE;UACTC,QAAQ,EAAE,EAAE;UACZC,eAAe,EAAE;QACnB,CAAC,CAAC;MACJ,CAAC,MAAM;QACL;QACAgB,WAAW,CAACgB,IAAI,CAACM,KAAK,IAAI,uBAAuB,EAAE,OAAO,CAAC;MAC7D;IACF,CAAC,CAAC,OAAOA,KAAK,EAAE;MACdtB,WAAW,CAAC,0DAA0D,EAAE,OAAO,CAAC;IAClF,CAAC,SAAS;MACRd,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;EAED,MAAMqC,UAAU,GAAGA,CAAA,KAAM;IACvB7C,UAAU,CAAC,CAACD,OAAO,CAAC;IACpBW,UAAU,CAAC,EAAE,CAAC;IACdR,WAAW,CAAC;MACVC,QAAQ,EAAE,EAAE;MACZC,KAAK,EAAE,EAAE;MACTC,QAAQ,EAAE,EAAE;MACZC,eAAe,EAAE;IACnB,CAAC,CAAC;EACJ,CAAC;EAED,IAAIV,IAAI,EAAE;IACR,oBACEJ,OAAA;MAAKsD,SAAS,EAAC,gBAAgB;MAAAC,QAAA,eAC7BvD,OAAA;QAAKsD,SAAS,EAAC,cAAc;QAAAC,QAAA,gBAC3BvD,OAAA;UAAKsD,SAAS,EAAC,gBAAgB;UAAAC,QAAA,gBAC7BvD,OAAA;YAAKsD,SAAS,EAAC,gBAAgB;YAAAC,QAAA,eAC7BvD,OAAA;cAAMsD,SAAS,EAAC,cAAc;cAAAC,QAAA,EAAC;YAAE;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACrC,CAAC,eACN3D,OAAA;YAAKsD,SAAS,EAAC,cAAc;YAAAC,QAAA,gBAC3BvD,OAAA;cAAIsD,SAAS,EAAC,cAAc;cAAAC,QAAA,gBAC1BvD,OAAA;gBAAMsD,SAAS,EAAC,eAAe;gBAAAC,QAAA,EAAC;cAAE;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,aAChC,EAACvD,IAAI,CAACO,QAAQ,EAAC,GAC1B;YAAA;cAAA6C,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACL3D,OAAA;cAAGsD,SAAS,EAAC,eAAe;cAAAC,QAAA,EAAEnD,IAAI,CAACQ;YAAK;cAAA4C,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC1C,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAEN3D,OAAA;UAAKsD,SAAS,EAAC,eAAe;UAAAC,QAAA,gBAC5BvD,OAAA;YAAKsD,SAAS,EAAC,WAAW;YAAAC,QAAA,gBACxBvD,OAAA;cAAMsD,SAAS,EAAC,WAAW;cAAAC,QAAA,EAAC;YAAE;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eACrC3D,OAAA;cAAKsD,SAAS,EAAC,cAAc;cAAAC,QAAA,gBAC3BvD,OAAA;gBAAAuD,QAAA,EAAI;cAAiB;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eAC1B3D,OAAA;gBAAAuD,QAAA,EAAG;cAAgB;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACpB,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eACN3D,OAAA;YAAKsD,SAAS,EAAC,WAAW;YAAAC,QAAA,gBACxBvD,OAAA;cAAMsD,SAAS,EAAC,WAAW;cAAAC,QAAA,EAAC;YAAC;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eACpC3D,OAAA;cAAKsD,SAAS,EAAC,cAAc;cAAAC,QAAA,gBAC3BvD,OAAA;gBAAAuD,QAAA,EAAI;cAAc;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACvB3D,OAAA;gBAAAuD,QAAA,EAAG;cAAa;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACjB,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAEN3D,OAAA;UAAKsD,SAAS,EAAC,iBAAiB;UAAAC,QAAA,gBAC9BvD,OAAA;YAAQsD,SAAS,EAAC,0BAA0B;YAAAC,QAAA,gBAC1CvD,OAAA;cAAMsD,SAAS,EAAC,UAAU;cAAAC,QAAA,EAAC;YAAE;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,kBAEtC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eACT3D,OAAA;YAAQsD,SAAS,EAAC,yBAAyB;YAAAC,QAAA,gBACzCvD,OAAA;cAAMsD,SAAS,EAAC,UAAU;cAAAC,QAAA,EAAC;YAAE;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,gBAEtC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAEV;EAEA,oBACE3D,OAAA;IAAKsD,SAAS,EAAC,gBAAgB;IAAAC,QAAA,eAC7BvD,OAAA;MAAKsD,SAAS,EAAC,mBAAmB;MAAAC,QAAA,gBAChCvD,OAAA;QAAKsD,SAAS,EAAC,aAAa;QAAAC,QAAA,gBAC1BvD,OAAA;UAAKsD,SAAS,EAAC,mBAAmB;UAAAC,QAAA,gBAChCvD,OAAA;YAAMsD,SAAS,EAAC,yBAAyB;YAAAC,QAAA,EAAC;UAAE;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eACnD3D,OAAA;YAAMsD,SAAS,EAAC,yBAAyB;YAAAC,QAAA,EAAC;UAAC;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eAClD3D,OAAA;YAAMsD,SAAS,EAAC,yBAAyB;YAAAC,QAAA,EAAC;UAAE;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAChD,CAAC,eACN3D,OAAA;UAAIsD,SAAS,EAAC,YAAY;UAAAC,QAAA,GACvBhD,OAAO,GAAG,eAAe,GAAG,8BAA8B,eAC3DP,OAAA;YAAMsD,SAAS,EAAC,kBAAkB;YAAAC,QAAA,EAC/BhD,OAAO,GAAG,IAAI,GAAG;UAAI;YAAAiD,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAClB,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACL,CAAC,eACL3D,OAAA;UAAGsD,SAAS,EAAC,eAAe;UAAAC,QAAA,EACzBhD,OAAO,GACJ,sDAAsD,GACtD;QAA6D;UAAAiD,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAEhE,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACD,CAAC,eAEN3D,OAAA;QAAM4D,QAAQ,EAAE1B,YAAa;QAACoB,SAAS,EAAC,WAAW;QAAAC,QAAA,GAChD,CAAChD,OAAO,iBACPP,OAAA;UAAKsD,SAAS,EAAC,YAAY;UAAAC,QAAA,gBACzBvD,OAAA;YAAOsD,SAAS,EAAC,YAAY;YAAAC,QAAA,gBAC3BvD,OAAA;cAAMsD,SAAS,EAAC,YAAY;cAAAC,QAAA,EAAC;YAAE;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,YAExC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC,eACR3D,OAAA;YACEgC,IAAI,EAAC,MAAM;YACXJ,IAAI,EAAC,UAAU;YACfC,KAAK,EAAEpB,QAAQ,CAACE,QAAS;YACzBkD,QAAQ,EAAEpC,iBAAkB;YAC5BqC,WAAW,EAAC,4BAA4B;YACxCR,SAAS,EAAC,YAAY;YACtBS,QAAQ,EAAE,CAACxD,OAAQ;YACnByD,QAAQ,EAAEjD;UAAQ;YAAAyC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACnB,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CACN,eAED3D,OAAA;UAAKsD,SAAS,EAAC,YAAY;UAAAC,QAAA,gBACzBvD,OAAA;YAAOsD,SAAS,EAAC,YAAY;YAAAC,QAAA,gBAC3BvD,OAAA;cAAMsD,SAAS,EAAC,YAAY;cAAAC,QAAA,EAAC;YAAE;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,SAExC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC,eACR3D,OAAA;YACEgC,IAAI,EAAC,OAAO;YACZJ,IAAI,EAAC,OAAO;YACZC,KAAK,EAAEpB,QAAQ,CAACG,KAAM;YACtBiD,QAAQ,EAAEpC,iBAAkB;YAC5BqC,WAAW,EAAC,wBAAwB;YACpCR,SAAS,EAAC,YAAY;YACtBS,QAAQ;YACRC,QAAQ,EAAEjD;UAAQ;YAAAyC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACnB,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC,eAEN3D,OAAA;UAAKsD,SAAS,EAAC,YAAY;UAAAC,QAAA,gBACzBvD,OAAA;YAAOsD,SAAS,EAAC,YAAY;YAAAC,QAAA,gBAC3BvD,OAAA;cAAMsD,SAAS,EAAC,YAAY;cAAAC,QAAA,EAAC;YAAE;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,YAExC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC,eACR3D,OAAA;YACEgC,IAAI,EAAC,UAAU;YACfJ,IAAI,EAAC,UAAU;YACfC,KAAK,EAAEpB,QAAQ,CAACI,QAAS;YACzBgD,QAAQ,EAAEpC,iBAAkB;YAC5BqC,WAAW,EAAEvD,OAAO,GAAG,qBAAqB,GAAG,0CAA2C;YAC1F+C,SAAS,EAAC,YAAY;YACtBS,QAAQ;YACRC,QAAQ,EAAEjD,OAAQ;YAClBkD,SAAS,EAAE1D,OAAO,GAAG2D,SAAS,GAAG;UAAE;YAAAV,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACpC,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC,EAEL,CAACpD,OAAO,iBACPP,OAAA;UAAKsD,SAAS,EAAC,YAAY;UAAAC,QAAA,gBACzBvD,OAAA;YAAOsD,SAAS,EAAC,YAAY;YAAAC,QAAA,gBAC3BvD,OAAA;cAAMsD,SAAS,EAAC,YAAY;cAAAC,QAAA,EAAC;YAAE;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,oBAExC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC,eACR3D,OAAA;YACEgC,IAAI,EAAC,UAAU;YACfJ,IAAI,EAAC,iBAAiB;YACtBC,KAAK,EAAEpB,QAAQ,CAACK,eAAgB;YAChC+C,QAAQ,EAAEpC,iBAAkB;YAC5BqC,WAAW,EAAC,uBAAuB;YACnCR,SAAS,EAAC,YAAY;YACtBS,QAAQ,EAAE,CAACxD,OAAQ;YACnByD,QAAQ,EAAEjD;UAAQ;YAAAyC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACnB,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CACN,EAEA1C,OAAO,iBACNjB,OAAA;UAAKsD,SAAS,EAAE,WAAWnC,WAAW,EAAG;UAAAoC,QAAA,gBACvCvD,OAAA;YAAMsD,SAAS,EAAC,cAAc;YAAAC,QAAA,EAC3BpC,WAAW,KAAK,SAAS,GAAG,GAAG,GAAG;UAAG;YAAAqC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAClC,CAAC,EACN1C,OAAO;QAAA;UAAAuC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACL,CACN,eAED3D,OAAA;UACEgC,IAAI,EAAC,QAAQ;UACbsB,SAAS,EAAC,iBAAiB;UAC3BU,QAAQ,EAAEjD,OAAQ;UAAAwC,QAAA,EAEjBxC,OAAO,gBACNf,OAAA,CAAAE,SAAA;YAAAqD,QAAA,gBACEvD,OAAA;cAAMsD,SAAS,EAAC;YAAS;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,EAChCpD,OAAO,GAAG,eAAe,GAAG,qBAAqB;UAAA,eAClD,CAAC,gBAEHP,OAAA,CAAAE,SAAA;YAAAqD,QAAA,gBACEvD,OAAA;cAAMsD,SAAS,EAAC,UAAU;cAAAC,QAAA,EACvBhD,OAAO,GAAG,IAAI,GAAG;YAAI;cAAAiD,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAClB,CAAC,EACNpD,OAAO,GAAG,SAAS,GAAG,gBAAgB;UAAA,eACvC;QACH;UAAAiD,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACK,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACL,CAAC,eAEP3D,OAAA;QAAKsD,SAAS,EAAC,aAAa;QAAAC,QAAA,gBAC1BvD,OAAA;UAAGsD,SAAS,EAAC,kBAAkB;UAAAC,QAAA,EAC5BhD,OAAO,GAAG,wBAAwB,GAAG;QAA0B;UAAAiD,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC/D,CAAC,eACJ3D,OAAA;UACEgC,IAAI,EAAC,QAAQ;UACbmC,OAAO,EAAEd,UAAW;UACpBC,SAAS,EAAC,iBAAiB;UAC3BU,QAAQ,EAAEjD,OAAQ;UAAAwC,QAAA,gBAElBvD,OAAA;YAAMsD,SAAS,EAAC,aAAa;YAAAC,QAAA,EAC1BhD,OAAO,GAAG,IAAI,GAAG;UAAI;YAAAiD,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAClB,CAAC,EACNpD,OAAO,GAAG,gBAAgB,GAAG,SAAS;QAAA;UAAAiD,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACjC,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAACrD,EAAA,CArTIH,QAAQ;AAAAiE,EAAA,GAARjE,QAAQ;AAuTd,eAAeA,QAAQ;AAAC,IAAAiE,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}