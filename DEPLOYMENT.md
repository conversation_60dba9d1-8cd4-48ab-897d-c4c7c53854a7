# Deployment Guide

This guide covers various deployment options for the AI Content Generator application.

## Quick Deployment Options

### Option 1: Local Development (Recommended for testing)

```powershell
# Run the deployment script
.\deploy.ps1 -Environment dev

# Or manually:
cd ai-generative/backend
npm install && npm start

# In another terminal:
cd ai-generative/client  
npm install && npm start
```

### Option 2: Docker Deployment (Recommended for production)

```bash
# 1. Set environment variables
cp .env.example .env
# Edit .env with your actual values

# 2. Build and run with Docker Compose
docker-compose up -d

# 3. Check status
docker-compose ps
docker-compose logs
```

### Option 3: Cloud Platform Deployment

#### Heroku Deployment

**Backend (API):**
```bash
# 1. Create Heroku app
heroku create your-app-name-api

# 2. Set environment variables
heroku config:set OPENAI_API_KEY=your_key
heroku config:set JWT_SECRET=your_secret
heroku config:set NODE_ENV=production

# 3. Deploy
git subtree push --prefix=ai-generative/backend heroku main
```

**Frontend:**
```bash
# 1. Build the app
cd ai-generative/client
npm run build

# 2. Deploy to Netlify, Vercel, or similar
# Upload the 'build' folder
```

#### AWS Deployment

**Backend (EC2/ECS):**
1. Create EC2 instance or ECS cluster
2. Install Docker and Docker Compose
3. Clone repository and run docker-compose
4. Configure security groups for ports 80, 443, 5000

**Frontend (S3 + CloudFront):**
1. Build the React app: `npm run build`
2. Upload build folder to S3 bucket
3. Configure CloudFront distribution
4. Set up custom domain with Route 53

#### Azure Deployment

**Backend (App Service):**
1. Create App Service for Node.js
2. Configure environment variables
3. Deploy using GitHub Actions or Azure CLI

**Frontend (Static Web Apps):**
1. Create Static Web App resource
2. Connect to GitHub repository
3. Configure build settings for React

## Environment Configuration

### Production Environment Variables

Create `.env` file in backend directory:

```env
# Required
OPENAI_API_KEY=sk-your-openai-api-key
JWT_SECRET=your-very-strong-secret-minimum-32-chars

# Optional
NODE_ENV=production
PORT=5000
FRONTEND_URL=https://your-frontend-domain.com
RATE_LIMIT=100
```

### Frontend Configuration

Update API endpoints in frontend for production:

```javascript
// src/config.js
const config = {
  API_BASE_URL: process.env.NODE_ENV === 'production' 
    ? 'https://your-api-domain.com'
    : 'http://localhost:5000'
};
```

## Security Considerations

### Backend Security
- Use strong JWT secrets (32+ characters)
- Enable CORS only for trusted domains
- Implement rate limiting
- Use HTTPS in production
- Keep dependencies updated
- Validate all inputs
- Use environment variables for secrets

### Frontend Security
- Build with production optimizations
- Use HTTPS
- Implement Content Security Policy
- Sanitize user inputs
- Keep dependencies updated

## Monitoring and Logging

### Application Monitoring
```javascript
// Add to backend for basic monitoring
app.use((req, res, next) => {
  console.log(`${new Date().toISOString()} - ${req.method} ${req.path}`);
  next();
});
```

### Health Checks
- Backend: `GET /` returns API status
- Docker: Built-in health check included
- Kubernetes: Configure liveness/readiness probes

### Log Management
- Use structured logging (JSON format)
- Centralize logs (ELK stack, CloudWatch, etc.)
- Set appropriate log levels
- Monitor error rates and response times

## Scaling Considerations

### Horizontal Scaling
- Use load balancer (nginx, AWS ALB, etc.)
- Implement session storage (Redis)
- Use database for persistent data
- Consider microservices architecture

### Performance Optimization
- Enable gzip compression
- Use CDN for static assets
- Implement caching strategies
- Optimize database queries
- Use connection pooling

## Backup and Recovery

### Data Backup
- Regular database backups
- Environment variable backups
- Code repository backups
- SSL certificate backups

### Disaster Recovery
- Multi-region deployment
- Automated failover
- Regular recovery testing
- Documentation of procedures

## Troubleshooting

### Common Issues

**Backend won't start:**
- Check environment variables
- Verify Node.js version (16+)
- Check port availability
- Review error logs

**Frontend build fails:**
- Clear node_modules and reinstall
- Check Node.js version
- Verify all dependencies
- Check for syntax errors

**API calls failing:**
- Verify CORS configuration
- Check API endpoints
- Validate authentication tokens
- Review network connectivity

**Docker issues:**
- Check Docker daemon status
- Verify Dockerfile syntax
- Check port mappings
- Review container logs

### Debug Commands

```bash
# Check application status
docker-compose ps
docker-compose logs backend
docker-compose logs frontend

# Backend debugging
cd ai-generative/backend
npm test
npm run dev

# Frontend debugging  
cd ai-generative/client
npm test
npm start
```

## Support

For deployment issues:
1. Check this documentation
2. Review application logs
3. Test locally first
4. Check environment variables
5. Verify network connectivity

## Updates and Maintenance

### Regular Maintenance
- Update dependencies monthly
- Monitor security advisories
- Review and rotate secrets
- Update documentation
- Test backup procedures

### Deployment Pipeline
Consider implementing CI/CD with:
- GitHub Actions
- GitLab CI
- Jenkins
- Azure DevOps
- AWS CodePipeline
