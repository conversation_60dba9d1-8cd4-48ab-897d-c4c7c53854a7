{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\vedika\\\\ai-generative\\\\client\\\\src\\\\components\\\\History.jsx\",\n  _s = $RefreshSig$();\nimport { useState, useEffect } from 'react';\nimport './History.css';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst History = ({\n  user,\n  onNavigate\n}) => {\n  _s();\n  const [history, setHistory] = useState([]);\n  const [loading, setLoading] = useState(false);\n  const [filter, setFilter] = useState('all');\n  const [sortBy, setSortBy] = useState('newest');\n\n  // Mock history data for demonstration (in real app, this would come from API)\n  const mockHistory = [{\n    id: 1,\n    contentType: 'blog',\n    tone: 'professional',\n    length: 300,\n    topic: 'AI in Healthcare',\n    content: 'Artificial Intelligence is revolutionizing healthcare by enabling faster diagnoses, personalized treatments, and improved patient outcomes. From medical imaging to drug discovery, AI technologies are transforming how we approach medicine...',\n    createdAt: new Date('2024-01-15T10:30:00'),\n    wordCount: 287\n  }, {\n    id: 2,\n    contentType: 'social',\n    tone: 'casual',\n    length: 100,\n    topic: 'Morning Motivation',\n    content: 'Good morning, creators! ☀️ Today is another chance to turn your ideas into reality. Remember, every expert was once a beginner. What amazing thing will you create today? #MondayMotivation #CreateDaily',\n    createdAt: new Date('2024-01-14T08:15:00'),\n    wordCount: 34\n  }, {\n    id: 3,\n    contentType: 'ad',\n    tone: 'friendly',\n    length: 150,\n    topic: 'Eco-Friendly Products',\n    content: 'Make a difference with every purchase! 🌱 Our eco-friendly products help you live sustainably without compromising on quality. Join thousands of happy customers who are making the planet greener, one choice at a time. Shop now and get 20% off your first order!',\n    createdAt: new Date('2024-01-13T16:45:00'),\n    wordCount: 48\n  }, {\n    id: 4,\n    contentType: 'blog',\n    tone: 'casual',\n    length: 500,\n    topic: 'Remote Work Tips',\n    content: 'Working from home can be amazing, but it definitely comes with its challenges! Here are some game-changing tips that have helped me stay productive and sane while working remotely...',\n    createdAt: new Date('2024-01-12T14:20:00'),\n    wordCount: 456\n  }];\n  useEffect(() => {\n    // Check for user in localStorage if prop is null\n    let currentUser = user;\n    if (!currentUser) {\n      const storedUserData = localStorage.getItem('userData');\n      if (storedUserData) {\n        try {\n          currentUser = JSON.parse(storedUserData);\n          console.log('History: Found user in localStorage:', currentUser);\n        } catch (error) {\n          console.error('Error parsing stored user data:', error);\n        }\n      }\n    }\n    console.log('History component - final user:', currentUser);\n\n    // Simulate loading\n    setLoading(true);\n    setTimeout(() => {\n      if (currentUser && currentUser.username) {\n        console.log('Setting mock history for user:', currentUser.username);\n        setHistory(mockHistory);\n      } else {\n        console.log('No user found, setting empty history');\n        setHistory([]);\n      }\n      setLoading(false);\n    }, 500);\n  }, [user]);\n  const getFilteredAndSortedHistory = () => {\n    let filtered = history;\n    if (filter !== 'all') {\n      filtered = history.filter(item => item.contentType === filter);\n    }\n    return filtered.sort((a, b) => {\n      if (sortBy === 'newest') {\n        return new Date(b.createdAt) - new Date(a.createdAt);\n      } else if (sortBy === 'oldest') {\n        return new Date(a.createdAt) - new Date(b.createdAt);\n      } else if (sortBy === 'longest') {\n        return b.wordCount - a.wordCount;\n      } else if (sortBy === 'shortest') {\n        return a.wordCount - b.wordCount;\n      }\n      return 0;\n    });\n  };\n  const formatDate = date => {\n    return new Date(date).toLocaleDateString('en-US', {\n      year: 'numeric',\n      month: 'short',\n      day: 'numeric',\n      hour: '2-digit',\n      minute: '2-digit'\n    });\n  };\n  const getContentTypeIcon = type => {\n    switch (type) {\n      case 'blog':\n        return '📝';\n      case 'social':\n        return '📱';\n      case 'ad':\n        return '📢';\n      default:\n        return '📄';\n    }\n  };\n  const getToneColor = tone => {\n    switch (tone) {\n      case 'professional':\n        return '#667eea';\n      case 'casual':\n        return '#ff6b6b';\n      case 'friendly':\n        return '#10b981';\n      default:\n        return '#64748b';\n    }\n  };\n  const copyToClipboard = async content => {\n    try {\n      await navigator.clipboard.writeText(content);\n      // You could add a toast notification here\n    } catch (err) {\n      console.error('Failed to copy text: ', err);\n    }\n  };\n\n  // Check user from prop or localStorage\n  const getCurrentUser = () => {\n    if (user && user.username) return user;\n    const storedUserData = localStorage.getItem('userData');\n    if (storedUserData) {\n      try {\n        const parsedUser = JSON.parse(storedUserData);\n        if (parsedUser && parsedUser.username) return parsedUser;\n      } catch (error) {\n        console.error('Error parsing stored user data:', error);\n      }\n    }\n    return null;\n  };\n  const currentUser = getCurrentUser();\n  if (!currentUser) {\n    console.log('History: No user found, showing demo with sign-up prompt');\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"history-container\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"demo-banner\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"banner-content\",\n          children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n            className: \"banner-title\",\n            children: [/*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"banner-icon\",\n              children: \"\\uD83C\\uDFAF\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 168,\n              columnNumber: 15\n            }, this), \"Preview Mode - Sign Up to Save Your Content!\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 167,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"banner-subtitle\",\n            children: \"Create an account to automatically save and manage all your generated content\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 171,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            className: \"banner-cta\",\n            onClick: () => window.location.hash = '#auth',\n            children: [/*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"cta-icon\",\n              children: \"\\uFFFD\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 178,\n              columnNumber: 15\n            }, this), \"Create Free Account\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 174,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 166,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 165,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"demo-content\",\n        children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n          className: \"demo-title\",\n          children: [/*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"demo-icon\",\n            children: \"\\uD83D\\uDC40\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 186,\n            columnNumber: 13\n          }, this), \"Here's what your history will look like:\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 185,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"history-grid\",\n          children: mockHistory.slice(0, 2).map(item => /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"history-card demo-card\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"demo-overlay\",\n              children: /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"demo-lock\",\n                children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"lock-icon\",\n                  children: \"\\uD83D\\uDD12\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 195,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"lock-text\",\n                  children: \"Sign up to unlock\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 196,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 194,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 193,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"card-header\",\n              children: /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"card-type\",\n                children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"type-icon\",\n                  children: getContentTypeIcon(item.contentType)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 202,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"type-text\",\n                  children: item.contentType\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 203,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 201,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 200,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"card-content\",\n              children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n                className: \"content-topic\",\n                children: item.topic\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 208,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"content-preview\",\n                children: item.content.length > 100 ? `${item.content.substring(0, 100)}...` : item.content\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 209,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 207,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"card-meta\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"meta-tags\",\n                children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"tone-tag\",\n                  style: {\n                    backgroundColor: getToneColor(item.tone)\n                  },\n                  children: item.tone\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 219,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"word-count\",\n                  children: [item.wordCount, \" words\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 225,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 218,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"meta-date\",\n                children: formatDate(item.createdAt)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 229,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 217,\n              columnNumber: 17\n            }, this)]\n          }, item.id, true, {\n            fileName: _jsxFileName,\n            lineNumber: 192,\n            columnNumber: 15\n          }, this))\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 190,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 184,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 164,\n      columnNumber: 7\n    }, this);\n  }\n  if (loading) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"history-container\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"history-loading\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"loading-spinner\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 245,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"loading-text\",\n          children: \"Loading your creative history...\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 246,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"loading-emojis\",\n          children: [/*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"loading-emoji\",\n            children: \"\\uD83D\\uDCDD\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 248,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"loading-emoji\",\n            children: \"\\u2728\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 249,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"loading-emoji\",\n            children: \"\\uD83D\\uDCDA\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 250,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 247,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 244,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 243,\n      columnNumber: 7\n    }, this);\n  }\n  const filteredHistory = getFilteredAndSortedHistory();\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"history-container\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"history-header\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"history-stats\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"stat-item\",\n          children: [/*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"stat-number\",\n            children: history.length\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 264,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"stat-label\",\n            children: \"Total Created\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 265,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 263,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"stat-item\",\n          children: [/*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"stat-number\",\n            children: history.reduce((sum, item) => sum + item.wordCount, 0)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 268,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"stat-label\",\n            children: \"Words Written\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 269,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 267,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"stat-item\",\n          children: [/*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"stat-number\",\n            children: new Set(history.map(item => item.contentType)).size\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 272,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"stat-label\",\n            children: \"Content Types\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 273,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 271,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 262,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"history-controls\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"filter-group\",\n          children: [/*#__PURE__*/_jsxDEV(\"label\", {\n            className: \"control-label\",\n            children: [/*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"label-icon\",\n              children: \"\\uD83C\\uDFAF\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 280,\n              columnNumber: 15\n            }, this), \"Filter by Type\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 279,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"select\", {\n            value: filter,\n            onChange: e => setFilter(e.target.value),\n            className: \"control-select\",\n            children: [/*#__PURE__*/_jsxDEV(\"option\", {\n              value: \"all\",\n              children: \"All Content\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 288,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n              value: \"blog\",\n              children: \"\\uD83D\\uDCDD Blog Posts\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 289,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n              value: \"social\",\n              children: \"\\uD83D\\uDCF1 Social Posts\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 290,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n              value: \"ad\",\n              children: \"\\uD83D\\uDCE2 Advertisements\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 291,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 283,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 278,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"filter-group\",\n          children: [/*#__PURE__*/_jsxDEV(\"label\", {\n            className: \"control-label\",\n            children: [/*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"label-icon\",\n              children: \"\\uD83D\\uDCCA\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 297,\n              columnNumber: 15\n            }, this), \"Sort by\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 296,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"select\", {\n            value: sortBy,\n            onChange: e => setSortBy(e.target.value),\n            className: \"control-select\",\n            children: [/*#__PURE__*/_jsxDEV(\"option\", {\n              value: \"newest\",\n              children: \"Newest First\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 305,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n              value: \"oldest\",\n              children: \"Oldest First\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 306,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n              value: \"longest\",\n              children: \"Longest First\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 307,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n              value: \"shortest\",\n              children: \"Shortest First\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 308,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 300,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 295,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 277,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 261,\n      columnNumber: 7\n    }, this), filteredHistory.length === 0 ? /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"history-empty\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"empty-illustration\",\n        children: /*#__PURE__*/_jsxDEV(\"span\", {\n          className: \"empty-emoji\",\n          children: \"\\uD83D\\uDCDD\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 317,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 316,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n        className: \"empty-title\",\n        children: \"No Content Yet\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 319,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n        className: \"empty-subtitle\",\n        children: \"Start creating amazing content to see your history here!\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 320,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 315,\n      columnNumber: 9\n    }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"history-grid\",\n      children: filteredHistory.map(item => /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"history-card\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"card-header\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"card-type\",\n            children: [/*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"type-icon\",\n              children: getContentTypeIcon(item.contentType)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 330,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"type-text\",\n              children: item.contentType\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 331,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 329,\n            columnNumber: 17\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"card-actions\",\n            children: /*#__PURE__*/_jsxDEV(\"button\", {\n              className: \"action-btn copy-btn\",\n              onClick: () => copyToClipboard(item.content),\n              title: \"Copy content\",\n              children: \"\\uD83D\\uDCCB\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 334,\n              columnNumber: 19\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 333,\n            columnNumber: 17\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 328,\n          columnNumber: 15\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"card-content\",\n          children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n            className: \"content-topic\",\n            children: item.topic\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 345,\n            columnNumber: 17\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"content-preview\",\n            children: item.content.length > 150 ? `${item.content.substring(0, 150)}...` : item.content\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 346,\n            columnNumber: 17\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 344,\n          columnNumber: 15\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"card-meta\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"meta-tags\",\n            children: [/*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"tone-tag\",\n              style: {\n                backgroundColor: getToneColor(item.tone)\n              },\n              children: item.tone\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 356,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"word-count\",\n              children: [item.wordCount, \" words\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 362,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 355,\n            columnNumber: 17\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"meta-date\",\n            children: formatDate(item.createdAt)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 366,\n            columnNumber: 17\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 354,\n          columnNumber: 15\n        }, this)]\n      }, item.id, true, {\n        fileName: _jsxFileName,\n        lineNumber: 327,\n        columnNumber: 13\n      }, this))\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 325,\n      columnNumber: 9\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 260,\n    columnNumber: 5\n  }, this);\n};\n_s(History, \"Ba6CSGJ3jwRC+rI8UFtpJwgMywQ=\");\n_c = History;\nexport default History;\nvar _c;\n$RefreshReg$(_c, \"History\");", "map": {"version": 3, "names": ["useState", "useEffect", "jsxDEV", "_jsxDEV", "History", "user", "onNavigate", "_s", "history", "setHistory", "loading", "setLoading", "filter", "setFilter", "sortBy", "setSortBy", "mockHistory", "id", "contentType", "tone", "length", "topic", "content", "createdAt", "Date", "wordCount", "currentUser", "storedUserData", "localStorage", "getItem", "JSON", "parse", "console", "log", "error", "setTimeout", "username", "getFilteredAndSortedHistory", "filtered", "item", "sort", "a", "b", "formatDate", "date", "toLocaleDateString", "year", "month", "day", "hour", "minute", "getContentTypeIcon", "type", "getToneColor", "copyToClipboard", "navigator", "clipboard", "writeText", "err", "getCurrentUser", "parsedUser", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "onClick", "window", "location", "hash", "slice", "map", "substring", "style", "backgroundColor", "filteredHistory", "reduce", "sum", "Set", "size", "value", "onChange", "e", "target", "title", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/vedika/ai-generative/client/src/components/History.jsx"], "sourcesContent": ["import { useState, useEffect } from 'react';\r\nimport './History.css';\r\n\r\nconst History = ({ user, onNavigate }) => {\r\n  const [history, setHistory] = useState([]);\r\n  const [loading, setLoading] = useState(false);\r\n  const [filter, setFilter] = useState('all');\r\n  const [sortBy, setSortBy] = useState('newest');\r\n\r\n  // Mock history data for demonstration (in real app, this would come from API)\r\n  const mockHistory = [\r\n    {\r\n      id: 1,\r\n      contentType: 'blog',\r\n      tone: 'professional',\r\n      length: 300,\r\n      topic: 'AI in Healthcare',\r\n      content: 'Artificial Intelligence is revolutionizing healthcare by enabling faster diagnoses, personalized treatments, and improved patient outcomes. From medical imaging to drug discovery, AI technologies are transforming how we approach medicine...',\r\n      createdAt: new Date('2024-01-15T10:30:00'),\r\n      wordCount: 287\r\n    },\r\n    {\r\n      id: 2,\r\n      contentType: 'social',\r\n      tone: 'casual',\r\n      length: 100,\r\n      topic: 'Morning Motivation',\r\n      content: 'Good morning, creators! ☀️ Today is another chance to turn your ideas into reality. Remember, every expert was once a beginner. What amazing thing will you create today? #MondayMotivation #CreateDaily',\r\n      createdAt: new Date('2024-01-14T08:15:00'),\r\n      wordCount: 34\r\n    },\r\n    {\r\n      id: 3,\r\n      contentType: 'ad',\r\n      tone: 'friendly',\r\n      length: 150,\r\n      topic: 'Eco-Friendly Products',\r\n      content: 'Make a difference with every purchase! 🌱 Our eco-friendly products help you live sustainably without compromising on quality. Join thousands of happy customers who are making the planet greener, one choice at a time. Shop now and get 20% off your first order!',\r\n      createdAt: new Date('2024-01-13T16:45:00'),\r\n      wordCount: 48\r\n    },\r\n    {\r\n      id: 4,\r\n      contentType: 'blog',\r\n      tone: 'casual',\r\n      length: 500,\r\n      topic: 'Remote Work Tips',\r\n      content: 'Working from home can be amazing, but it definitely comes with its challenges! Here are some game-changing tips that have helped me stay productive and sane while working remotely...',\r\n      createdAt: new Date('2024-01-12T14:20:00'),\r\n      wordCount: 456\r\n    }\r\n  ];\r\n\r\n  useEffect(() => {\r\n    // Check for user in localStorage if prop is null\r\n    let currentUser = user;\r\n    if (!currentUser) {\r\n      const storedUserData = localStorage.getItem('userData');\r\n      if (storedUserData) {\r\n        try {\r\n          currentUser = JSON.parse(storedUserData);\r\n          console.log('History: Found user in localStorage:', currentUser);\r\n        } catch (error) {\r\n          console.error('Error parsing stored user data:', error);\r\n        }\r\n      }\r\n    }\r\n\r\n    console.log('History component - final user:', currentUser);\r\n\r\n    // Simulate loading\r\n    setLoading(true);\r\n    setTimeout(() => {\r\n      if (currentUser && currentUser.username) {\r\n        console.log('Setting mock history for user:', currentUser.username);\r\n        setHistory(mockHistory);\r\n      } else {\r\n        console.log('No user found, setting empty history');\r\n        setHistory([]);\r\n      }\r\n      setLoading(false);\r\n    }, 500);\r\n  }, [user]);\r\n\r\n  const getFilteredAndSortedHistory = () => {\r\n    let filtered = history;\r\n\r\n    if (filter !== 'all') {\r\n      filtered = history.filter(item => item.contentType === filter);\r\n    }\r\n\r\n    return filtered.sort((a, b) => {\r\n      if (sortBy === 'newest') {\r\n        return new Date(b.createdAt) - new Date(a.createdAt);\r\n      } else if (sortBy === 'oldest') {\r\n        return new Date(a.createdAt) - new Date(b.createdAt);\r\n      } else if (sortBy === 'longest') {\r\n        return b.wordCount - a.wordCount;\r\n      } else if (sortBy === 'shortest') {\r\n        return a.wordCount - b.wordCount;\r\n      }\r\n      return 0;\r\n    });\r\n  };\r\n\r\n  const formatDate = (date) => {\r\n    return new Date(date).toLocaleDateString('en-US', {\r\n      year: 'numeric',\r\n      month: 'short',\r\n      day: 'numeric',\r\n      hour: '2-digit',\r\n      minute: '2-digit'\r\n    });\r\n  };\r\n\r\n  const getContentTypeIcon = (type) => {\r\n    switch (type) {\r\n      case 'blog': return '📝';\r\n      case 'social': return '📱';\r\n      case 'ad': return '📢';\r\n      default: return '📄';\r\n    }\r\n  };\r\n\r\n  const getToneColor = (tone) => {\r\n    switch (tone) {\r\n      case 'professional': return '#667eea';\r\n      case 'casual': return '#ff6b6b';\r\n      case 'friendly': return '#10b981';\r\n      default: return '#64748b';\r\n    }\r\n  };\r\n\r\n  const copyToClipboard = async (content) => {\r\n    try {\r\n      await navigator.clipboard.writeText(content);\r\n      // You could add a toast notification here\r\n    } catch (err) {\r\n      console.error('Failed to copy text: ', err);\r\n    }\r\n  };\r\n\r\n  // Check user from prop or localStorage\r\n  const getCurrentUser = () => {\r\n    if (user && user.username) return user;\r\n\r\n    const storedUserData = localStorage.getItem('userData');\r\n    if (storedUserData) {\r\n      try {\r\n        const parsedUser = JSON.parse(storedUserData);\r\n        if (parsedUser && parsedUser.username) return parsedUser;\r\n      } catch (error) {\r\n        console.error('Error parsing stored user data:', error);\r\n      }\r\n    }\r\n    return null;\r\n  };\r\n\r\n  const currentUser = getCurrentUser();\r\n\r\n  if (!currentUser) {\r\n    console.log('History: No user found, showing demo with sign-up prompt');\r\n    return (\r\n      <div className=\"history-container\">\r\n        <div className=\"demo-banner\">\r\n          <div className=\"banner-content\">\r\n            <h3 className=\"banner-title\">\r\n              <span className=\"banner-icon\">🎯</span>\r\n              Preview Mode - Sign Up to Save Your Content!\r\n            </h3>\r\n            <p className=\"banner-subtitle\">\r\n              Create an account to automatically save and manage all your generated content\r\n            </p>\r\n            <button\r\n              className=\"banner-cta\"\r\n              onClick={() => window.location.hash = '#auth'}\r\n            >\r\n              <span className=\"cta-icon\">�</span>\r\n              Create Free Account\r\n            </button>\r\n          </div>\r\n        </div>\r\n\r\n        <div className=\"demo-content\">\r\n          <h4 className=\"demo-title\">\r\n            <span className=\"demo-icon\">👀</span>\r\n            Here's what your history will look like:\r\n          </h4>\r\n\r\n          <div className=\"history-grid\">\r\n            {mockHistory.slice(0, 2).map((item) => (\r\n              <div key={item.id} className=\"history-card demo-card\">\r\n                <div className=\"demo-overlay\">\r\n                  <div className=\"demo-lock\">\r\n                    <span className=\"lock-icon\">🔒</span>\r\n                    <span className=\"lock-text\">Sign up to unlock</span>\r\n                  </div>\r\n                </div>\r\n\r\n                <div className=\"card-header\">\r\n                  <div className=\"card-type\">\r\n                    <span className=\"type-icon\">{getContentTypeIcon(item.contentType)}</span>\r\n                    <span className=\"type-text\">{item.contentType}</span>\r\n                  </div>\r\n                </div>\r\n\r\n                <div className=\"card-content\">\r\n                  <h4 className=\"content-topic\">{item.topic}</h4>\r\n                  <p className=\"content-preview\">\r\n                    {item.content.length > 100\r\n                      ? `${item.content.substring(0, 100)}...`\r\n                      : item.content\r\n                    }\r\n                  </p>\r\n                </div>\r\n\r\n                <div className=\"card-meta\">\r\n                  <div className=\"meta-tags\">\r\n                    <span\r\n                      className=\"tone-tag\"\r\n                      style={{ backgroundColor: getToneColor(item.tone) }}\r\n                    >\r\n                      {item.tone}\r\n                    </span>\r\n                    <span className=\"word-count\">\r\n                      {item.wordCount} words\r\n                    </span>\r\n                  </div>\r\n                  <div className=\"meta-date\">\r\n                    {formatDate(item.createdAt)}\r\n                  </div>\r\n                </div>\r\n              </div>\r\n            ))}\r\n          </div>\r\n        </div>\r\n      </div>\r\n    );\r\n  }\r\n\r\n  if (loading) {\r\n    return (\r\n      <div className=\"history-container\">\r\n        <div className=\"history-loading\">\r\n          <div className=\"loading-spinner\"></div>\r\n          <p className=\"loading-text\">Loading your creative history...</p>\r\n          <div className=\"loading-emojis\">\r\n            <span className=\"loading-emoji\">📝</span>\r\n            <span className=\"loading-emoji\">✨</span>\r\n            <span className=\"loading-emoji\">📚</span>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    );\r\n  }\r\n\r\n  const filteredHistory = getFilteredAndSortedHistory();\r\n\r\n  return (\r\n    <div className=\"history-container\">\r\n      <div className=\"history-header\">\r\n        <div className=\"history-stats\">\r\n          <div className=\"stat-item\">\r\n            <span className=\"stat-number\">{history.length}</span>\r\n            <span className=\"stat-label\">Total Created</span>\r\n          </div>\r\n          <div className=\"stat-item\">\r\n            <span className=\"stat-number\">{history.reduce((sum, item) => sum + item.wordCount, 0)}</span>\r\n            <span className=\"stat-label\">Words Written</span>\r\n          </div>\r\n          <div className=\"stat-item\">\r\n            <span className=\"stat-number\">{new Set(history.map(item => item.contentType)).size}</span>\r\n            <span className=\"stat-label\">Content Types</span>\r\n          </div>\r\n        </div>\r\n\r\n        <div className=\"history-controls\">\r\n          <div className=\"filter-group\">\r\n            <label className=\"control-label\">\r\n              <span className=\"label-icon\">🎯</span>\r\n              Filter by Type\r\n            </label>\r\n            <select\r\n              value={filter}\r\n              onChange={(e) => setFilter(e.target.value)}\r\n              className=\"control-select\"\r\n            >\r\n              <option value=\"all\">All Content</option>\r\n              <option value=\"blog\">📝 Blog Posts</option>\r\n              <option value=\"social\">📱 Social Posts</option>\r\n              <option value=\"ad\">📢 Advertisements</option>\r\n            </select>\r\n          </div>\r\n\r\n          <div className=\"filter-group\">\r\n            <label className=\"control-label\">\r\n              <span className=\"label-icon\">📊</span>\r\n              Sort by\r\n            </label>\r\n            <select\r\n              value={sortBy}\r\n              onChange={(e) => setSortBy(e.target.value)}\r\n              className=\"control-select\"\r\n            >\r\n              <option value=\"newest\">Newest First</option>\r\n              <option value=\"oldest\">Oldest First</option>\r\n              <option value=\"longest\">Longest First</option>\r\n              <option value=\"shortest\">Shortest First</option>\r\n            </select>\r\n          </div>\r\n        </div>\r\n      </div>\r\n\r\n      {filteredHistory.length === 0 ? (\r\n        <div className=\"history-empty\">\r\n          <div className=\"empty-illustration\">\r\n            <span className=\"empty-emoji\">📝</span>\r\n          </div>\r\n          <h3 className=\"empty-title\">No Content Yet</h3>\r\n          <p className=\"empty-subtitle\">\r\n            Start creating amazing content to see your history here!\r\n          </p>\r\n        </div>\r\n      ) : (\r\n        <div className=\"history-grid\">\r\n          {filteredHistory.map((item) => (\r\n            <div key={item.id} className=\"history-card\">\r\n              <div className=\"card-header\">\r\n                <div className=\"card-type\">\r\n                  <span className=\"type-icon\">{getContentTypeIcon(item.contentType)}</span>\r\n                  <span className=\"type-text\">{item.contentType}</span>\r\n                </div>\r\n                <div className=\"card-actions\">\r\n                  <button\r\n                    className=\"action-btn copy-btn\"\r\n                    onClick={() => copyToClipboard(item.content)}\r\n                    title=\"Copy content\"\r\n                  >\r\n                    📋\r\n                  </button>\r\n                </div>\r\n              </div>\r\n\r\n              <div className=\"card-content\">\r\n                <h4 className=\"content-topic\">{item.topic}</h4>\r\n                <p className=\"content-preview\">\r\n                  {item.content.length > 150\r\n                    ? `${item.content.substring(0, 150)}...`\r\n                    : item.content\r\n                  }\r\n                </p>\r\n              </div>\r\n\r\n              <div className=\"card-meta\">\r\n                <div className=\"meta-tags\">\r\n                  <span\r\n                    className=\"tone-tag\"\r\n                    style={{ backgroundColor: getToneColor(item.tone) }}\r\n                  >\r\n                    {item.tone}\r\n                  </span>\r\n                  <span className=\"word-count\">\r\n                    {item.wordCount} words\r\n                  </span>\r\n                </div>\r\n                <div className=\"meta-date\">\r\n                  {formatDate(item.createdAt)}\r\n                </div>\r\n              </div>\r\n            </div>\r\n          ))}\r\n        </div>\r\n      )}\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default History;"], "mappings": ";;AAAA,SAASA,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAC3C,OAAO,eAAe;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEvB,MAAMC,OAAO,GAAGA,CAAC;EAAEC,IAAI;EAAEC;AAAW,CAAC,KAAK;EAAAC,EAAA;EACxC,MAAM,CAACC,OAAO,EAAEC,UAAU,CAAC,GAAGT,QAAQ,CAAC,EAAE,CAAC;EAC1C,MAAM,CAACU,OAAO,EAAEC,UAAU,CAAC,GAAGX,QAAQ,CAAC,KAAK,CAAC;EAC7C,MAAM,CAACY,MAAM,EAAEC,SAAS,CAAC,GAAGb,QAAQ,CAAC,KAAK,CAAC;EAC3C,MAAM,CAACc,MAAM,EAAEC,SAAS,CAAC,GAAGf,QAAQ,CAAC,QAAQ,CAAC;;EAE9C;EACA,MAAMgB,WAAW,GAAG,CAClB;IACEC,EAAE,EAAE,CAAC;IACLC,WAAW,EAAE,MAAM;IACnBC,IAAI,EAAE,cAAc;IACpBC,MAAM,EAAE,GAAG;IACXC,KAAK,EAAE,kBAAkB;IACzBC,OAAO,EAAE,kPAAkP;IAC3PC,SAAS,EAAE,IAAIC,IAAI,CAAC,qBAAqB,CAAC;IAC1CC,SAAS,EAAE;EACb,CAAC,EACD;IACER,EAAE,EAAE,CAAC;IACLC,WAAW,EAAE,QAAQ;IACrBC,IAAI,EAAE,QAAQ;IACdC,MAAM,EAAE,GAAG;IACXC,KAAK,EAAE,oBAAoB;IAC3BC,OAAO,EAAE,0MAA0M;IACnNC,SAAS,EAAE,IAAIC,IAAI,CAAC,qBAAqB,CAAC;IAC1CC,SAAS,EAAE;EACb,CAAC,EACD;IACER,EAAE,EAAE,CAAC;IACLC,WAAW,EAAE,IAAI;IACjBC,IAAI,EAAE,UAAU;IAChBC,MAAM,EAAE,GAAG;IACXC,KAAK,EAAE,uBAAuB;IAC9BC,OAAO,EAAE,sQAAsQ;IAC/QC,SAAS,EAAE,IAAIC,IAAI,CAAC,qBAAqB,CAAC;IAC1CC,SAAS,EAAE;EACb,CAAC,EACD;IACER,EAAE,EAAE,CAAC;IACLC,WAAW,EAAE,MAAM;IACnBC,IAAI,EAAE,QAAQ;IACdC,MAAM,EAAE,GAAG;IACXC,KAAK,EAAE,kBAAkB;IACzBC,OAAO,EAAE,wLAAwL;IACjMC,SAAS,EAAE,IAAIC,IAAI,CAAC,qBAAqB,CAAC;IAC1CC,SAAS,EAAE;EACb,CAAC,CACF;EAEDxB,SAAS,CAAC,MAAM;IACd;IACA,IAAIyB,WAAW,GAAGrB,IAAI;IACtB,IAAI,CAACqB,WAAW,EAAE;MAChB,MAAMC,cAAc,GAAGC,YAAY,CAACC,OAAO,CAAC,UAAU,CAAC;MACvD,IAAIF,cAAc,EAAE;QAClB,IAAI;UACFD,WAAW,GAAGI,IAAI,CAACC,KAAK,CAACJ,cAAc,CAAC;UACxCK,OAAO,CAACC,GAAG,CAAC,sCAAsC,EAAEP,WAAW,CAAC;QAClE,CAAC,CAAC,OAAOQ,KAAK,EAAE;UACdF,OAAO,CAACE,KAAK,CAAC,iCAAiC,EAAEA,KAAK,CAAC;QACzD;MACF;IACF;IAEAF,OAAO,CAACC,GAAG,CAAC,iCAAiC,EAAEP,WAAW,CAAC;;IAE3D;IACAf,UAAU,CAAC,IAAI,CAAC;IAChBwB,UAAU,CAAC,MAAM;MACf,IAAIT,WAAW,IAAIA,WAAW,CAACU,QAAQ,EAAE;QACvCJ,OAAO,CAACC,GAAG,CAAC,gCAAgC,EAAEP,WAAW,CAACU,QAAQ,CAAC;QACnE3B,UAAU,CAACO,WAAW,CAAC;MACzB,CAAC,MAAM;QACLgB,OAAO,CAACC,GAAG,CAAC,sCAAsC,CAAC;QACnDxB,UAAU,CAAC,EAAE,CAAC;MAChB;MACAE,UAAU,CAAC,KAAK,CAAC;IACnB,CAAC,EAAE,GAAG,CAAC;EACT,CAAC,EAAE,CAACN,IAAI,CAAC,CAAC;EAEV,MAAMgC,2BAA2B,GAAGA,CAAA,KAAM;IACxC,IAAIC,QAAQ,GAAG9B,OAAO;IAEtB,IAAII,MAAM,KAAK,KAAK,EAAE;MACpB0B,QAAQ,GAAG9B,OAAO,CAACI,MAAM,CAAC2B,IAAI,IAAIA,IAAI,CAACrB,WAAW,KAAKN,MAAM,CAAC;IAChE;IAEA,OAAO0B,QAAQ,CAACE,IAAI,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAK;MAC7B,IAAI5B,MAAM,KAAK,QAAQ,EAAE;QACvB,OAAO,IAAIU,IAAI,CAACkB,CAAC,CAACnB,SAAS,CAAC,GAAG,IAAIC,IAAI,CAACiB,CAAC,CAAClB,SAAS,CAAC;MACtD,CAAC,MAAM,IAAIT,MAAM,KAAK,QAAQ,EAAE;QAC9B,OAAO,IAAIU,IAAI,CAACiB,CAAC,CAAClB,SAAS,CAAC,GAAG,IAAIC,IAAI,CAACkB,CAAC,CAACnB,SAAS,CAAC;MACtD,CAAC,MAAM,IAAIT,MAAM,KAAK,SAAS,EAAE;QAC/B,OAAO4B,CAAC,CAACjB,SAAS,GAAGgB,CAAC,CAAChB,SAAS;MAClC,CAAC,MAAM,IAAIX,MAAM,KAAK,UAAU,EAAE;QAChC,OAAO2B,CAAC,CAAChB,SAAS,GAAGiB,CAAC,CAACjB,SAAS;MAClC;MACA,OAAO,CAAC;IACV,CAAC,CAAC;EACJ,CAAC;EAED,MAAMkB,UAAU,GAAIC,IAAI,IAAK;IAC3B,OAAO,IAAIpB,IAAI,CAACoB,IAAI,CAAC,CAACC,kBAAkB,CAAC,OAAO,EAAE;MAChDC,IAAI,EAAE,SAAS;MACfC,KAAK,EAAE,OAAO;MACdC,GAAG,EAAE,SAAS;MACdC,IAAI,EAAE,SAAS;MACfC,MAAM,EAAE;IACV,CAAC,CAAC;EACJ,CAAC;EAED,MAAMC,kBAAkB,GAAIC,IAAI,IAAK;IACnC,QAAQA,IAAI;MACV,KAAK,MAAM;QAAE,OAAO,IAAI;MACxB,KAAK,QAAQ;QAAE,OAAO,IAAI;MAC1B,KAAK,IAAI;QAAE,OAAO,IAAI;MACtB;QAAS,OAAO,IAAI;IACtB;EACF,CAAC;EAED,MAAMC,YAAY,GAAIlC,IAAI,IAAK;IAC7B,QAAQA,IAAI;MACV,KAAK,cAAc;QAAE,OAAO,SAAS;MACrC,KAAK,QAAQ;QAAE,OAAO,SAAS;MAC/B,KAAK,UAAU;QAAE,OAAO,SAAS;MACjC;QAAS,OAAO,SAAS;IAC3B;EACF,CAAC;EAED,MAAMmC,eAAe,GAAG,MAAOhC,OAAO,IAAK;IACzC,IAAI;MACF,MAAMiC,SAAS,CAACC,SAAS,CAACC,SAAS,CAACnC,OAAO,CAAC;MAC5C;IACF,CAAC,CAAC,OAAOoC,GAAG,EAAE;MACZ1B,OAAO,CAACE,KAAK,CAAC,uBAAuB,EAAEwB,GAAG,CAAC;IAC7C;EACF,CAAC;;EAED;EACA,MAAMC,cAAc,GAAGA,CAAA,KAAM;IAC3B,IAAItD,IAAI,IAAIA,IAAI,CAAC+B,QAAQ,EAAE,OAAO/B,IAAI;IAEtC,MAAMsB,cAAc,GAAGC,YAAY,CAACC,OAAO,CAAC,UAAU,CAAC;IACvD,IAAIF,cAAc,EAAE;MAClB,IAAI;QACF,MAAMiC,UAAU,GAAG9B,IAAI,CAACC,KAAK,CAACJ,cAAc,CAAC;QAC7C,IAAIiC,UAAU,IAAIA,UAAU,CAACxB,QAAQ,EAAE,OAAOwB,UAAU;MAC1D,CAAC,CAAC,OAAO1B,KAAK,EAAE;QACdF,OAAO,CAACE,KAAK,CAAC,iCAAiC,EAAEA,KAAK,CAAC;MACzD;IACF;IACA,OAAO,IAAI;EACb,CAAC;EAED,MAAMR,WAAW,GAAGiC,cAAc,CAAC,CAAC;EAEpC,IAAI,CAACjC,WAAW,EAAE;IAChBM,OAAO,CAACC,GAAG,CAAC,0DAA0D,CAAC;IACvE,oBACE9B,OAAA;MAAK0D,SAAS,EAAC,mBAAmB;MAAAC,QAAA,gBAChC3D,OAAA;QAAK0D,SAAS,EAAC,aAAa;QAAAC,QAAA,eAC1B3D,OAAA;UAAK0D,SAAS,EAAC,gBAAgB;UAAAC,QAAA,gBAC7B3D,OAAA;YAAI0D,SAAS,EAAC,cAAc;YAAAC,QAAA,gBAC1B3D,OAAA;cAAM0D,SAAS,EAAC,aAAa;cAAAC,QAAA,EAAC;YAAE;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,gDAEzC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACL/D,OAAA;YAAG0D,SAAS,EAAC,iBAAiB;YAAAC,QAAA,EAAC;UAE/B;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC,eACJ/D,OAAA;YACE0D,SAAS,EAAC,YAAY;YACtBM,OAAO,EAAEA,CAAA,KAAMC,MAAM,CAACC,QAAQ,CAACC,IAAI,GAAG,OAAQ;YAAAR,QAAA,gBAE9C3D,OAAA;cAAM0D,SAAS,EAAC,UAAU;cAAAC,QAAA,EAAC;YAAC;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,uBAErC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAEN/D,OAAA;QAAK0D,SAAS,EAAC,cAAc;QAAAC,QAAA,gBAC3B3D,OAAA;UAAI0D,SAAS,EAAC,YAAY;UAAAC,QAAA,gBACxB3D,OAAA;YAAM0D,SAAS,EAAC,WAAW;YAAAC,QAAA,EAAC;UAAE;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,4CAEvC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eAEL/D,OAAA;UAAK0D,SAAS,EAAC,cAAc;UAAAC,QAAA,EAC1B9C,WAAW,CAACuD,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAACC,GAAG,CAAEjC,IAAI,iBAChCpC,OAAA;YAAmB0D,SAAS,EAAC,wBAAwB;YAAAC,QAAA,gBACnD3D,OAAA;cAAK0D,SAAS,EAAC,cAAc;cAAAC,QAAA,eAC3B3D,OAAA;gBAAK0D,SAAS,EAAC,WAAW;gBAAAC,QAAA,gBACxB3D,OAAA;kBAAM0D,SAAS,EAAC,WAAW;kBAAAC,QAAA,EAAC;gBAAE;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,eACrC/D,OAAA;kBAAM0D,SAAS,EAAC,WAAW;kBAAAC,QAAA,EAAC;gBAAiB;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACjD;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eAEN/D,OAAA;cAAK0D,SAAS,EAAC,aAAa;cAAAC,QAAA,eAC1B3D,OAAA;gBAAK0D,SAAS,EAAC,WAAW;gBAAAC,QAAA,gBACxB3D,OAAA;kBAAM0D,SAAS,EAAC,WAAW;kBAAAC,QAAA,EAAEX,kBAAkB,CAACZ,IAAI,CAACrB,WAAW;gBAAC;kBAAA6C,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eACzE/D,OAAA;kBAAM0D,SAAS,EAAC,WAAW;kBAAAC,QAAA,EAAEvB,IAAI,CAACrB;gBAAW;kBAAA6C,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAClD;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eAEN/D,OAAA;cAAK0D,SAAS,EAAC,cAAc;cAAAC,QAAA,gBAC3B3D,OAAA;gBAAI0D,SAAS,EAAC,eAAe;gBAAAC,QAAA,EAAEvB,IAAI,CAAClB;cAAK;gBAAA0C,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,eAC/C/D,OAAA;gBAAG0D,SAAS,EAAC,iBAAiB;gBAAAC,QAAA,EAC3BvB,IAAI,CAACjB,OAAO,CAACF,MAAM,GAAG,GAAG,GACtB,GAAGmB,IAAI,CAACjB,OAAO,CAACmD,SAAS,CAAC,CAAC,EAAE,GAAG,CAAC,KAAK,GACtClC,IAAI,CAACjB;cAAO;gBAAAyC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAEf,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACD,CAAC,eAEN/D,OAAA;cAAK0D,SAAS,EAAC,WAAW;cAAAC,QAAA,gBACxB3D,OAAA;gBAAK0D,SAAS,EAAC,WAAW;gBAAAC,QAAA,gBACxB3D,OAAA;kBACE0D,SAAS,EAAC,UAAU;kBACpBa,KAAK,EAAE;oBAAEC,eAAe,EAAEtB,YAAY,CAACd,IAAI,CAACpB,IAAI;kBAAE,CAAE;kBAAA2C,QAAA,EAEnDvB,IAAI,CAACpB;gBAAI;kBAAA4C,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACN,CAAC,eACP/D,OAAA;kBAAM0D,SAAS,EAAC,YAAY;kBAAAC,QAAA,GACzBvB,IAAI,CAACd,SAAS,EAAC,QAClB;gBAAA;kBAAAsC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACJ,CAAC,eACN/D,OAAA;gBAAK0D,SAAS,EAAC,WAAW;gBAAAC,QAAA,EACvBnB,UAAU,CAACJ,IAAI,CAAChB,SAAS;cAAC;gBAAAwC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACxB,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC;UAAA,GAxCE3B,IAAI,CAACtB,EAAE;YAAA8C,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAyCZ,CACN;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAEV;EAEA,IAAIxD,OAAO,EAAE;IACX,oBACEP,OAAA;MAAK0D,SAAS,EAAC,mBAAmB;MAAAC,QAAA,eAChC3D,OAAA;QAAK0D,SAAS,EAAC,iBAAiB;QAAAC,QAAA,gBAC9B3D,OAAA;UAAK0D,SAAS,EAAC;QAAiB;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,eACvC/D,OAAA;UAAG0D,SAAS,EAAC,cAAc;UAAAC,QAAA,EAAC;QAAgC;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC,eAChE/D,OAAA;UAAK0D,SAAS,EAAC,gBAAgB;UAAAC,QAAA,gBAC7B3D,OAAA;YAAM0D,SAAS,EAAC,eAAe;YAAAC,QAAA,EAAC;UAAE;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eACzC/D,OAAA;YAAM0D,SAAS,EAAC,eAAe;YAAAC,QAAA,EAAC;UAAC;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eACxC/D,OAAA;YAAM0D,SAAS,EAAC,eAAe;YAAAC,QAAA,EAAC;UAAE;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACtC,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAEV;EAEA,MAAMU,eAAe,GAAGvC,2BAA2B,CAAC,CAAC;EAErD,oBACElC,OAAA;IAAK0D,SAAS,EAAC,mBAAmB;IAAAC,QAAA,gBAChC3D,OAAA;MAAK0D,SAAS,EAAC,gBAAgB;MAAAC,QAAA,gBAC7B3D,OAAA;QAAK0D,SAAS,EAAC,eAAe;QAAAC,QAAA,gBAC5B3D,OAAA;UAAK0D,SAAS,EAAC,WAAW;UAAAC,QAAA,gBACxB3D,OAAA;YAAM0D,SAAS,EAAC,aAAa;YAAAC,QAAA,EAAEtD,OAAO,CAACY;UAAM;YAAA2C,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC,eACrD/D,OAAA;YAAM0D,SAAS,EAAC,YAAY;YAAAC,QAAA,EAAC;UAAa;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC9C,CAAC,eACN/D,OAAA;UAAK0D,SAAS,EAAC,WAAW;UAAAC,QAAA,gBACxB3D,OAAA;YAAM0D,SAAS,EAAC,aAAa;YAAAC,QAAA,EAAEtD,OAAO,CAACqE,MAAM,CAAC,CAACC,GAAG,EAAEvC,IAAI,KAAKuC,GAAG,GAAGvC,IAAI,CAACd,SAAS,EAAE,CAAC;UAAC;YAAAsC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC,eAC7F/D,OAAA;YAAM0D,SAAS,EAAC,YAAY;YAAAC,QAAA,EAAC;UAAa;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC9C,CAAC,eACN/D,OAAA;UAAK0D,SAAS,EAAC,WAAW;UAAAC,QAAA,gBACxB3D,OAAA;YAAM0D,SAAS,EAAC,aAAa;YAAAC,QAAA,EAAE,IAAIiB,GAAG,CAACvE,OAAO,CAACgE,GAAG,CAACjC,IAAI,IAAIA,IAAI,CAACrB,WAAW,CAAC,CAAC,CAAC8D;UAAI;YAAAjB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC,eAC1F/D,OAAA;YAAM0D,SAAS,EAAC,YAAY;YAAAC,QAAA,EAAC;UAAa;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC9C,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAEN/D,OAAA;QAAK0D,SAAS,EAAC,kBAAkB;QAAAC,QAAA,gBAC/B3D,OAAA;UAAK0D,SAAS,EAAC,cAAc;UAAAC,QAAA,gBAC3B3D,OAAA;YAAO0D,SAAS,EAAC,eAAe;YAAAC,QAAA,gBAC9B3D,OAAA;cAAM0D,SAAS,EAAC,YAAY;cAAAC,QAAA,EAAC;YAAE;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,kBAExC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC,eACR/D,OAAA;YACE8E,KAAK,EAAErE,MAAO;YACdsE,QAAQ,EAAGC,CAAC,IAAKtE,SAAS,CAACsE,CAAC,CAACC,MAAM,CAACH,KAAK,CAAE;YAC3CpB,SAAS,EAAC,gBAAgB;YAAAC,QAAA,gBAE1B3D,OAAA;cAAQ8E,KAAK,EAAC,KAAK;cAAAnB,QAAA,EAAC;YAAW;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eACxC/D,OAAA;cAAQ8E,KAAK,EAAC,MAAM;cAAAnB,QAAA,EAAC;YAAa;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eAC3C/D,OAAA;cAAQ8E,KAAK,EAAC,QAAQ;cAAAnB,QAAA,EAAC;YAAe;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eAC/C/D,OAAA;cAAQ8E,KAAK,EAAC,IAAI;cAAAnB,QAAA,EAAC;YAAiB;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACvC,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC,eAEN/D,OAAA;UAAK0D,SAAS,EAAC,cAAc;UAAAC,QAAA,gBAC3B3D,OAAA;YAAO0D,SAAS,EAAC,eAAe;YAAAC,QAAA,gBAC9B3D,OAAA;cAAM0D,SAAS,EAAC,YAAY;cAAAC,QAAA,EAAC;YAAE;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,WAExC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC,eACR/D,OAAA;YACE8E,KAAK,EAAEnE,MAAO;YACdoE,QAAQ,EAAGC,CAAC,IAAKpE,SAAS,CAACoE,CAAC,CAACC,MAAM,CAACH,KAAK,CAAE;YAC3CpB,SAAS,EAAC,gBAAgB;YAAAC,QAAA,gBAE1B3D,OAAA;cAAQ8E,KAAK,EAAC,QAAQ;cAAAnB,QAAA,EAAC;YAAY;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eAC5C/D,OAAA;cAAQ8E,KAAK,EAAC,QAAQ;cAAAnB,QAAA,EAAC;YAAY;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eAC5C/D,OAAA;cAAQ8E,KAAK,EAAC,SAAS;cAAAnB,QAAA,EAAC;YAAa;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eAC9C/D,OAAA;cAAQ8E,KAAK,EAAC,UAAU;cAAAnB,QAAA,EAAC;YAAc;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC1C,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,EAELU,eAAe,CAACxD,MAAM,KAAK,CAAC,gBAC3BjB,OAAA;MAAK0D,SAAS,EAAC,eAAe;MAAAC,QAAA,gBAC5B3D,OAAA;QAAK0D,SAAS,EAAC,oBAAoB;QAAAC,QAAA,eACjC3D,OAAA;UAAM0D,SAAS,EAAC,aAAa;UAAAC,QAAA,EAAC;QAAE;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACpC,CAAC,eACN/D,OAAA;QAAI0D,SAAS,EAAC,aAAa;QAAAC,QAAA,EAAC;MAAc;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eAC/C/D,OAAA;QAAG0D,SAAS,EAAC,gBAAgB;QAAAC,QAAA,EAAC;MAE9B;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAG,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACD,CAAC,gBAEN/D,OAAA;MAAK0D,SAAS,EAAC,cAAc;MAAAC,QAAA,EAC1Bc,eAAe,CAACJ,GAAG,CAAEjC,IAAI,iBACxBpC,OAAA;QAAmB0D,SAAS,EAAC,cAAc;QAAAC,QAAA,gBACzC3D,OAAA;UAAK0D,SAAS,EAAC,aAAa;UAAAC,QAAA,gBAC1B3D,OAAA;YAAK0D,SAAS,EAAC,WAAW;YAAAC,QAAA,gBACxB3D,OAAA;cAAM0D,SAAS,EAAC,WAAW;cAAAC,QAAA,EAAEX,kBAAkB,CAACZ,IAAI,CAACrB,WAAW;YAAC;cAAA6C,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eACzE/D,OAAA;cAAM0D,SAAS,EAAC,WAAW;cAAAC,QAAA,EAAEvB,IAAI,CAACrB;YAAW;cAAA6C,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAClD,CAAC,eACN/D,OAAA;YAAK0D,SAAS,EAAC,cAAc;YAAAC,QAAA,eAC3B3D,OAAA;cACE0D,SAAS,EAAC,qBAAqB;cAC/BM,OAAO,EAAEA,CAAA,KAAMb,eAAe,CAACf,IAAI,CAACjB,OAAO,CAAE;cAC7C+D,KAAK,EAAC,cAAc;cAAAvB,QAAA,EACrB;YAED;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAEN/D,OAAA;UAAK0D,SAAS,EAAC,cAAc;UAAAC,QAAA,gBAC3B3D,OAAA;YAAI0D,SAAS,EAAC,eAAe;YAAAC,QAAA,EAAEvB,IAAI,CAAClB;UAAK;YAAA0C,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC,eAC/C/D,OAAA;YAAG0D,SAAS,EAAC,iBAAiB;YAAAC,QAAA,EAC3BvB,IAAI,CAACjB,OAAO,CAACF,MAAM,GAAG,GAAG,GACtB,GAAGmB,IAAI,CAACjB,OAAO,CAACmD,SAAS,CAAC,CAAC,EAAE,GAAG,CAAC,KAAK,GACtClC,IAAI,CAACjB;UAAO;YAAAyC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAEf,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACD,CAAC,eAEN/D,OAAA;UAAK0D,SAAS,EAAC,WAAW;UAAAC,QAAA,gBACxB3D,OAAA;YAAK0D,SAAS,EAAC,WAAW;YAAAC,QAAA,gBACxB3D,OAAA;cACE0D,SAAS,EAAC,UAAU;cACpBa,KAAK,EAAE;gBAAEC,eAAe,EAAEtB,YAAY,CAACd,IAAI,CAACpB,IAAI;cAAE,CAAE;cAAA2C,QAAA,EAEnDvB,IAAI,CAACpB;YAAI;cAAA4C,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACN,CAAC,eACP/D,OAAA;cAAM0D,SAAS,EAAC,YAAY;cAAAC,QAAA,GACzBvB,IAAI,CAACd,SAAS,EAAC,QAClB;YAAA;cAAAsC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACJ,CAAC,eACN/D,OAAA;YAAK0D,SAAS,EAAC,WAAW;YAAAC,QAAA,EACvBnB,UAAU,CAACJ,IAAI,CAAChB,SAAS;UAAC;YAAAwC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACxB,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA,GA1CE3B,IAAI,CAACtB,EAAE;QAAA8C,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OA2CZ,CACN;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC,CACN;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACE,CAAC;AAEV,CAAC;AAAC3D,EAAA,CApXIH,OAAO;AAAAkF,EAAA,GAAPlF,OAAO;AAsXb,eAAeA,OAAO;AAAC,IAAAkF,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}