{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\vedika\\\\ai-generative\\\\client\\\\src\\\\components\\\\ToneSelector.jsx\";\nimport './ToneSelector.css';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst ToneSelector = ({\n  value,\n  onChange\n}) => {\n  const tones = [{\n    value: 'professional',\n    label: 'Professional',\n    icon: '👔',\n    description: 'Formal and business-like'\n  }, {\n    value: 'casual',\n    label: 'Casual',\n    icon: '😊',\n    description: 'Relaxed and friendly'\n  }, {\n    value: 'friendly',\n    label: 'Friendly',\n    icon: '🤗',\n    description: 'Warm and approachable'\n  }];\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"tone-selector\",\n    children: [/*#__PURE__*/_jsxDEV(\"label\", {\n      className: \"selector-label\",\n      children: [/*#__PURE__*/_jsxDEV(\"span\", {\n        className: \"label-icon\",\n        children: \"\\uD83C\\uDFAD\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 13,\n        columnNumber: 9\n      }, this), \"Writing Tone\"]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 12,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"tone-grid\",\n      children: tones.map(tone => /*#__PURE__*/_jsxDEV(\"button\", {\n        className: `tone-card ${value === tone.value ? 'active' : ''}`,\n        onClick: () => onChange && onChange(tone.value),\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"tone-icon\",\n          children: tone.icon\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 23,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"tone-content\",\n          children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n            className: \"tone-title\",\n            children: tone.label\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 25,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"tone-description\",\n            children: tone.description\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 26,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 24,\n          columnNumber: 13\n        }, this)]\n      }, tone.value, true, {\n        fileName: _jsxFileName,\n        lineNumber: 18,\n        columnNumber: 11\n      }, this))\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 16,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 11,\n    columnNumber: 5\n  }, this);\n};\n_c = ToneSelector;\nexport default ToneSelector;\nvar _c;\n$RefreshReg$(_c, \"ToneSelector\");", "map": {"version": 3, "names": ["jsxDEV", "_jsxDEV", "ToneSelector", "value", "onChange", "tones", "label", "icon", "description", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "map", "tone", "onClick", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/vedika/ai-generative/client/src/components/ToneSelector.jsx"], "sourcesContent": ["import './ToneSelector.css';\r\n\r\nconst ToneSelector = ({ value, onChange }) => {\r\n  const tones = [\r\n    { value: 'professional', label: 'Professional', icon: '👔', description: 'Formal and business-like' },\r\n    { value: 'casual', label: 'Casual', icon: '😊', description: 'Relaxed and friendly' },\r\n    { value: 'friendly', label: 'Friendly', icon: '🤗', description: 'Warm and approachable' }\r\n  ];\r\n\r\n  return (\r\n    <div className=\"tone-selector\">\r\n      <label className=\"selector-label\">\r\n        <span className=\"label-icon\">🎭</span>\r\n        Writing Tone\r\n      </label>\r\n      <div className=\"tone-grid\">\r\n        {tones.map((tone) => (\r\n          <button\r\n            key={tone.value}\r\n            className={`tone-card ${value === tone.value ? 'active' : ''}`}\r\n            onClick={() => onChange && onChange(tone.value)}\r\n          >\r\n            <div className=\"tone-icon\">{tone.icon}</div>\r\n            <div className=\"tone-content\">\r\n              <h4 className=\"tone-title\">{tone.label}</h4>\r\n              <p className=\"tone-description\">{tone.description}</p>\r\n            </div>\r\n          </button>\r\n        ))}\r\n      </div>\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default ToneSelector;"], "mappings": ";AAAA,OAAO,oBAAoB;AAAC,SAAAA,MAAA,IAAAC,OAAA;AAE5B,MAAMC,YAAY,GAAGA,CAAC;EAAEC,KAAK;EAAEC;AAAS,CAAC,KAAK;EAC5C,MAAMC,KAAK,GAAG,CACZ;IAAEF,KAAK,EAAE,cAAc;IAAEG,KAAK,EAAE,cAAc;IAAEC,IAAI,EAAE,IAAI;IAAEC,WAAW,EAAE;EAA2B,CAAC,EACrG;IAAEL,KAAK,EAAE,QAAQ;IAAEG,KAAK,EAAE,QAAQ;IAAEC,IAAI,EAAE,IAAI;IAAEC,WAAW,EAAE;EAAuB,CAAC,EACrF;IAAEL,KAAK,EAAE,UAAU;IAAEG,KAAK,EAAE,UAAU;IAAEC,IAAI,EAAE,IAAI;IAAEC,WAAW,EAAE;EAAwB,CAAC,CAC3F;EAED,oBACEP,OAAA;IAAKQ,SAAS,EAAC,eAAe;IAAAC,QAAA,gBAC5BT,OAAA;MAAOQ,SAAS,EAAC,gBAAgB;MAAAC,QAAA,gBAC/BT,OAAA;QAAMQ,SAAS,EAAC,YAAY;QAAAC,QAAA,EAAC;MAAE;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM,CAAC,gBAExC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAO,CAAC,eACRb,OAAA;MAAKQ,SAAS,EAAC,WAAW;MAAAC,QAAA,EACvBL,KAAK,CAACU,GAAG,CAAEC,IAAI,iBACdf,OAAA;QAEEQ,SAAS,EAAE,aAAaN,KAAK,KAAKa,IAAI,CAACb,KAAK,GAAG,QAAQ,GAAG,EAAE,EAAG;QAC/Dc,OAAO,EAAEA,CAAA,KAAMb,QAAQ,IAAIA,QAAQ,CAACY,IAAI,CAACb,KAAK,CAAE;QAAAO,QAAA,gBAEhDT,OAAA;UAAKQ,SAAS,EAAC,WAAW;UAAAC,QAAA,EAAEM,IAAI,CAACT;QAAI;UAAAI,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,eAC5Cb,OAAA;UAAKQ,SAAS,EAAC,cAAc;UAAAC,QAAA,gBAC3BT,OAAA;YAAIQ,SAAS,EAAC,YAAY;YAAAC,QAAA,EAAEM,IAAI,CAACV;UAAK;YAAAK,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC,eAC5Cb,OAAA;YAAGQ,SAAS,EAAC,kBAAkB;YAAAC,QAAA,EAAEM,IAAI,CAACR;UAAW;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACnD,CAAC;MAAA,GARDE,IAAI,CAACb,KAAK;QAAAQ,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAST,CACT;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAACI,EAAA,GA9BIhB,YAAY;AAgClB,eAAeA,YAAY;AAAC,IAAAgB,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}