.history-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 2rem;
}

.history-header {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  border-radius: 20px;
  padding: 2rem;
  margin-bottom: 2rem;
  box-shadow: 0 10px 40px rgba(0, 0, 0, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.history-stats {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 2rem;
  margin-bottom: 2rem;
}

.stat-item {
  text-align: center;
  padding: 1.5rem;
  background: linear-gradient(145deg, rgba(102, 126, 234, 0.1), rgba(118, 75, 162, 0.05));
  border-radius: 15px;
  border: 2px solid rgba(102, 126, 234, 0.2);
  transition: all 0.3s ease;
}

.stat-item:hover {
  transform: translateY(-5px);
  box-shadow: 0 15px 30px rgba(102, 126, 234, 0.2);
  border-color: #667eea;
}

.stat-number {
  display: block;
  font-size: 2.5rem;
  font-weight: 900;
  background: linear-gradient(135deg, #667eea, #764ba2);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  margin-bottom: 0.5rem;
}

.stat-label {
  font-size: 0.9rem;
  color: #64748b;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.history-controls {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 2rem;
}

.filter-group {
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
}

.control-label {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-weight: 600;
  color: #1e293b;
  font-size: 0.95rem;
}

.label-icon {
  font-size: 1.1rem;
  animation: wiggle 3s ease-in-out infinite;
}

.control-select {
  padding: 0.75rem 1rem;
  border: 2px solid #e2e8f0;
  border-radius: 10px;
  background: white;
  font-size: 0.95rem;
  color: #1e293b;
  cursor: pointer;
  transition: all 0.3s ease;
}

.control-select:focus {
  outline: none;
  border-color: #667eea;
  box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

.history-loading {
  text-align: center;
  padding: 4rem 2rem;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  border-radius: 20px;
  box-shadow: 0 10px 40px rgba(0, 0, 0, 0.1);
}

.loading-spinner {
  width: 60px;
  height: 60px;
  border: 4px solid #e2e8f0;
  border-top: 4px solid #667eea;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin: 0 auto 2rem;
}

.loading-text {
  font-size: 1.2rem;
  color: #667eea;
  font-weight: 600;
  margin-bottom: 1rem;
}

.loading-emojis {
  display: flex;
  justify-content: center;
  gap: 1rem;
}

.loading-emoji {
  font-size: 1.5rem;
  animation: bounce 1.5s ease-in-out infinite;
}

.loading-emoji:nth-child(1) { animation-delay: 0s; }
.loading-emoji:nth-child(2) { animation-delay: 0.3s; }
.loading-emoji:nth-child(3) { animation-delay: 0.6s; }

.history-empty {
  text-align: center;
  padding: 4rem 2rem;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  border-radius: 20px;
  box-shadow: 0 10px 40px rgba(0, 0, 0, 0.1);
}

.empty-illustration {
  margin-bottom: 2rem;
}

.empty-emoji {
  font-size: 4rem;
  animation: float 3s ease-in-out infinite;
  filter: drop-shadow(0 4px 8px rgba(0, 0, 0, 0.1));
}

.empty-title {
  font-size: 1.8rem;
  font-weight: 700;
  color: #1e293b;
  margin-bottom: 1rem;
}

.empty-subtitle {
  font-size: 1.1rem;
  color: #64748b;
  max-width: 400px;
  margin: 0 auto;
  line-height: 1.6;
}

.history-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
  gap: 2rem;
}

.history-card {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  border-radius: 20px;
  padding: 2rem;
  box-shadow: 0 10px 40px rgba(0, 0, 0, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.history-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(102, 126, 234, 0.1), transparent);
  transition: left 0.5s ease;
}

.history-card:hover::before {
  left: 100%;
}

.history-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 20px 60px rgba(0, 0, 0, 0.15);
  border-color: #667eea;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1.5rem;
}

.card-type {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  background: linear-gradient(135deg, #667eea, #764ba2);
  color: white;
  padding: 0.5rem 1rem;
  border-radius: 20px;
  font-size: 0.9rem;
  font-weight: 600;
  text-transform: capitalize;
}

.type-icon {
  font-size: 1.1rem;
}

.card-actions {
  display: flex;
  gap: 0.5rem;
}

.action-btn {
  width: 40px;
  height: 40px;
  border: none;
  border-radius: 10px;
  background: rgba(102, 126, 234, 0.1);
  color: #667eea;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1.1rem;
}

.action-btn:hover {
  background: #667eea;
  color: white;
  transform: scale(1.1);
}

.card-content {
  margin-bottom: 1.5rem;
}

.content-topic {
  font-size: 1.2rem;
  font-weight: 700;
  color: #1e293b;
  margin-bottom: 1rem;
  line-height: 1.3;
}

.content-preview {
  color: #64748b;
  line-height: 1.6;
  font-size: 0.95rem;
}

.card-meta {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding-top: 1rem;
  border-top: 1px solid #e2e8f0;
}

.meta-tags {
  display: flex;
  gap: 0.75rem;
  align-items: center;
}

.tone-tag {
  color: white;
  padding: 0.25rem 0.75rem;
  border-radius: 12px;
  font-size: 0.8rem;
  font-weight: 600;
  text-transform: capitalize;
}

.word-count {
  background: rgba(100, 116, 139, 0.1);
  color: #64748b;
  padding: 0.25rem 0.75rem;
  border-radius: 12px;
  font-size: 0.8rem;
  font-weight: 600;
}

.meta-date {
  font-size: 0.85rem;
  color: #94a3b8;
  font-weight: 500;
}

/* Responsive Design */
@media (max-width: 768px) {
  .history-container {
    padding: 1rem;
  }

  .history-stats {
    grid-template-columns: 1fr;
    gap: 1rem;
  }

  .history-controls {
    grid-template-columns: 1fr;
    gap: 1rem;
  }

  .history-grid {
    grid-template-columns: 1fr;
    gap: 1rem;
  }

  .history-card {
    padding: 1.5rem;
  }

  .card-meta {
    flex-direction: column;
    gap: 1rem;
    align-items: flex-start;
  }
}

/* Animation Keyframes */
@keyframes spin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

@keyframes bounce {
  0%, 100% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-10px);
  }
}

@keyframes float {
  0%, 100% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-15px);
  }
}

@keyframes wiggle {
  0%, 100% {
    transform: rotate(0deg);
  }
  25% {
    transform: rotate(3deg);
  }
  75% {
    transform: rotate(-3deg);
  }
}

/* Profile Badge Animations */
@keyframes badgeGlow {
  0%, 100% {
    box-shadow: 0 4px 15px rgba(16, 185, 129, 0.3);
  }
  50% {
    box-shadow: 0 6px 25px rgba(16, 185, 129, 0.6);
  }
}

@keyframes badgeSpark {
  0%, 100% {
    transform: scale(1);
    filter: drop-shadow(0 0 5px rgba(255, 255, 255, 0.8));
  }
  50% {
    transform: scale(1.2);
    filter: drop-shadow(0 0 15px rgba(255, 255, 255, 1));
  }
}

@keyframes avatarPulse {
  0%, 100% {
    transform: scale(1);
    box-shadow:
      0 15px 40px rgba(102, 126, 234, 0.4),
      0 0 0 4px rgba(255, 255, 255, 0.3),
      inset 0 2px 10px rgba(255, 255, 255, 0.2);
  }
  50% {
    transform: scale(1.05);
    box-shadow:
      0 20px 50px rgba(102, 126, 234, 0.6),
      0 0 0 6px rgba(255, 255, 255, 0.4),
      inset 0 2px 10px rgba(255, 255, 255, 0.3);
  }
}

@keyframes avatarSpin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

@keyframes celebrationFloat {
  0%, 100% {
    transform: translateY(0px) rotate(0deg);
    opacity: 0.7;
  }
  33% {
    transform: translateY(-20px) rotate(120deg);
    opacity: 1;
  }
  66% {
    transform: translateY(-10px) rotate(240deg);
    opacity: 0.8;
  }
}

@keyframes gradientShift {
  0%, 100% {
    background-position: 0% 50%;
  }
  50% {
    background-position: 100% 50%;
  }
}

@keyframes ctaRocket {
  0%, 100% {
    transform: scale(1) rotate(0deg);
  }
  25% {
    transform: scale(1.1) rotate(5deg);
  }
  50% {
    transform: scale(1.05) rotate(0deg);
  }
  75% {
    transform: scale(1.1) rotate(-5deg);
  }
}

@keyframes shimmer {
  0% {
    left: -100%;
  }
  100% {
    left: 100%;
  }
}
