{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\vedika\\\\ai-generative\\\\client\\\\src\\\\components\\\\ContentTypeSelector.jsx\";\nimport React from 'react';\nimport './ContentTypeSelector.css';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst ContentTypeSelector = ({\n  value,\n  onChange\n}) => {\n  const contentTypes = [{\n    value: 'blog',\n    label: 'Blog Post',\n    icon: '📝',\n    description: 'Long-form articles and posts'\n  }, {\n    value: 'social',\n    label: 'Social Post',\n    icon: '📱',\n    description: 'Social media content'\n  }, {\n    value: 'ad',\n    label: 'Advertisement',\n    icon: '📢',\n    description: 'Marketing and promotional copy'\n  }];\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"content-type-selector\",\n    children: [/*#__PURE__*/_jsxDEV(\"label\", {\n      className: \"selector-label\",\n      children: [/*#__PURE__*/_jsxDEV(\"span\", {\n        className: \"label-icon\",\n        children: \"\\uD83C\\uDFAF\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 14,\n        columnNumber: 9\n      }, this), \"Content Type\"]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 13,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"content-type-grid\",\n      children: contentTypes.map(type => /*#__PURE__*/_jsxDEV(\"button\", {\n        className: `content-type-card ${value === type.value ? 'active' : ''}`,\n        onClick: () => onChange && onChange(type.value),\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"card-icon\",\n          children: type.icon\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 24,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"card-content\",\n          children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n            className: \"card-title\",\n            children: type.label\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 26,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"card-description\",\n            children: type.description\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 27,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 25,\n          columnNumber: 13\n        }, this)]\n      }, type.value, true, {\n        fileName: _jsxFileName,\n        lineNumber: 19,\n        columnNumber: 11\n      }, this))\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 17,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 12,\n    columnNumber: 5\n  }, this);\n};\n_c = ContentTypeSelector;\nexport default ContentTypeSelector;\nvar _c;\n$RefreshReg$(_c, \"ContentTypeSelector\");", "map": {"version": 3, "names": ["React", "jsxDEV", "_jsxDEV", "ContentTypeSelector", "value", "onChange", "contentTypes", "label", "icon", "description", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "map", "type", "onClick", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/vedika/ai-generative/client/src/components/ContentTypeSelector.jsx"], "sourcesContent": ["import React from 'react';\r\nimport './ContentTypeSelector.css';\r\n\r\nconst ContentTypeSelector = ({ value, onChange }) => {\r\n  const contentTypes = [\r\n    { value: 'blog', label: 'Blog Post', icon: '📝', description: 'Long-form articles and posts' },\r\n    { value: 'social', label: 'Social Post', icon: '📱', description: 'Social media content' },\r\n    { value: 'ad', label: 'Advertisement', icon: '📢', description: 'Marketing and promotional copy' }\r\n  ];\r\n\r\n  return (\r\n    <div className=\"content-type-selector\">\r\n      <label className=\"selector-label\">\r\n        <span className=\"label-icon\">🎯</span>\r\n        Content Type\r\n      </label>\r\n      <div className=\"content-type-grid\">\r\n        {contentTypes.map((type) => (\r\n          <button\r\n            key={type.value}\r\n            className={`content-type-card ${value === type.value ? 'active' : ''}`}\r\n            onClick={() => onChange && onChange(type.value)}\r\n          >\r\n            <div className=\"card-icon\">{type.icon}</div>\r\n            <div className=\"card-content\">\r\n              <h4 className=\"card-title\">{type.label}</h4>\r\n              <p className=\"card-description\">{type.description}</p>\r\n            </div>\r\n          </button>\r\n        ))}\r\n      </div>\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default ContentTypeSelector;"], "mappings": ";AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,OAAO,2BAA2B;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEnC,MAAMC,mBAAmB,GAAGA,CAAC;EAAEC,KAAK;EAAEC;AAAS,CAAC,KAAK;EACnD,MAAMC,YAAY,GAAG,CACnB;IAAEF,KAAK,EAAE,MAAM;IAAEG,KAAK,EAAE,WAAW;IAAEC,IAAI,EAAE,IAAI;IAAEC,WAAW,EAAE;EAA+B,CAAC,EAC9F;IAAEL,KAAK,EAAE,QAAQ;IAAEG,KAAK,EAAE,aAAa;IAAEC,IAAI,EAAE,IAAI;IAAEC,WAAW,EAAE;EAAuB,CAAC,EAC1F;IAAEL,KAAK,EAAE,IAAI;IAAEG,KAAK,EAAE,eAAe;IAAEC,IAAI,EAAE,IAAI;IAAEC,WAAW,EAAE;EAAiC,CAAC,CACnG;EAED,oBACEP,OAAA;IAAKQ,SAAS,EAAC,uBAAuB;IAAAC,QAAA,gBACpCT,OAAA;MAAOQ,SAAS,EAAC,gBAAgB;MAAAC,QAAA,gBAC/BT,OAAA;QAAMQ,SAAS,EAAC,YAAY;QAAAC,QAAA,EAAC;MAAE;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM,CAAC,gBAExC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAO,CAAC,eACRb,OAAA;MAAKQ,SAAS,EAAC,mBAAmB;MAAAC,QAAA,EAC/BL,YAAY,CAACU,GAAG,CAAEC,IAAI,iBACrBf,OAAA;QAEEQ,SAAS,EAAE,qBAAqBN,KAAK,KAAKa,IAAI,CAACb,KAAK,GAAG,QAAQ,GAAG,EAAE,EAAG;QACvEc,OAAO,EAAEA,CAAA,KAAMb,QAAQ,IAAIA,QAAQ,CAACY,IAAI,CAACb,KAAK,CAAE;QAAAO,QAAA,gBAEhDT,OAAA;UAAKQ,SAAS,EAAC,WAAW;UAAAC,QAAA,EAAEM,IAAI,CAACT;QAAI;UAAAI,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,eAC5Cb,OAAA;UAAKQ,SAAS,EAAC,cAAc;UAAAC,QAAA,gBAC3BT,OAAA;YAAIQ,SAAS,EAAC,YAAY;YAAAC,QAAA,EAAEM,IAAI,CAACV;UAAK;YAAAK,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC,eAC5Cb,OAAA;YAAGQ,SAAS,EAAC,kBAAkB;YAAAC,QAAA,EAAEM,IAAI,CAACR;UAAW;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACnD,CAAC;MAAA,GARDE,IAAI,CAACb,KAAK;QAAAQ,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAST,CACT;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAACI,EAAA,GA9BIhB,mBAAmB;AAgCzB,eAAeA,mBAAmB;AAAC,IAAAgB,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}