.content-type-selector {
  margin-bottom: 1.5rem;
}

.selector-label {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-size: 1.1rem;
  font-weight: 600;
  color: #1e293b;
  margin-bottom: 1rem;
}

.label-icon {
  font-size: 1.3rem;
  animation: bounce 2s ease-in-out infinite;
}

.content-type-grid {
  display: grid;
  grid-template-columns: 1fr;
  gap: 0.75rem;
}

.content-type-card {
  display: flex;
  align-items: center;
  gap: 1rem;
  padding: 1rem;
  background: white;
  border: 2px solid #e2e8f0;
  border-radius: 12px;
  cursor: pointer;
  transition: all 0.3s ease;
  text-align: left;
  position: relative;
  overflow: hidden;
}

.content-type-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(102, 126, 234, 0.1), transparent);
  transition: left 0.5s ease;
}

.content-type-card:hover::before {
  left: 100%;
}

.content-type-card:hover {
  border-color: #667eea;
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(102, 126, 234, 0.15);
}

.content-type-card.active {
  border-color: #667eea;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  transform: scale(1.02);
  box-shadow: 0 10px 30px rgba(102, 126, 234, 0.3);
}

.content-type-card.active .card-title,
.content-type-card.active .card-description {
  color: white;
}

.card-icon {
  font-size: 2.5rem;
  animation: wiggle 3s ease-in-out infinite;
  filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.1));
}

.content-type-card.active .card-icon {
  animation: sparkle 2s ease-in-out infinite;
  filter: drop-shadow(0 0 10px rgba(255, 255, 255, 0.5));
}

.card-content {
  flex: 1;
}

.card-title {
  font-size: 1.1rem;
  font-weight: 600;
  color: #1e293b;
  margin-bottom: 0.25rem;
  transition: color 0.3s ease;
}

.card-description {
  font-size: 0.9rem;
  color: #64748b;
  transition: color 0.3s ease;
}

/* Cartoon decorations */
.content-type-card::after {
  content: '✨';
  position: absolute;
  top: 8px;
  right: 8px;
  font-size: 0.8rem;
  opacity: 0;
  transition: opacity 0.3s ease;
  animation: twinkle 2s ease-in-out infinite;
}

.content-type-card.active::after {
  opacity: 1;
}

/* Responsive design */
@media (min-width: 768px) {
  .content-type-grid {
    grid-template-columns: repeat(3, 1fr);
  }
  
  .content-type-card {
    flex-direction: column;
    text-align: center;
    padding: 1.5rem 1rem;
  }
  
  .card-icon {
    font-size: 3rem;
    margin-bottom: 0.5rem;
  }
}

/* Animation keyframes */
@keyframes wiggle {
  0%, 100% {
    transform: rotate(0deg);
  }
  25% {
    transform: rotate(3deg);
  }
  75% {
    transform: rotate(-3deg);
  }
}

@keyframes sparkle {
  0%, 100% {
    transform: scale(1) rotate(0deg);
  }
  50% {
    transform: scale(1.1) rotate(180deg);
  }
}

@keyframes twinkle {
  0%, 100% {
    opacity: 0.3;
    transform: scale(0.8);
  }
  50% {
    opacity: 1;
    transform: scale(1.2);
  }
}

@keyframes bounce {
  0%, 100% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-5px);
  }
}
