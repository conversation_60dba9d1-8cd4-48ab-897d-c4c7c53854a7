{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\vedika\\\\ai-generative\\\\client\\\\src\\\\components\\\\History.jsx\",\n  _s = $RefreshSig$();\nimport { useState, useEffect } from 'react';\nimport './History.css';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst History = ({\n  user\n}) => {\n  _s();\n  const [history, setHistory] = useState([]);\n  const [loading, setLoading] = useState(false);\n  const [filter, setFilter] = useState('all');\n  const [sortBy, setSortBy] = useState('newest');\n\n  // Mock history data for demonstration (in real app, this would come from API)\n  const mockHistory = [{\n    id: 1,\n    contentType: 'blog',\n    tone: 'professional',\n    length: 300,\n    topic: 'AI in Healthcare',\n    content: 'Artificial Intelligence is revolutionizing healthcare by enabling faster diagnoses, personalized treatments, and improved patient outcomes. From medical imaging to drug discovery, AI technologies are transforming how we approach medicine...',\n    createdAt: new Date('2024-01-15T10:30:00'),\n    wordCount: 287\n  }, {\n    id: 2,\n    contentType: 'social',\n    tone: 'casual',\n    length: 100,\n    topic: 'Morning Motivation',\n    content: 'Good morning, creators! ☀️ Today is another chance to turn your ideas into reality. Remember, every expert was once a beginner. What amazing thing will you create today? #MondayMotivation #CreateDaily',\n    createdAt: new Date('2024-01-14T08:15:00'),\n    wordCount: 34\n  }, {\n    id: 3,\n    contentType: 'ad',\n    tone: 'friendly',\n    length: 150,\n    topic: 'Eco-Friendly Products',\n    content: 'Make a difference with every purchase! 🌱 Our eco-friendly products help you live sustainably without compromising on quality. Join thousands of happy customers who are making the planet greener, one choice at a time. Shop now and get 20% off your first order!',\n    createdAt: new Date('2024-01-13T16:45:00'),\n    wordCount: 48\n  }, {\n    id: 4,\n    contentType: 'blog',\n    tone: 'casual',\n    length: 500,\n    topic: 'Remote Work Tips',\n    content: 'Working from home can be amazing, but it definitely comes with its challenges! Here are some game-changing tips that have helped me stay productive and sane while working remotely...',\n    createdAt: new Date('2024-01-12T14:20:00'),\n    wordCount: 456\n  }];\n  useEffect(() => {\n    // Debug logging\n    console.log('History component - user prop:', user);\n    console.log('History component - user type:', typeof user);\n\n    // Simulate loading\n    setLoading(true);\n    setTimeout(() => {\n      if (user && user.username) {\n        console.log('Setting mock history for user:', user.username);\n        setHistory(mockHistory);\n      } else {\n        console.log('No user found, setting empty history');\n        setHistory([]);\n      }\n      setLoading(false);\n    }, 500); // Reduced loading time\n  }, [user]);\n  const getFilteredAndSortedHistory = () => {\n    let filtered = history;\n    if (filter !== 'all') {\n      filtered = history.filter(item => item.contentType === filter);\n    }\n    return filtered.sort((a, b) => {\n      if (sortBy === 'newest') {\n        return new Date(b.createdAt) - new Date(a.createdAt);\n      } else if (sortBy === 'oldest') {\n        return new Date(a.createdAt) - new Date(b.createdAt);\n      } else if (sortBy === 'longest') {\n        return b.wordCount - a.wordCount;\n      } else if (sortBy === 'shortest') {\n        return a.wordCount - b.wordCount;\n      }\n      return 0;\n    });\n  };\n  const formatDate = date => {\n    return new Date(date).toLocaleDateString('en-US', {\n      year: 'numeric',\n      month: 'short',\n      day: 'numeric',\n      hour: '2-digit',\n      minute: '2-digit'\n    });\n  };\n  const getContentTypeIcon = type => {\n    switch (type) {\n      case 'blog':\n        return '📝';\n      case 'social':\n        return '📱';\n      case 'ad':\n        return '📢';\n      default:\n        return '📄';\n    }\n  };\n  const getToneColor = tone => {\n    switch (tone) {\n      case 'professional':\n        return '#667eea';\n      case 'casual':\n        return '#ff6b6b';\n      case 'friendly':\n        return '#10b981';\n      default:\n        return '#64748b';\n    }\n  };\n  const copyToClipboard = async content => {\n    try {\n      await navigator.clipboard.writeText(content);\n      // You could add a toast notification here\n    } catch (err) {\n      console.error('Failed to copy text: ', err);\n    }\n  };\n  if (!user) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"history-container\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"history-empty\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"empty-illustration\",\n          children: /*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"empty-emoji\",\n            children: \"\\uD83D\\uDD10\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 136,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 135,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n          className: \"empty-title\",\n          children: \"Sign In to View History\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 138,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"empty-subtitle\",\n          children: \"Create an account to save and access your generated content history\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 139,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 134,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 133,\n      columnNumber: 7\n    }, this);\n  }\n  if (loading) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"history-container\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"history-loading\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"loading-spinner\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 151,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"loading-text\",\n          children: \"Loading your creative history...\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 152,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"loading-emojis\",\n          children: [/*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"loading-emoji\",\n            children: \"\\uD83D\\uDCDD\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 154,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"loading-emoji\",\n            children: \"\\u2728\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 155,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"loading-emoji\",\n            children: \"\\uD83D\\uDCDA\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 156,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 153,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 150,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 149,\n      columnNumber: 7\n    }, this);\n  }\n  const filteredHistory = getFilteredAndSortedHistory();\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"history-container\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"history-header\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"history-stats\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"stat-item\",\n          children: [/*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"stat-number\",\n            children: history.length\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 170,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"stat-label\",\n            children: \"Total Created\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 171,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 169,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"stat-item\",\n          children: [/*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"stat-number\",\n            children: history.reduce((sum, item) => sum + item.wordCount, 0)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 174,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"stat-label\",\n            children: \"Words Written\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 175,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 173,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"stat-item\",\n          children: [/*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"stat-number\",\n            children: new Set(history.map(item => item.contentType)).size\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 178,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"stat-label\",\n            children: \"Content Types\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 179,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 177,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 168,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"history-controls\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"filter-group\",\n          children: [/*#__PURE__*/_jsxDEV(\"label\", {\n            className: \"control-label\",\n            children: [/*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"label-icon\",\n              children: \"\\uD83C\\uDFAF\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 186,\n              columnNumber: 15\n            }, this), \"Filter by Type\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 185,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"select\", {\n            value: filter,\n            onChange: e => setFilter(e.target.value),\n            className: \"control-select\",\n            children: [/*#__PURE__*/_jsxDEV(\"option\", {\n              value: \"all\",\n              children: \"All Content\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 194,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n              value: \"blog\",\n              children: \"\\uD83D\\uDCDD Blog Posts\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 195,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n              value: \"social\",\n              children: \"\\uD83D\\uDCF1 Social Posts\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 196,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n              value: \"ad\",\n              children: \"\\uD83D\\uDCE2 Advertisements\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 197,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 189,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 184,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"filter-group\",\n          children: [/*#__PURE__*/_jsxDEV(\"label\", {\n            className: \"control-label\",\n            children: [/*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"label-icon\",\n              children: \"\\uD83D\\uDCCA\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 203,\n              columnNumber: 15\n            }, this), \"Sort by\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 202,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"select\", {\n            value: sortBy,\n            onChange: e => setSortBy(e.target.value),\n            className: \"control-select\",\n            children: [/*#__PURE__*/_jsxDEV(\"option\", {\n              value: \"newest\",\n              children: \"Newest First\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 211,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n              value: \"oldest\",\n              children: \"Oldest First\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 212,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n              value: \"longest\",\n              children: \"Longest First\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 213,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n              value: \"shortest\",\n              children: \"Shortest First\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 214,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 206,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 201,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 183,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 167,\n      columnNumber: 7\n    }, this), filteredHistory.length === 0 ? /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"history-empty\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"empty-illustration\",\n        children: /*#__PURE__*/_jsxDEV(\"span\", {\n          className: \"empty-emoji\",\n          children: \"\\uD83D\\uDCDD\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 223,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 222,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n        className: \"empty-title\",\n        children: \"No Content Yet\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 225,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n        className: \"empty-subtitle\",\n        children: \"Start creating amazing content to see your history here!\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 226,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 221,\n      columnNumber: 9\n    }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"history-grid\",\n      children: filteredHistory.map(item => /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"history-card\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"card-header\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"card-type\",\n            children: [/*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"type-icon\",\n              children: getContentTypeIcon(item.contentType)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 236,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"type-text\",\n              children: item.contentType\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 237,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 235,\n            columnNumber: 17\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"card-actions\",\n            children: /*#__PURE__*/_jsxDEV(\"button\", {\n              className: \"action-btn copy-btn\",\n              onClick: () => copyToClipboard(item.content),\n              title: \"Copy content\",\n              children: \"\\uD83D\\uDCCB\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 240,\n              columnNumber: 19\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 239,\n            columnNumber: 17\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 234,\n          columnNumber: 15\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"card-content\",\n          children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n            className: \"content-topic\",\n            children: item.topic\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 251,\n            columnNumber: 17\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"content-preview\",\n            children: item.content.length > 150 ? `${item.content.substring(0, 150)}...` : item.content\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 252,\n            columnNumber: 17\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 250,\n          columnNumber: 15\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"card-meta\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"meta-tags\",\n            children: [/*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"tone-tag\",\n              style: {\n                backgroundColor: getToneColor(item.tone)\n              },\n              children: item.tone\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 262,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"word-count\",\n              children: [item.wordCount, \" words\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 268,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 261,\n            columnNumber: 17\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"meta-date\",\n            children: formatDate(item.createdAt)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 272,\n            columnNumber: 17\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 260,\n          columnNumber: 15\n        }, this)]\n      }, item.id, true, {\n        fileName: _jsxFileName,\n        lineNumber: 233,\n        columnNumber: 13\n      }, this))\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 231,\n      columnNumber: 9\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 166,\n    columnNumber: 5\n  }, this);\n};\n_s(History, \"Ba6CSGJ3jwRC+rI8UFtpJwgMywQ=\");\n_c = History;\nexport default History;\nvar _c;\n$RefreshReg$(_c, \"History\");", "map": {"version": 3, "names": ["useState", "useEffect", "jsxDEV", "_jsxDEV", "History", "user", "_s", "history", "setHistory", "loading", "setLoading", "filter", "setFilter", "sortBy", "setSortBy", "mockHistory", "id", "contentType", "tone", "length", "topic", "content", "createdAt", "Date", "wordCount", "console", "log", "setTimeout", "username", "getFilteredAndSortedHistory", "filtered", "item", "sort", "a", "b", "formatDate", "date", "toLocaleDateString", "year", "month", "day", "hour", "minute", "getContentTypeIcon", "type", "getToneColor", "copyToClipboard", "navigator", "clipboard", "writeText", "err", "error", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "filteredHistory", "reduce", "sum", "Set", "map", "size", "value", "onChange", "e", "target", "onClick", "title", "substring", "style", "backgroundColor", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/vedika/ai-generative/client/src/components/History.jsx"], "sourcesContent": ["import { useState, useEffect } from 'react';\r\nimport './History.css';\r\n\r\nconst History = ({ user }) => {\r\n  const [history, setHistory] = useState([]);\r\n  const [loading, setLoading] = useState(false);\r\n  const [filter, setFilter] = useState('all');\r\n  const [sortBy, setSortBy] = useState('newest');\r\n\r\n  // Mock history data for demonstration (in real app, this would come from API)\r\n  const mockHistory = [\r\n    {\r\n      id: 1,\r\n      contentType: 'blog',\r\n      tone: 'professional',\r\n      length: 300,\r\n      topic: 'AI in Healthcare',\r\n      content: 'Artificial Intelligence is revolutionizing healthcare by enabling faster diagnoses, personalized treatments, and improved patient outcomes. From medical imaging to drug discovery, AI technologies are transforming how we approach medicine...',\r\n      createdAt: new Date('2024-01-15T10:30:00'),\r\n      wordCount: 287\r\n    },\r\n    {\r\n      id: 2,\r\n      contentType: 'social',\r\n      tone: 'casual',\r\n      length: 100,\r\n      topic: 'Morning Motivation',\r\n      content: 'Good morning, creators! ☀️ Today is another chance to turn your ideas into reality. Remember, every expert was once a beginner. What amazing thing will you create today? #MondayMotivation #CreateDaily',\r\n      createdAt: new Date('2024-01-14T08:15:00'),\r\n      wordCount: 34\r\n    },\r\n    {\r\n      id: 3,\r\n      contentType: 'ad',\r\n      tone: 'friendly',\r\n      length: 150,\r\n      topic: 'Eco-Friendly Products',\r\n      content: 'Make a difference with every purchase! 🌱 Our eco-friendly products help you live sustainably without compromising on quality. Join thousands of happy customers who are making the planet greener, one choice at a time. Shop now and get 20% off your first order!',\r\n      createdAt: new Date('2024-01-13T16:45:00'),\r\n      wordCount: 48\r\n    },\r\n    {\r\n      id: 4,\r\n      contentType: 'blog',\r\n      tone: 'casual',\r\n      length: 500,\r\n      topic: 'Remote Work Tips',\r\n      content: 'Working from home can be amazing, but it definitely comes with its challenges! Here are some game-changing tips that have helped me stay productive and sane while working remotely...',\r\n      createdAt: new Date('2024-01-12T14:20:00'),\r\n      wordCount: 456\r\n    }\r\n  ];\r\n\r\n  useEffect(() => {\r\n    // Debug logging\r\n    console.log('History component - user prop:', user);\r\n    console.log('History component - user type:', typeof user);\r\n\r\n    // Simulate loading\r\n    setLoading(true);\r\n    setTimeout(() => {\r\n      if (user && user.username) {\r\n        console.log('Setting mock history for user:', user.username);\r\n        setHistory(mockHistory);\r\n      } else {\r\n        console.log('No user found, setting empty history');\r\n        setHistory([]);\r\n      }\r\n      setLoading(false);\r\n    }, 500); // Reduced loading time\r\n  }, [user]);\r\n\r\n  const getFilteredAndSortedHistory = () => {\r\n    let filtered = history;\r\n\r\n    if (filter !== 'all') {\r\n      filtered = history.filter(item => item.contentType === filter);\r\n    }\r\n\r\n    return filtered.sort((a, b) => {\r\n      if (sortBy === 'newest') {\r\n        return new Date(b.createdAt) - new Date(a.createdAt);\r\n      } else if (sortBy === 'oldest') {\r\n        return new Date(a.createdAt) - new Date(b.createdAt);\r\n      } else if (sortBy === 'longest') {\r\n        return b.wordCount - a.wordCount;\r\n      } else if (sortBy === 'shortest') {\r\n        return a.wordCount - b.wordCount;\r\n      }\r\n      return 0;\r\n    });\r\n  };\r\n\r\n  const formatDate = (date) => {\r\n    return new Date(date).toLocaleDateString('en-US', {\r\n      year: 'numeric',\r\n      month: 'short',\r\n      day: 'numeric',\r\n      hour: '2-digit',\r\n      minute: '2-digit'\r\n    });\r\n  };\r\n\r\n  const getContentTypeIcon = (type) => {\r\n    switch (type) {\r\n      case 'blog': return '📝';\r\n      case 'social': return '📱';\r\n      case 'ad': return '📢';\r\n      default: return '📄';\r\n    }\r\n  };\r\n\r\n  const getToneColor = (tone) => {\r\n    switch (tone) {\r\n      case 'professional': return '#667eea';\r\n      case 'casual': return '#ff6b6b';\r\n      case 'friendly': return '#10b981';\r\n      default: return '#64748b';\r\n    }\r\n  };\r\n\r\n  const copyToClipboard = async (content) => {\r\n    try {\r\n      await navigator.clipboard.writeText(content);\r\n      // You could add a toast notification here\r\n    } catch (err) {\r\n      console.error('Failed to copy text: ', err);\r\n    }\r\n  };\r\n\r\n  if (!user) {\r\n    return (\r\n      <div className=\"history-container\">\r\n        <div className=\"history-empty\">\r\n          <div className=\"empty-illustration\">\r\n            <span className=\"empty-emoji\">🔐</span>\r\n          </div>\r\n          <h3 className=\"empty-title\">Sign In to View History</h3>\r\n          <p className=\"empty-subtitle\">\r\n            Create an account to save and access your generated content history\r\n          </p>\r\n        </div>\r\n      </div>\r\n    );\r\n  }\r\n\r\n  if (loading) {\r\n    return (\r\n      <div className=\"history-container\">\r\n        <div className=\"history-loading\">\r\n          <div className=\"loading-spinner\"></div>\r\n          <p className=\"loading-text\">Loading your creative history...</p>\r\n          <div className=\"loading-emojis\">\r\n            <span className=\"loading-emoji\">📝</span>\r\n            <span className=\"loading-emoji\">✨</span>\r\n            <span className=\"loading-emoji\">📚</span>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    );\r\n  }\r\n\r\n  const filteredHistory = getFilteredAndSortedHistory();\r\n\r\n  return (\r\n    <div className=\"history-container\">\r\n      <div className=\"history-header\">\r\n        <div className=\"history-stats\">\r\n          <div className=\"stat-item\">\r\n            <span className=\"stat-number\">{history.length}</span>\r\n            <span className=\"stat-label\">Total Created</span>\r\n          </div>\r\n          <div className=\"stat-item\">\r\n            <span className=\"stat-number\">{history.reduce((sum, item) => sum + item.wordCount, 0)}</span>\r\n            <span className=\"stat-label\">Words Written</span>\r\n          </div>\r\n          <div className=\"stat-item\">\r\n            <span className=\"stat-number\">{new Set(history.map(item => item.contentType)).size}</span>\r\n            <span className=\"stat-label\">Content Types</span>\r\n          </div>\r\n        </div>\r\n\r\n        <div className=\"history-controls\">\r\n          <div className=\"filter-group\">\r\n            <label className=\"control-label\">\r\n              <span className=\"label-icon\">🎯</span>\r\n              Filter by Type\r\n            </label>\r\n            <select\r\n              value={filter}\r\n              onChange={(e) => setFilter(e.target.value)}\r\n              className=\"control-select\"\r\n            >\r\n              <option value=\"all\">All Content</option>\r\n              <option value=\"blog\">📝 Blog Posts</option>\r\n              <option value=\"social\">📱 Social Posts</option>\r\n              <option value=\"ad\">📢 Advertisements</option>\r\n            </select>\r\n          </div>\r\n\r\n          <div className=\"filter-group\">\r\n            <label className=\"control-label\">\r\n              <span className=\"label-icon\">📊</span>\r\n              Sort by\r\n            </label>\r\n            <select\r\n              value={sortBy}\r\n              onChange={(e) => setSortBy(e.target.value)}\r\n              className=\"control-select\"\r\n            >\r\n              <option value=\"newest\">Newest First</option>\r\n              <option value=\"oldest\">Oldest First</option>\r\n              <option value=\"longest\">Longest First</option>\r\n              <option value=\"shortest\">Shortest First</option>\r\n            </select>\r\n          </div>\r\n        </div>\r\n      </div>\r\n\r\n      {filteredHistory.length === 0 ? (\r\n        <div className=\"history-empty\">\r\n          <div className=\"empty-illustration\">\r\n            <span className=\"empty-emoji\">📝</span>\r\n          </div>\r\n          <h3 className=\"empty-title\">No Content Yet</h3>\r\n          <p className=\"empty-subtitle\">\r\n            Start creating amazing content to see your history here!\r\n          </p>\r\n        </div>\r\n      ) : (\r\n        <div className=\"history-grid\">\r\n          {filteredHistory.map((item) => (\r\n            <div key={item.id} className=\"history-card\">\r\n              <div className=\"card-header\">\r\n                <div className=\"card-type\">\r\n                  <span className=\"type-icon\">{getContentTypeIcon(item.contentType)}</span>\r\n                  <span className=\"type-text\">{item.contentType}</span>\r\n                </div>\r\n                <div className=\"card-actions\">\r\n                  <button\r\n                    className=\"action-btn copy-btn\"\r\n                    onClick={() => copyToClipboard(item.content)}\r\n                    title=\"Copy content\"\r\n                  >\r\n                    📋\r\n                  </button>\r\n                </div>\r\n              </div>\r\n\r\n              <div className=\"card-content\">\r\n                <h4 className=\"content-topic\">{item.topic}</h4>\r\n                <p className=\"content-preview\">\r\n                  {item.content.length > 150\r\n                    ? `${item.content.substring(0, 150)}...`\r\n                    : item.content\r\n                  }\r\n                </p>\r\n              </div>\r\n\r\n              <div className=\"card-meta\">\r\n                <div className=\"meta-tags\">\r\n                  <span\r\n                    className=\"tone-tag\"\r\n                    style={{ backgroundColor: getToneColor(item.tone) }}\r\n                  >\r\n                    {item.tone}\r\n                  </span>\r\n                  <span className=\"word-count\">\r\n                    {item.wordCount} words\r\n                  </span>\r\n                </div>\r\n                <div className=\"meta-date\">\r\n                  {formatDate(item.createdAt)}\r\n                </div>\r\n              </div>\r\n            </div>\r\n          ))}\r\n        </div>\r\n      )}\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default History;"], "mappings": ";;AAAA,SAASA,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAC3C,OAAO,eAAe;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEvB,MAAMC,OAAO,GAAGA,CAAC;EAAEC;AAAK,CAAC,KAAK;EAAAC,EAAA;EAC5B,MAAM,CAACC,OAAO,EAAEC,UAAU,CAAC,GAAGR,QAAQ,CAAC,EAAE,CAAC;EAC1C,MAAM,CAACS,OAAO,EAAEC,UAAU,CAAC,GAAGV,QAAQ,CAAC,KAAK,CAAC;EAC7C,MAAM,CAACW,MAAM,EAAEC,SAAS,CAAC,GAAGZ,QAAQ,CAAC,KAAK,CAAC;EAC3C,MAAM,CAACa,MAAM,EAAEC,SAAS,CAAC,GAAGd,QAAQ,CAAC,QAAQ,CAAC;;EAE9C;EACA,MAAMe,WAAW,GAAG,CAClB;IACEC,EAAE,EAAE,CAAC;IACLC,WAAW,EAAE,MAAM;IACnBC,IAAI,EAAE,cAAc;IACpBC,MAAM,EAAE,GAAG;IACXC,KAAK,EAAE,kBAAkB;IACzBC,OAAO,EAAE,kPAAkP;IAC3PC,SAAS,EAAE,IAAIC,IAAI,CAAC,qBAAqB,CAAC;IAC1CC,SAAS,EAAE;EACb,CAAC,EACD;IACER,EAAE,EAAE,CAAC;IACLC,WAAW,EAAE,QAAQ;IACrBC,IAAI,EAAE,QAAQ;IACdC,MAAM,EAAE,GAAG;IACXC,KAAK,EAAE,oBAAoB;IAC3BC,OAAO,EAAE,0MAA0M;IACnNC,SAAS,EAAE,IAAIC,IAAI,CAAC,qBAAqB,CAAC;IAC1CC,SAAS,EAAE;EACb,CAAC,EACD;IACER,EAAE,EAAE,CAAC;IACLC,WAAW,EAAE,IAAI;IACjBC,IAAI,EAAE,UAAU;IAChBC,MAAM,EAAE,GAAG;IACXC,KAAK,EAAE,uBAAuB;IAC9BC,OAAO,EAAE,sQAAsQ;IAC/QC,SAAS,EAAE,IAAIC,IAAI,CAAC,qBAAqB,CAAC;IAC1CC,SAAS,EAAE;EACb,CAAC,EACD;IACER,EAAE,EAAE,CAAC;IACLC,WAAW,EAAE,MAAM;IACnBC,IAAI,EAAE,QAAQ;IACdC,MAAM,EAAE,GAAG;IACXC,KAAK,EAAE,kBAAkB;IACzBC,OAAO,EAAE,wLAAwL;IACjMC,SAAS,EAAE,IAAIC,IAAI,CAAC,qBAAqB,CAAC;IAC1CC,SAAS,EAAE;EACb,CAAC,CACF;EAEDvB,SAAS,CAAC,MAAM;IACd;IACAwB,OAAO,CAACC,GAAG,CAAC,gCAAgC,EAAErB,IAAI,CAAC;IACnDoB,OAAO,CAACC,GAAG,CAAC,gCAAgC,EAAE,OAAOrB,IAAI,CAAC;;IAE1D;IACAK,UAAU,CAAC,IAAI,CAAC;IAChBiB,UAAU,CAAC,MAAM;MACf,IAAItB,IAAI,IAAIA,IAAI,CAACuB,QAAQ,EAAE;QACzBH,OAAO,CAACC,GAAG,CAAC,gCAAgC,EAAErB,IAAI,CAACuB,QAAQ,CAAC;QAC5DpB,UAAU,CAACO,WAAW,CAAC;MACzB,CAAC,MAAM;QACLU,OAAO,CAACC,GAAG,CAAC,sCAAsC,CAAC;QACnDlB,UAAU,CAAC,EAAE,CAAC;MAChB;MACAE,UAAU,CAAC,KAAK,CAAC;IACnB,CAAC,EAAE,GAAG,CAAC,CAAC,CAAC;EACX,CAAC,EAAE,CAACL,IAAI,CAAC,CAAC;EAEV,MAAMwB,2BAA2B,GAAGA,CAAA,KAAM;IACxC,IAAIC,QAAQ,GAAGvB,OAAO;IAEtB,IAAII,MAAM,KAAK,KAAK,EAAE;MACpBmB,QAAQ,GAAGvB,OAAO,CAACI,MAAM,CAACoB,IAAI,IAAIA,IAAI,CAACd,WAAW,KAAKN,MAAM,CAAC;IAChE;IAEA,OAAOmB,QAAQ,CAACE,IAAI,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAK;MAC7B,IAAIrB,MAAM,KAAK,QAAQ,EAAE;QACvB,OAAO,IAAIU,IAAI,CAACW,CAAC,CAACZ,SAAS,CAAC,GAAG,IAAIC,IAAI,CAACU,CAAC,CAACX,SAAS,CAAC;MACtD,CAAC,MAAM,IAAIT,MAAM,KAAK,QAAQ,EAAE;QAC9B,OAAO,IAAIU,IAAI,CAACU,CAAC,CAACX,SAAS,CAAC,GAAG,IAAIC,IAAI,CAACW,CAAC,CAACZ,SAAS,CAAC;MACtD,CAAC,MAAM,IAAIT,MAAM,KAAK,SAAS,EAAE;QAC/B,OAAOqB,CAAC,CAACV,SAAS,GAAGS,CAAC,CAACT,SAAS;MAClC,CAAC,MAAM,IAAIX,MAAM,KAAK,UAAU,EAAE;QAChC,OAAOoB,CAAC,CAACT,SAAS,GAAGU,CAAC,CAACV,SAAS;MAClC;MACA,OAAO,CAAC;IACV,CAAC,CAAC;EACJ,CAAC;EAED,MAAMW,UAAU,GAAIC,IAAI,IAAK;IAC3B,OAAO,IAAIb,IAAI,CAACa,IAAI,CAAC,CAACC,kBAAkB,CAAC,OAAO,EAAE;MAChDC,IAAI,EAAE,SAAS;MACfC,KAAK,EAAE,OAAO;MACdC,GAAG,EAAE,SAAS;MACdC,IAAI,EAAE,SAAS;MACfC,MAAM,EAAE;IACV,CAAC,CAAC;EACJ,CAAC;EAED,MAAMC,kBAAkB,GAAIC,IAAI,IAAK;IACnC,QAAQA,IAAI;MACV,KAAK,MAAM;QAAE,OAAO,IAAI;MACxB,KAAK,QAAQ;QAAE,OAAO,IAAI;MAC1B,KAAK,IAAI;QAAE,OAAO,IAAI;MACtB;QAAS,OAAO,IAAI;IACtB;EACF,CAAC;EAED,MAAMC,YAAY,GAAI3B,IAAI,IAAK;IAC7B,QAAQA,IAAI;MACV,KAAK,cAAc;QAAE,OAAO,SAAS;MACrC,KAAK,QAAQ;QAAE,OAAO,SAAS;MAC/B,KAAK,UAAU;QAAE,OAAO,SAAS;MACjC;QAAS,OAAO,SAAS;IAC3B;EACF,CAAC;EAED,MAAM4B,eAAe,GAAG,MAAOzB,OAAO,IAAK;IACzC,IAAI;MACF,MAAM0B,SAAS,CAACC,SAAS,CAACC,SAAS,CAAC5B,OAAO,CAAC;MAC5C;IACF,CAAC,CAAC,OAAO6B,GAAG,EAAE;MACZzB,OAAO,CAAC0B,KAAK,CAAC,uBAAuB,EAAED,GAAG,CAAC;IAC7C;EACF,CAAC;EAED,IAAI,CAAC7C,IAAI,EAAE;IACT,oBACEF,OAAA;MAAKiD,SAAS,EAAC,mBAAmB;MAAAC,QAAA,eAChClD,OAAA;QAAKiD,SAAS,EAAC,eAAe;QAAAC,QAAA,gBAC5BlD,OAAA;UAAKiD,SAAS,EAAC,oBAAoB;UAAAC,QAAA,eACjClD,OAAA;YAAMiD,SAAS,EAAC,aAAa;YAAAC,QAAA,EAAC;UAAE;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACpC,CAAC,eACNtD,OAAA;UAAIiD,SAAS,EAAC,aAAa;UAAAC,QAAA,EAAC;QAAuB;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACxDtD,OAAA;UAAGiD,SAAS,EAAC,gBAAgB;UAAAC,QAAA,EAAC;QAE9B;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACD;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAEV;EAEA,IAAIhD,OAAO,EAAE;IACX,oBACEN,OAAA;MAAKiD,SAAS,EAAC,mBAAmB;MAAAC,QAAA,eAChClD,OAAA;QAAKiD,SAAS,EAAC,iBAAiB;QAAAC,QAAA,gBAC9BlD,OAAA;UAAKiD,SAAS,EAAC;QAAiB;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,eACvCtD,OAAA;UAAGiD,SAAS,EAAC,cAAc;UAAAC,QAAA,EAAC;QAAgC;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC,eAChEtD,OAAA;UAAKiD,SAAS,EAAC,gBAAgB;UAAAC,QAAA,gBAC7BlD,OAAA;YAAMiD,SAAS,EAAC,eAAe;YAAAC,QAAA,EAAC;UAAE;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eACzCtD,OAAA;YAAMiD,SAAS,EAAC,eAAe;YAAAC,QAAA,EAAC;UAAC;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eACxCtD,OAAA;YAAMiD,SAAS,EAAC,eAAe;YAAAC,QAAA,EAAC;UAAE;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACtC,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAEV;EAEA,MAAMC,eAAe,GAAG7B,2BAA2B,CAAC,CAAC;EAErD,oBACE1B,OAAA;IAAKiD,SAAS,EAAC,mBAAmB;IAAAC,QAAA,gBAChClD,OAAA;MAAKiD,SAAS,EAAC,gBAAgB;MAAAC,QAAA,gBAC7BlD,OAAA;QAAKiD,SAAS,EAAC,eAAe;QAAAC,QAAA,gBAC5BlD,OAAA;UAAKiD,SAAS,EAAC,WAAW;UAAAC,QAAA,gBACxBlD,OAAA;YAAMiD,SAAS,EAAC,aAAa;YAAAC,QAAA,EAAE9C,OAAO,CAACY;UAAM;YAAAmC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC,eACrDtD,OAAA;YAAMiD,SAAS,EAAC,YAAY;YAAAC,QAAA,EAAC;UAAa;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC9C,CAAC,eACNtD,OAAA;UAAKiD,SAAS,EAAC,WAAW;UAAAC,QAAA,gBACxBlD,OAAA;YAAMiD,SAAS,EAAC,aAAa;YAAAC,QAAA,EAAE9C,OAAO,CAACoD,MAAM,CAAC,CAACC,GAAG,EAAE7B,IAAI,KAAK6B,GAAG,GAAG7B,IAAI,CAACP,SAAS,EAAE,CAAC;UAAC;YAAA8B,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC,eAC7FtD,OAAA;YAAMiD,SAAS,EAAC,YAAY;YAAAC,QAAA,EAAC;UAAa;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC9C,CAAC,eACNtD,OAAA;UAAKiD,SAAS,EAAC,WAAW;UAAAC,QAAA,gBACxBlD,OAAA;YAAMiD,SAAS,EAAC,aAAa;YAAAC,QAAA,EAAE,IAAIQ,GAAG,CAACtD,OAAO,CAACuD,GAAG,CAAC/B,IAAI,IAAIA,IAAI,CAACd,WAAW,CAAC,CAAC,CAAC8C;UAAI;YAAAT,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC,eAC1FtD,OAAA;YAAMiD,SAAS,EAAC,YAAY;YAAAC,QAAA,EAAC;UAAa;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC9C,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAENtD,OAAA;QAAKiD,SAAS,EAAC,kBAAkB;QAAAC,QAAA,gBAC/BlD,OAAA;UAAKiD,SAAS,EAAC,cAAc;UAAAC,QAAA,gBAC3BlD,OAAA;YAAOiD,SAAS,EAAC,eAAe;YAAAC,QAAA,gBAC9BlD,OAAA;cAAMiD,SAAS,EAAC,YAAY;cAAAC,QAAA,EAAC;YAAE;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,kBAExC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC,eACRtD,OAAA;YACE6D,KAAK,EAAErD,MAAO;YACdsD,QAAQ,EAAGC,CAAC,IAAKtD,SAAS,CAACsD,CAAC,CAACC,MAAM,CAACH,KAAK,CAAE;YAC3CZ,SAAS,EAAC,gBAAgB;YAAAC,QAAA,gBAE1BlD,OAAA;cAAQ6D,KAAK,EAAC,KAAK;cAAAX,QAAA,EAAC;YAAW;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eACxCtD,OAAA;cAAQ6D,KAAK,EAAC,MAAM;cAAAX,QAAA,EAAC;YAAa;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eAC3CtD,OAAA;cAAQ6D,KAAK,EAAC,QAAQ;cAAAX,QAAA,EAAC;YAAe;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eAC/CtD,OAAA;cAAQ6D,KAAK,EAAC,IAAI;cAAAX,QAAA,EAAC;YAAiB;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACvC,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC,eAENtD,OAAA;UAAKiD,SAAS,EAAC,cAAc;UAAAC,QAAA,gBAC3BlD,OAAA;YAAOiD,SAAS,EAAC,eAAe;YAAAC,QAAA,gBAC9BlD,OAAA;cAAMiD,SAAS,EAAC,YAAY;cAAAC,QAAA,EAAC;YAAE;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,WAExC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC,eACRtD,OAAA;YACE6D,KAAK,EAAEnD,MAAO;YACdoD,QAAQ,EAAGC,CAAC,IAAKpD,SAAS,CAACoD,CAAC,CAACC,MAAM,CAACH,KAAK,CAAE;YAC3CZ,SAAS,EAAC,gBAAgB;YAAAC,QAAA,gBAE1BlD,OAAA;cAAQ6D,KAAK,EAAC,QAAQ;cAAAX,QAAA,EAAC;YAAY;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eAC5CtD,OAAA;cAAQ6D,KAAK,EAAC,QAAQ;cAAAX,QAAA,EAAC;YAAY;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eAC5CtD,OAAA;cAAQ6D,KAAK,EAAC,SAAS;cAAAX,QAAA,EAAC;YAAa;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eAC9CtD,OAAA;cAAQ6D,KAAK,EAAC,UAAU;cAAAX,QAAA,EAAC;YAAc;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC1C,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,EAELC,eAAe,CAACvC,MAAM,KAAK,CAAC,gBAC3BhB,OAAA;MAAKiD,SAAS,EAAC,eAAe;MAAAC,QAAA,gBAC5BlD,OAAA;QAAKiD,SAAS,EAAC,oBAAoB;QAAAC,QAAA,eACjClD,OAAA;UAAMiD,SAAS,EAAC,aAAa;UAAAC,QAAA,EAAC;QAAE;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACpC,CAAC,eACNtD,OAAA;QAAIiD,SAAS,EAAC,aAAa;QAAAC,QAAA,EAAC;MAAc;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eAC/CtD,OAAA;QAAGiD,SAAS,EAAC,gBAAgB;QAAAC,QAAA,EAAC;MAE9B;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAG,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACD,CAAC,gBAENtD,OAAA;MAAKiD,SAAS,EAAC,cAAc;MAAAC,QAAA,EAC1BK,eAAe,CAACI,GAAG,CAAE/B,IAAI,iBACxB5B,OAAA;QAAmBiD,SAAS,EAAC,cAAc;QAAAC,QAAA,gBACzClD,OAAA;UAAKiD,SAAS,EAAC,aAAa;UAAAC,QAAA,gBAC1BlD,OAAA;YAAKiD,SAAS,EAAC,WAAW;YAAAC,QAAA,gBACxBlD,OAAA;cAAMiD,SAAS,EAAC,WAAW;cAAAC,QAAA,EAAEV,kBAAkB,CAACZ,IAAI,CAACd,WAAW;YAAC;cAAAqC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eACzEtD,OAAA;cAAMiD,SAAS,EAAC,WAAW;cAAAC,QAAA,EAAEtB,IAAI,CAACd;YAAW;cAAAqC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAClD,CAAC,eACNtD,OAAA;YAAKiD,SAAS,EAAC,cAAc;YAAAC,QAAA,eAC3BlD,OAAA;cACEiD,SAAS,EAAC,qBAAqB;cAC/BgB,OAAO,EAAEA,CAAA,KAAMtB,eAAe,CAACf,IAAI,CAACV,OAAO,CAAE;cAC7CgD,KAAK,EAAC,cAAc;cAAAhB,QAAA,EACrB;YAED;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAENtD,OAAA;UAAKiD,SAAS,EAAC,cAAc;UAAAC,QAAA,gBAC3BlD,OAAA;YAAIiD,SAAS,EAAC,eAAe;YAAAC,QAAA,EAAEtB,IAAI,CAACX;UAAK;YAAAkC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC,eAC/CtD,OAAA;YAAGiD,SAAS,EAAC,iBAAiB;YAAAC,QAAA,EAC3BtB,IAAI,CAACV,OAAO,CAACF,MAAM,GAAG,GAAG,GACtB,GAAGY,IAAI,CAACV,OAAO,CAACiD,SAAS,CAAC,CAAC,EAAE,GAAG,CAAC,KAAK,GACtCvC,IAAI,CAACV;UAAO;YAAAiC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAEf,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACD,CAAC,eAENtD,OAAA;UAAKiD,SAAS,EAAC,WAAW;UAAAC,QAAA,gBACxBlD,OAAA;YAAKiD,SAAS,EAAC,WAAW;YAAAC,QAAA,gBACxBlD,OAAA;cACEiD,SAAS,EAAC,UAAU;cACpBmB,KAAK,EAAE;gBAAEC,eAAe,EAAE3B,YAAY,CAACd,IAAI,CAACb,IAAI;cAAE,CAAE;cAAAmC,QAAA,EAEnDtB,IAAI,CAACb;YAAI;cAAAoC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACN,CAAC,eACPtD,OAAA;cAAMiD,SAAS,EAAC,YAAY;cAAAC,QAAA,GACzBtB,IAAI,CAACP,SAAS,EAAC,QAClB;YAAA;cAAA8B,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACJ,CAAC,eACNtD,OAAA;YAAKiD,SAAS,EAAC,WAAW;YAAAC,QAAA,EACvBlB,UAAU,CAACJ,IAAI,CAACT,SAAS;UAAC;YAAAgC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACxB,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA,GA1CE1B,IAAI,CAACf,EAAE;QAAAsC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OA2CZ,CACN;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC,CACN;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACE,CAAC;AAEV,CAAC;AAACnD,EAAA,CAtRIF,OAAO;AAAAqE,EAAA,GAAPrE,OAAO;AAwRb,eAAeA,OAAO;AAAC,IAAAqE,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}