# AI Content Generator

A full-stack web application that generates high-quality content (blog posts, social media posts, and advertisements) using AI, with customizable tone, length, and topic parameters.

## Features

### 🎯 Content Generation
- **Multiple Content Types**: Blog posts, social media posts, and advertisements
- **Tone Variations**: Professional, casual, and friendly tones
- **Flexible Length**: Customizable word count (50-2000 words)
- **Topic-Based**: Generate content on any topic
- **AI-Powered**: Uses OpenAI GPT models with intelligent fallback system

### 🔐 User Authentication
- **JWT-based Authentication**: Secure user registration and login
- **Protected Routes**: Content history requires authentication
- **Password Security**: Bcrypt hashing for secure password storage

### 📚 Content History
- **Personal History**: Save and manage generated content per user
- **Full CRUD Operations**: View, retrieve, and delete content history
- **Chronological Ordering**: Most recent content first
- **Bulk Operations**: Clear all history at once

### 🧪 Testing
- **Comprehensive Test Suite**: 25+ unit tests covering all functionality
- **API Testing**: Complete coverage of all endpoints
- **Authentication Testing**: Security and authorization tests
- **Mock Fallback**: Graceful handling when OpenAI API is unavailable

## Tech Stack

### Backend
- **Node.js** with Express.js
- **OpenAI API** for content generation
- **JWT** for authentication
- **Bcrypt** for password hashing
- **Jest & Supertest** for testing
- **CORS** enabled for cross-origin requests

### Frontend
- **React 19** with modern hooks
- **Create React App** for development setup
- **Responsive Design** for all devices
- **Real-time Content Generation** with loading states

## Quick Start

### Prerequisites
- Node.js 16+ installed
- OpenAI API key (optional - app works with mock content)

### Installation

1. **Clone the repository**
```bash
git clone <repository-url>
cd vedika
```

2. **Setup Backend**
```bash
cd ai-generative/backend
npm install
```

3. **Configure Environment Variables**
Create `.env` file in the backend directory:
```env
OPENAI_API_KEY=your_openai_api_key_here
JWT_SECRET=your_jwt_secret_here
```

4. **Setup Frontend**
```bash
cd ../client
npm install
```

### Running the Application

1. **Start Backend Server**
```bash
cd ai-generative/backend
npm start
```
Backend runs on http://localhost:5000

2. **Start Frontend Development Server**
```bash
cd ai-generative/client
npm start
```
Frontend runs on http://localhost:3000

### Running Tests

**Backend Tests**
```bash
cd ai-generative/backend
npm test
```

**Frontend Tests**
```bash
cd ai-generative/client
npm test
```

## API Documentation

### Authentication Endpoints

#### Register User
```http
POST /api/auth/register
Content-Type: application/json

{
  "username": "string",
  "email": "string",
  "password": "string"
}
```

#### Login User
```http
POST /api/auth/login
Content-Type: application/json

{
  "email": "string",
  "password": "string"
}
```

#### Get User Profile
```http
GET /api/auth/profile
Authorization: Bearer <token>
```

### Content Generation

#### Generate Content
```http
POST /api/generate
Content-Type: application/json
Authorization: Bearer <token> (optional)

{
  "contentType": "blog|social post|ad",
  "tone": "professional|casual|friendly",
  "length": 100,
  "topic": "your topic here"
}
```

### Content History

#### Get User History
```http
GET /api/history
Authorization: Bearer <token>
```

#### Get Specific Content
```http
GET /api/history/:id
Authorization: Bearer <token>
```

#### Delete Content
```http
DELETE /api/history/:id
Authorization: Bearer <token>
```

#### Clear All History
```http
DELETE /api/history
Authorization: Bearer <token>
```

## Deployment

### Production Environment Variables
```env
NODE_ENV=production
OPENAI_API_KEY=your_production_openai_key
JWT_SECRET=your_strong_jwt_secret
PORT=5000
```

### Backend Deployment
1. Build and deploy to your preferred platform (Heroku, AWS, etc.)
2. Ensure environment variables are set
3. Database migration (if using persistent storage)

### Frontend Deployment
1. Build the React app:
```bash
npm run build
```
2. Deploy the `build` folder to your hosting service
3. Update API endpoints for production

## Architecture

```
┌─────────────────┐    HTTP/HTTPS    ┌─────────────────┐
│   React Client  │ ◄──────────────► │  Express API    │
│   (Port 3000)   │                  │   (Port 5000)   │
└─────────────────┘                  └─────────────────┘
                                              │
                                              ▼
                                     ┌─────────────────┐
                                     │   OpenAI API    │
                                     │  (with fallback)│
                                     └─────────────────┘
```

## Contributing

1. Fork the repository
2. Create a feature branch
3. Add tests for new functionality
4. Ensure all tests pass
5. Submit a pull request

## License

MIT License - see LICENSE file for details
