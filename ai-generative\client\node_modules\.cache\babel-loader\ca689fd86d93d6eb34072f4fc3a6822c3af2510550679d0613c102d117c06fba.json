{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\vedika\\\\ai-generative\\\\client\\\\src\\\\components\\\\AuthForm.jsx\",\n  _s = $RefreshSig$();\nimport { useState } from 'react';\nimport './AuthForm.css';\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst AuthForm = ({\n  user,\n  setUser,\n  onNavigate\n}) => {\n  _s();\n  const [isLogin, setIsLogin] = useState(true);\n  const [formData, setFormData] = useState({\n    username: '',\n    email: '',\n    password: '',\n    confirmPassword: ''\n  });\n  const [loading, setLoading] = useState(false);\n  const [message, setMessage] = useState('');\n  const [messageType, setMessageType] = useState(''); // 'success' or 'error'\n\n  // Get API base URL\n  const getApiUrl = () => {\n    return process.env.NODE_ENV === 'production' ? '/api' : 'http://localhost:5000/api';\n  };\n  const handleInputChange = e => {\n    setFormData({\n      ...formData,\n      [e.target.name]: e.target.value\n    });\n  };\n  const showMessage = (text, type) => {\n    setMessage(text);\n    setMessageType(type);\n    setTimeout(() => {\n      setMessage('');\n      setMessageType('');\n    }, 5000);\n  };\n  const handleSubmit = async e => {\n    e.preventDefault();\n    setLoading(true);\n    try {\n      if (!isLogin) {\n        // Registration validation\n        if (formData.password !== formData.confirmPassword) {\n          showMessage('Passwords do not match!', 'error');\n          setLoading(false);\n          return;\n        }\n        if (formData.password.length < 6) {\n          showMessage('Password must be at least 6 characters long!', 'error');\n          setLoading(false);\n          return;\n        }\n      }\n      const endpoint = isLogin ? '/auth/login' : '/auth/register';\n      const payload = isLogin ? {\n        email: formData.email,\n        password: formData.password\n      } : {\n        username: formData.username,\n        email: formData.email,\n        password: formData.password\n      };\n      const response = await fetch(`${getApiUrl()}${endpoint}`, {\n        method: 'POST',\n        headers: {\n          'Content-Type': 'application/json'\n        },\n        body: JSON.stringify(payload)\n      });\n      const data = await response.json();\n      if (response.ok) {\n        // Success\n        localStorage.setItem('authToken', data.token);\n        localStorage.setItem('userData', JSON.stringify(data.user));\n        setUser(data.user);\n        showMessage(isLogin ? `Welcome back, ${data.user.username}! 🎉` : `🎉 ACCOUNT CREATED! Welcome to the revolution, ${data.user.username}! 🚀`, 'success');\n\n        // Clear form\n        setFormData({\n          username: '',\n          email: '',\n          password: '',\n          confirmPassword: ''\n        });\n\n        // Auto-navigate to generate tab after successful registration\n        if (!isLogin && onNavigate) {\n          setTimeout(() => {\n            onNavigate('generate');\n          }, 2000); // Wait 2 seconds to show success message\n        }\n      } else {\n        // Error\n        showMessage(data.error || 'Something went wrong!', 'error');\n      }\n    } catch (error) {\n      showMessage('Unable to connect to the server. Please try again later.', 'error');\n    } finally {\n      setLoading(false);\n    }\n  };\n  const toggleMode = () => {\n    setIsLogin(!isLogin);\n    setMessage('');\n    setFormData({\n      username: '',\n      email: '',\n      password: '',\n      confirmPassword: ''\n    });\n  };\n  if (user) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"auth-container\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"user-profile\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"profile-celebration\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"celebration-emojis\",\n            children: [/*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"celebration-emoji\",\n              children: \"\\uD83C\\uDF89\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 131,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"celebration-emoji\",\n              children: \"\\u2728\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 132,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"celebration-emoji\",\n              children: \"\\uD83D\\uDE80\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 133,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"celebration-emoji\",\n              children: \"\\uD83C\\uDFAF\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 134,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 130,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 129,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"profile-header\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"profile-avatar\",\n            children: [/*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"avatar-emoji\",\n              children: \"\\uD83C\\uDFA8\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 140,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"avatar-glow\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 141,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 139,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"profile-info\",\n            children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n              className: \"profile-name\",\n              children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"welcome-emoji\",\n                children: \"\\uD83D\\uDC4B\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 145,\n                columnNumber: 17\n              }, this), \"Welcome, \", user.username, \"!\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 144,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"profile-email\",\n              children: user.email\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 148,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"profile-badge\",\n              onClick: () => {\n                // Add a fun interaction\n                const badge = document.querySelector('.profile-badge');\n                badge.style.animation = 'none';\n                setTimeout(() => {\n                  badge.style.animation = 'badgeGlow 2s ease-in-out infinite, badgeClick 0.6s ease-out';\n                }, 10);\n              },\n              title: \"Click for power boost! \\u26A1\",\n              children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"badge-icon\",\n                children: \"\\u26A1\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 161,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"badge-text\",\n                children: \"Creator Activated\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 162,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 149,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 143,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 138,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"profile-stats\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"stat-card\",\n            children: [/*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"stat-icon\",\n              children: \"\\uD83E\\uDDE0\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 169,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"stat-content\",\n              children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n                children: \"AI Power\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 171,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                children: \"Unlimited\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 172,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 170,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 168,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"stat-card\",\n            children: [/*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"stat-icon\",\n              children: \"\\u26A1\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 176,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"stat-content\",\n              children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n                children: \"Speed\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 178,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                children: \"Lightning Fast\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 179,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 177,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 175,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"stat-card\",\n            children: [/*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"stat-icon\",\n              children: \"\\uD83C\\uDFA8\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 183,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"stat-content\",\n              children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n                children: \"Creativity\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 185,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                children: \"Boundless\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 186,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 184,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 182,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 167,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"cta-section\",\n          children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n            className: \"cta-title\",\n            children: [/*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"cta-icon\",\n              children: \"\\uD83D\\uDE80\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 193,\n              columnNumber: 15\n            }, this), \"Ready to Create Magic?\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 192,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"cta-subtitle\",\n            children: \"Your AI-powered content creation journey starts now!\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 196,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 191,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"profile-actions\",\n          children: [/*#__PURE__*/_jsxDEV(\"button\", {\n            className: \"profile-btn generate-btn mega-cta\",\n            onClick: () => {\n              // Add success feedback\n              const button = document.querySelector('.mega-cta');\n              button.style.transform = 'scale(0.95)';\n              setTimeout(() => {\n                button.style.transform = '';\n                onNavigate && onNavigate('generate');\n              }, 150);\n            },\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"btn-bg-effect\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 214,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"btn-content\",\n              children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"btn-icon\",\n                children: \"\\uD83D\\uDE80\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 216,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"btn-text\",\n                children: \"START CREATING NOW\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 217,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"btn-particles\",\n                children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"particle\",\n                  children: \"\\u2728\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 219,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"particle\",\n                  children: \"\\uD83D\\uDCAB\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 220,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"particle\",\n                  children: \"\\u2B50\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 221,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 218,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 215,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 202,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            className: \"profile-btn history-btn secondary-btn\",\n            onClick: () => onNavigate && onNavigate('history'),\n            children: [/*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"btn-icon\",\n              children: \"\\uD83D\\uDCDA\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 229,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"btn-text\",\n              children: \"View History\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 230,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 225,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 201,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 128,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 127,\n      columnNumber: 7\n    }, this);\n  }\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"auth-container\",\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"auth-form-wrapper\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"auth-header\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"auth-illustration\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"magic-circle\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"orbit orbit-1\",\n              children: /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"orbit-emoji\",\n                children: \"\\uD83C\\uDFA8\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 245,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 244,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"orbit orbit-2\",\n              children: /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"orbit-emoji\",\n                children: \"\\u2728\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 248,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 247,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"orbit orbit-3\",\n              children: /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"orbit-emoji\",\n                children: \"\\uD83D\\uDE80\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 251,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 250,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"center-logo\",\n              children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"logo-main\",\n                children: \"\\uD83E\\uDD16\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 254,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"energy-rings\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"ring ring-1\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 256,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"ring ring-2\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 257,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"ring ring-3\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 258,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 255,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 253,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 243,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 242,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"title-container\",\n          children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n            className: \"auth-title\",\n            children: [/*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"title-text\",\n              children: isLogin ? 'Welcome Back, Creator!' : 'Unleash Your Creative Power!'\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 266,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"title-effects\",\n              children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"title-decoration explosive\",\n                children: isLogin ? '⚡' : '💥'\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 270,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"sparkle-trail\",\n                children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"sparkle\",\n                  children: \"\\u2728\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 274,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"sparkle\",\n                  children: \"\\u2B50\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 275,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"sparkle\",\n                  children: \"\\uD83D\\uDCAB\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 276,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 273,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 269,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 265,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"subtitle-container\",\n            children: [/*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"auth-subtitle\",\n              children: isLogin ? '🎯 Ready to create mind-blowing content with AI superpowers?' : '🌟 Join thousands of creators transforming ideas into reality!'\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 282,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"impact-stats\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"stat-bubble\",\n                children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"stat-number\",\n                  children: \"10K+\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 290,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"stat-label\",\n                  children: \"Creators\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 291,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 289,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"stat-bubble\",\n                children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"stat-number\",\n                  children: \"1M+\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 294,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"stat-label\",\n                  children: \"Content\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 295,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 293,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"stat-bubble\",\n                children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"stat-number\",\n                  children: \"\\u221E\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 298,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"stat-label\",\n                  children: \"Possibilities\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 299,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 297,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 288,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 281,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 264,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 241,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"form\", {\n        onSubmit: handleSubmit,\n        className: \"auth-form\",\n        children: [!isLogin && /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"form-group\",\n          children: [/*#__PURE__*/_jsxDEV(\"label\", {\n            className: \"form-label\",\n            children: [/*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"label-icon\",\n              children: \"\\uD83D\\uDC64\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 310,\n              columnNumber: 17\n            }, this), \"Username\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 309,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n            type: \"text\",\n            name: \"username\",\n            value: formData.username,\n            onChange: handleInputChange,\n            placeholder: \"Choose a creative username\",\n            className: \"form-input\",\n            required: !isLogin,\n            disabled: loading\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 313,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 308,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"form-group\",\n          children: [/*#__PURE__*/_jsxDEV(\"label\", {\n            className: \"form-label\",\n            children: [/*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"label-icon\",\n              children: \"\\uD83D\\uDCE7\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 328,\n              columnNumber: 15\n            }, this), \"Email\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 327,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n            type: \"email\",\n            name: \"email\",\n            value: formData.email,\n            onChange: handleInputChange,\n            placeholder: \"<EMAIL>\",\n            className: \"form-input\",\n            required: true,\n            disabled: loading\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 331,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 326,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"form-group\",\n          children: [/*#__PURE__*/_jsxDEV(\"label\", {\n            className: \"form-label\",\n            children: [/*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"label-icon\",\n              children: \"\\uD83D\\uDD12\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 345,\n              columnNumber: 15\n            }, this), \"Password\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 344,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n            type: \"password\",\n            name: \"password\",\n            value: formData.password,\n            onChange: handleInputChange,\n            placeholder: isLogin ? \"Enter your password\" : \"Create a secure password (6+ characters)\",\n            className: \"form-input\",\n            required: true,\n            disabled: loading,\n            minLength: isLogin ? undefined : 6\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 348,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 343,\n          columnNumber: 11\n        }, this), !isLogin && /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"form-group\",\n          children: [/*#__PURE__*/_jsxDEV(\"label\", {\n            className: \"form-label\",\n            children: [/*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"label-icon\",\n              children: \"\\uD83D\\uDD10\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 364,\n              columnNumber: 17\n            }, this), \"Confirm Password\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 363,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n            type: \"password\",\n            name: \"confirmPassword\",\n            value: formData.confirmPassword,\n            onChange: handleInputChange,\n            placeholder: \"Confirm your password\",\n            className: \"form-input\",\n            required: !isLogin,\n            disabled: loading\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 367,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 362,\n          columnNumber: 13\n        }, this), message && /*#__PURE__*/_jsxDEV(\"div\", {\n          className: `message ${messageType}`,\n          children: [/*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"message-icon\",\n            children: messageType === 'success' ? '✅' : '❌'\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 382,\n            columnNumber: 15\n          }, this), message]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 381,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          type: \"submit\",\n          className: \"auth-submit-btn mega-button\",\n          disabled: loading,\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"button-bg-effect\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 394,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"button-content\",\n            children: loading ? /*#__PURE__*/_jsxDEV(_Fragment, {\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"loading-animation\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"loading-orb\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 399,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"loading-particles\",\n                  children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"particle\",\n                    children: \"\\u2728\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 401,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"particle\",\n                    children: \"\\u2B50\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 402,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"particle\",\n                    children: \"\\uD83D\\uDCAB\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 403,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 400,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 398,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"loading-text\",\n                children: isLogin ? '🔮 Authenticating Magic...' : '🚀 Launching Your Journey...'\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 406,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true) : /*#__PURE__*/_jsxDEV(_Fragment, {\n              children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"btn-icon mega-icon\",\n                children: isLogin ? '⚡' : '�'\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 412,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"btn-text\",\n                children: isLogin ? 'UNLEASH CREATIVITY' : 'START MY JOURNEY'\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 415,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"button-particles\",\n                children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"btn-particle\",\n                  children: \"\\u2728\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 419,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"btn-particle\",\n                  children: \"\\uD83D\\uDCA5\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 420,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"btn-particle\",\n                  children: \"\\uD83D\\uDE80\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 421,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 418,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 395,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 389,\n          columnNumber: 11\n        }, this), !isLogin && /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"power-features\",\n          children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n            className: \"features-title\",\n            children: [/*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"features-icon\",\n              children: \"\\u26A1\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 431,\n              columnNumber: 17\n            }, this), \"Unlock These Superpowers\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 430,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"features-grid\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"feature-item\",\n              children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"feature-emoji\",\n                children: \"\\uD83E\\uDDE0\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 436,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"feature-text\",\n                children: \"AI Brain Power\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 437,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 435,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"feature-item\",\n              children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"feature-emoji\",\n                children: \"\\u26A1\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 440,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"feature-text\",\n                children: \"Lightning Speed\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 441,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 439,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"feature-item\",\n              children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"feature-emoji\",\n                children: \"\\uD83C\\uDFA8\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 444,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"feature-text\",\n                children: \"Unlimited Creativity\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 445,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 443,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"feature-item\",\n              children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"feature-emoji\",\n                children: \"\\uD83D\\uDE80\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 448,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"feature-text\",\n                children: \"Instant Results\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 449,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 447,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 434,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 429,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 306,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"auth-footer\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"switch-container\",\n          children: [/*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"auth-switch-text\",\n            children: isLogin ? \"🌟 Ready to join the revolution?\" : \"🚀 Already part of the magic?\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 458,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            type: \"button\",\n            onClick: toggleMode,\n            className: \"auth-switch-btn impact-switch\",\n            disabled: loading,\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"switch-bg-effect\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 467,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"switch-content\",\n              children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"switch-icon explosive\",\n                children: isLogin ? '💥' : '⚡'\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 469,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"switch-text\",\n                children: isLogin ? 'JOIN THE REVOLUTION' : 'ENTER THE MATRIX'\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 472,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 468,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 461,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 457,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"trust-indicators\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"trust-item\",\n            children: [/*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"trust-icon\",\n              children: \"\\uD83D\\uDD12\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 481,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"trust-text\",\n              children: \"Bank-Level Security\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 482,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 480,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"trust-item\",\n            children: [/*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"trust-icon\",\n              children: \"\\u26A1\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 485,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"trust-text\",\n              children: \"Instant Access\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 486,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 484,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"trust-item\",\n            children: [/*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"trust-icon\",\n              children: \"\\uD83C\\uDF0D\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 489,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"trust-text\",\n              children: \"Global Community\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 490,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 488,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 479,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 456,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 240,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 239,\n    columnNumber: 5\n  }, this);\n};\n_s(AuthForm, \"FzuKKKgrko/pj98k0+aL3Zo/XfA=\");\n_c = AuthForm;\nexport default AuthForm;\nvar _c;\n$RefreshReg$(_c, \"AuthForm\");", "map": {"version": 3, "names": ["useState", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "AuthForm", "user", "setUser", "onNavigate", "_s", "is<PERSON>ogin", "setIsLogin", "formData", "setFormData", "username", "email", "password", "confirmPassword", "loading", "setLoading", "message", "setMessage", "messageType", "setMessageType", "getApiUrl", "process", "env", "NODE_ENV", "handleInputChange", "e", "target", "name", "value", "showMessage", "text", "type", "setTimeout", "handleSubmit", "preventDefault", "length", "endpoint", "payload", "response", "fetch", "method", "headers", "body", "JSON", "stringify", "data", "json", "ok", "localStorage", "setItem", "token", "error", "toggleMode", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "onClick", "badge", "document", "querySelector", "style", "animation", "title", "button", "transform", "onSubmit", "onChange", "placeholder", "required", "disabled", "<PERSON><PERSON><PERSON><PERSON>", "undefined", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/vedika/ai-generative/client/src/components/AuthForm.jsx"], "sourcesContent": ["import { useState } from 'react';\r\nimport './AuthForm.css';\r\n\r\nconst AuthForm = ({ user, setUser, onNavigate }) => {\r\n  const [isLogin, setIsLogin] = useState(true);\r\n  const [formData, setFormData] = useState({\r\n    username: '',\r\n    email: '',\r\n    password: '',\r\n    confirmPassword: ''\r\n  });\r\n  const [loading, setLoading] = useState(false);\r\n  const [message, setMessage] = useState('');\r\n  const [messageType, setMessageType] = useState(''); // 'success' or 'error'\r\n\r\n  // Get API base URL\r\n  const getApiUrl = () => {\r\n    return process.env.NODE_ENV === 'production'\r\n      ? '/api'\r\n      : 'http://localhost:5000/api';\r\n  };\r\n\r\n  const handleInputChange = (e) => {\r\n    setFormData({\r\n      ...formData,\r\n      [e.target.name]: e.target.value\r\n    });\r\n  };\r\n\r\n  const showMessage = (text, type) => {\r\n    setMessage(text);\r\n    setMessageType(type);\r\n    setTimeout(() => {\r\n      setMessage('');\r\n      setMessageType('');\r\n    }, 5000);\r\n  };\r\n\r\n  const handleSubmit = async (e) => {\r\n    e.preventDefault();\r\n    setLoading(true);\r\n\r\n    try {\r\n      if (!isLogin) {\r\n        // Registration validation\r\n        if (formData.password !== formData.confirmPassword) {\r\n          showMessage('Passwords do not match!', 'error');\r\n          setLoading(false);\r\n          return;\r\n        }\r\n        if (formData.password.length < 6) {\r\n          showMessage('Password must be at least 6 characters long!', 'error');\r\n          setLoading(false);\r\n          return;\r\n        }\r\n      }\r\n\r\n      const endpoint = isLogin ? '/auth/login' : '/auth/register';\r\n      const payload = isLogin\r\n        ? { email: formData.email, password: formData.password }\r\n        : {\r\n            username: formData.username,\r\n            email: formData.email,\r\n            password: formData.password\r\n          };\r\n\r\n      const response = await fetch(`${getApiUrl()}${endpoint}`, {\r\n        method: 'POST',\r\n        headers: {\r\n          'Content-Type': 'application/json',\r\n        },\r\n        body: JSON.stringify(payload),\r\n      });\r\n\r\n      const data = await response.json();\r\n\r\n      if (response.ok) {\r\n        // Success\r\n        localStorage.setItem('authToken', data.token);\r\n        localStorage.setItem('userData', JSON.stringify(data.user));\r\n        setUser(data.user);\r\n        showMessage(\r\n          isLogin\r\n            ? `Welcome back, ${data.user.username}! 🎉`\r\n            : `🎉 ACCOUNT CREATED! Welcome to the revolution, ${data.user.username}! 🚀`,\r\n          'success'\r\n        );\r\n\r\n        // Clear form\r\n        setFormData({\r\n          username: '',\r\n          email: '',\r\n          password: '',\r\n          confirmPassword: ''\r\n        });\r\n\r\n        // Auto-navigate to generate tab after successful registration\r\n        if (!isLogin && onNavigate) {\r\n          setTimeout(() => {\r\n            onNavigate('generate');\r\n          }, 2000); // Wait 2 seconds to show success message\r\n        }\r\n      } else {\r\n        // Error\r\n        showMessage(data.error || 'Something went wrong!', 'error');\r\n      }\r\n    } catch (error) {\r\n      showMessage('Unable to connect to the server. Please try again later.', 'error');\r\n    } finally {\r\n      setLoading(false);\r\n    }\r\n  };\r\n\r\n  const toggleMode = () => {\r\n    setIsLogin(!isLogin);\r\n    setMessage('');\r\n    setFormData({\r\n      username: '',\r\n      email: '',\r\n      password: '',\r\n      confirmPassword: ''\r\n    });\r\n  };\r\n\r\n  if (user) {\r\n    return (\r\n      <div className=\"auth-container\">\r\n        <div className=\"user-profile\">\r\n          <div className=\"profile-celebration\">\r\n            <div className=\"celebration-emojis\">\r\n              <span className=\"celebration-emoji\">🎉</span>\r\n              <span className=\"celebration-emoji\">✨</span>\r\n              <span className=\"celebration-emoji\">🚀</span>\r\n              <span className=\"celebration-emoji\">🎯</span>\r\n            </div>\r\n          </div>\r\n\r\n          <div className=\"profile-header\">\r\n            <div className=\"profile-avatar\">\r\n              <span className=\"avatar-emoji\">🎨</span>\r\n              <div className=\"avatar-glow\"></div>\r\n            </div>\r\n            <div className=\"profile-info\">\r\n              <h3 className=\"profile-name\">\r\n                <span className=\"welcome-emoji\">👋</span>\r\n                Welcome, {user.username}!\r\n              </h3>\r\n              <p className=\"profile-email\">{user.email}</p>\r\n              <div\r\n                className=\"profile-badge\"\r\n                onClick={() => {\r\n                  // Add a fun interaction\r\n                  const badge = document.querySelector('.profile-badge');\r\n                  badge.style.animation = 'none';\r\n                  setTimeout(() => {\r\n                    badge.style.animation = 'badgeGlow 2s ease-in-out infinite, badgeClick 0.6s ease-out';\r\n                  }, 10);\r\n                }}\r\n                title=\"Click for power boost! ⚡\"\r\n              >\r\n                <span className=\"badge-icon\">⚡</span>\r\n                <span className=\"badge-text\">Creator Activated</span>\r\n              </div>\r\n            </div>\r\n          </div>\r\n\r\n          <div className=\"profile-stats\">\r\n            <div className=\"stat-card\">\r\n              <span className=\"stat-icon\">🧠</span>\r\n              <div className=\"stat-content\">\r\n                <h4>AI Power</h4>\r\n                <p>Unlimited</p>\r\n              </div>\r\n            </div>\r\n            <div className=\"stat-card\">\r\n              <span className=\"stat-icon\">⚡</span>\r\n              <div className=\"stat-content\">\r\n                <h4>Speed</h4>\r\n                <p>Lightning Fast</p>\r\n              </div>\r\n            </div>\r\n            <div className=\"stat-card\">\r\n              <span className=\"stat-icon\">🎨</span>\r\n              <div className=\"stat-content\">\r\n                <h4>Creativity</h4>\r\n                <p>Boundless</p>\r\n              </div>\r\n            </div>\r\n          </div>\r\n\r\n          <div className=\"cta-section\">\r\n            <h4 className=\"cta-title\">\r\n              <span className=\"cta-icon\">🚀</span>\r\n              Ready to Create Magic?\r\n            </h4>\r\n            <p className=\"cta-subtitle\">\r\n              Your AI-powered content creation journey starts now!\r\n            </p>\r\n          </div>\r\n\r\n          <div className=\"profile-actions\">\r\n            <button\r\n              className=\"profile-btn generate-btn mega-cta\"\r\n              onClick={() => {\r\n                // Add success feedback\r\n                const button = document.querySelector('.mega-cta');\r\n                button.style.transform = 'scale(0.95)';\r\n                setTimeout(() => {\r\n                  button.style.transform = '';\r\n                  onNavigate && onNavigate('generate');\r\n                }, 150);\r\n              }}\r\n            >\r\n              <div className=\"btn-bg-effect\"></div>\r\n              <span className=\"btn-content\">\r\n                <span className=\"btn-icon\">🚀</span>\r\n                <span className=\"btn-text\">START CREATING NOW</span>\r\n                <div className=\"btn-particles\">\r\n                  <span className=\"particle\">✨</span>\r\n                  <span className=\"particle\">💫</span>\r\n                  <span className=\"particle\">⭐</span>\r\n                </div>\r\n              </span>\r\n            </button>\r\n            <button\r\n              className=\"profile-btn history-btn secondary-btn\"\r\n              onClick={() => onNavigate && onNavigate('history')}\r\n            >\r\n              <span className=\"btn-icon\">📚</span>\r\n              <span className=\"btn-text\">View History</span>\r\n            </button>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    );\r\n  }\r\n\r\n  return (\r\n    <div className=\"auth-container\">\r\n      <div className=\"auth-form-wrapper\">\r\n        <div className=\"auth-header\">\r\n          <div className=\"auth-illustration\">\r\n            <div className=\"magic-circle\">\r\n              <div className=\"orbit orbit-1\">\r\n                <span className=\"orbit-emoji\">🎨</span>\r\n              </div>\r\n              <div className=\"orbit orbit-2\">\r\n                <span className=\"orbit-emoji\">✨</span>\r\n              </div>\r\n              <div className=\"orbit orbit-3\">\r\n                <span className=\"orbit-emoji\">🚀</span>\r\n              </div>\r\n              <div className=\"center-logo\">\r\n                <span className=\"logo-main\">🤖</span>\r\n                <div className=\"energy-rings\">\r\n                  <div className=\"ring ring-1\"></div>\r\n                  <div className=\"ring ring-2\"></div>\r\n                  <div className=\"ring ring-3\"></div>\r\n                </div>\r\n              </div>\r\n            </div>\r\n          </div>\r\n\r\n          <div className=\"title-container\">\r\n            <h2 className=\"auth-title\">\r\n              <span className=\"title-text\">\r\n                {isLogin ? 'Welcome Back, Creator!' : 'Unleash Your Creative Power!'}\r\n              </span>\r\n              <div className=\"title-effects\">\r\n                <span className=\"title-decoration explosive\">\r\n                  {isLogin ? '⚡' : '💥'}\r\n                </span>\r\n                <div className=\"sparkle-trail\">\r\n                  <span className=\"sparkle\">✨</span>\r\n                  <span className=\"sparkle\">⭐</span>\r\n                  <span className=\"sparkle\">💫</span>\r\n                </div>\r\n              </div>\r\n            </h2>\r\n\r\n            <div className=\"subtitle-container\">\r\n              <p className=\"auth-subtitle\">\r\n                {isLogin\r\n                  ? '🎯 Ready to create mind-blowing content with AI superpowers?'\r\n                  : '🌟 Join thousands of creators transforming ideas into reality!'\r\n                }\r\n              </p>\r\n              <div className=\"impact-stats\">\r\n                <div className=\"stat-bubble\">\r\n                  <span className=\"stat-number\">10K+</span>\r\n                  <span className=\"stat-label\">Creators</span>\r\n                </div>\r\n                <div className=\"stat-bubble\">\r\n                  <span className=\"stat-number\">1M+</span>\r\n                  <span className=\"stat-label\">Content</span>\r\n                </div>\r\n                <div className=\"stat-bubble\">\r\n                  <span className=\"stat-number\">∞</span>\r\n                  <span className=\"stat-label\">Possibilities</span>\r\n                </div>\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </div>\r\n\r\n        <form onSubmit={handleSubmit} className=\"auth-form\">\r\n          {!isLogin && (\r\n            <div className=\"form-group\">\r\n              <label className=\"form-label\">\r\n                <span className=\"label-icon\">👤</span>\r\n                Username\r\n              </label>\r\n              <input\r\n                type=\"text\"\r\n                name=\"username\"\r\n                value={formData.username}\r\n                onChange={handleInputChange}\r\n                placeholder=\"Choose a creative username\"\r\n                className=\"form-input\"\r\n                required={!isLogin}\r\n                disabled={loading}\r\n              />\r\n            </div>\r\n          )}\r\n\r\n          <div className=\"form-group\">\r\n            <label className=\"form-label\">\r\n              <span className=\"label-icon\">📧</span>\r\n              Email\r\n            </label>\r\n            <input\r\n              type=\"email\"\r\n              name=\"email\"\r\n              value={formData.email}\r\n              onChange={handleInputChange}\r\n              placeholder=\"<EMAIL>\"\r\n              className=\"form-input\"\r\n              required\r\n              disabled={loading}\r\n            />\r\n          </div>\r\n\r\n          <div className=\"form-group\">\r\n            <label className=\"form-label\">\r\n              <span className=\"label-icon\">🔒</span>\r\n              Password\r\n            </label>\r\n            <input\r\n              type=\"password\"\r\n              name=\"password\"\r\n              value={formData.password}\r\n              onChange={handleInputChange}\r\n              placeholder={isLogin ? \"Enter your password\" : \"Create a secure password (6+ characters)\"}\r\n              className=\"form-input\"\r\n              required\r\n              disabled={loading}\r\n              minLength={isLogin ? undefined : 6}\r\n            />\r\n          </div>\r\n\r\n          {!isLogin && (\r\n            <div className=\"form-group\">\r\n              <label className=\"form-label\">\r\n                <span className=\"label-icon\">🔐</span>\r\n                Confirm Password\r\n              </label>\r\n              <input\r\n                type=\"password\"\r\n                name=\"confirmPassword\"\r\n                value={formData.confirmPassword}\r\n                onChange={handleInputChange}\r\n                placeholder=\"Confirm your password\"\r\n                className=\"form-input\"\r\n                required={!isLogin}\r\n                disabled={loading}\r\n              />\r\n            </div>\r\n          )}\r\n\r\n          {message && (\r\n            <div className={`message ${messageType}`}>\r\n              <span className=\"message-icon\">\r\n                {messageType === 'success' ? '✅' : '❌'}\r\n              </span>\r\n              {message}\r\n            </div>\r\n          )}\r\n\r\n          <button\r\n            type=\"submit\"\r\n            className=\"auth-submit-btn mega-button\"\r\n            disabled={loading}\r\n          >\r\n            <div className=\"button-bg-effect\"></div>\r\n            <div className=\"button-content\">\r\n              {loading ? (\r\n                <>\r\n                  <div className=\"loading-animation\">\r\n                    <div className=\"loading-orb\"></div>\r\n                    <div className=\"loading-particles\">\r\n                      <span className=\"particle\">✨</span>\r\n                      <span className=\"particle\">⭐</span>\r\n                      <span className=\"particle\">💫</span>\r\n                    </div>\r\n                  </div>\r\n                  <span className=\"loading-text\">\r\n                    {isLogin ? '🔮 Authenticating Magic...' : '🚀 Launching Your Journey...'}\r\n                  </span>\r\n                </>\r\n              ) : (\r\n                <>\r\n                  <span className=\"btn-icon mega-icon\">\r\n                    {isLogin ? '⚡' : '�'}\r\n                  </span>\r\n                  <span className=\"btn-text\">\r\n                    {isLogin ? 'UNLEASH CREATIVITY' : 'START MY JOURNEY'}\r\n                  </span>\r\n                  <div className=\"button-particles\">\r\n                    <span className=\"btn-particle\">✨</span>\r\n                    <span className=\"btn-particle\">💥</span>\r\n                    <span className=\"btn-particle\">🚀</span>\r\n                  </div>\r\n                </>\r\n              )}\r\n            </div>\r\n          </button>\r\n\r\n          {!isLogin && (\r\n            <div className=\"power-features\">\r\n              <h4 className=\"features-title\">\r\n                <span className=\"features-icon\">⚡</span>\r\n                Unlock These Superpowers\r\n              </h4>\r\n              <div className=\"features-grid\">\r\n                <div className=\"feature-item\">\r\n                  <span className=\"feature-emoji\">🧠</span>\r\n                  <span className=\"feature-text\">AI Brain Power</span>\r\n                </div>\r\n                <div className=\"feature-item\">\r\n                  <span className=\"feature-emoji\">⚡</span>\r\n                  <span className=\"feature-text\">Lightning Speed</span>\r\n                </div>\r\n                <div className=\"feature-item\">\r\n                  <span className=\"feature-emoji\">🎨</span>\r\n                  <span className=\"feature-text\">Unlimited Creativity</span>\r\n                </div>\r\n                <div className=\"feature-item\">\r\n                  <span className=\"feature-emoji\">🚀</span>\r\n                  <span className=\"feature-text\">Instant Results</span>\r\n                </div>\r\n              </div>\r\n            </div>\r\n          )}\r\n        </form>\r\n\r\n        <div className=\"auth-footer\">\r\n          <div className=\"switch-container\">\r\n            <p className=\"auth-switch-text\">\r\n              {isLogin ? \"🌟 Ready to join the revolution?\" : \"🚀 Already part of the magic?\"}\r\n            </p>\r\n            <button\r\n              type=\"button\"\r\n              onClick={toggleMode}\r\n              className=\"auth-switch-btn impact-switch\"\r\n              disabled={loading}\r\n            >\r\n              <div className=\"switch-bg-effect\"></div>\r\n              <span className=\"switch-content\">\r\n                <span className=\"switch-icon explosive\">\r\n                  {isLogin ? '💥' : '⚡'}\r\n                </span>\r\n                <span className=\"switch-text\">\r\n                  {isLogin ? 'JOIN THE REVOLUTION' : 'ENTER THE MATRIX'}\r\n                </span>\r\n              </span>\r\n            </button>\r\n          </div>\r\n\r\n          <div className=\"trust-indicators\">\r\n            <div className=\"trust-item\">\r\n              <span className=\"trust-icon\">🔒</span>\r\n              <span className=\"trust-text\">Bank-Level Security</span>\r\n            </div>\r\n            <div className=\"trust-item\">\r\n              <span className=\"trust-icon\">⚡</span>\r\n              <span className=\"trust-text\">Instant Access</span>\r\n            </div>\r\n            <div className=\"trust-item\">\r\n              <span className=\"trust-icon\">🌍</span>\r\n              <span className=\"trust-text\">Global Community</span>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default AuthForm;"], "mappings": ";;AAAA,SAASA,QAAQ,QAAQ,OAAO;AAChC,OAAO,gBAAgB;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAExB,MAAMC,QAAQ,GAAGA,CAAC;EAAEC,IAAI;EAAEC,OAAO;EAAEC;AAAW,CAAC,KAAK;EAAAC,EAAA;EAClD,MAAM,CAACC,OAAO,EAAEC,UAAU,CAAC,GAAGX,QAAQ,CAAC,IAAI,CAAC;EAC5C,MAAM,CAACY,QAAQ,EAAEC,WAAW,CAAC,GAAGb,QAAQ,CAAC;IACvCc,QAAQ,EAAE,EAAE;IACZC,KAAK,EAAE,EAAE;IACTC,QAAQ,EAAE,EAAE;IACZC,eAAe,EAAE;EACnB,CAAC,CAAC;EACF,MAAM,CAACC,OAAO,EAAEC,UAAU,CAAC,GAAGnB,QAAQ,CAAC,KAAK,CAAC;EAC7C,MAAM,CAACoB,OAAO,EAAEC,UAAU,CAAC,GAAGrB,QAAQ,CAAC,EAAE,CAAC;EAC1C,MAAM,CAACsB,WAAW,EAAEC,cAAc,CAAC,GAAGvB,QAAQ,CAAC,EAAE,CAAC,CAAC,CAAC;;EAEpD;EACA,MAAMwB,SAAS,GAAGA,CAAA,KAAM;IACtB,OAAOC,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,GACxC,MAAM,GACN,2BAA2B;EACjC,CAAC;EAED,MAAMC,iBAAiB,GAAIC,CAAC,IAAK;IAC/BhB,WAAW,CAAC;MACV,GAAGD,QAAQ;MACX,CAACiB,CAAC,CAACC,MAAM,CAACC,IAAI,GAAGF,CAAC,CAACC,MAAM,CAACE;IAC5B,CAAC,CAAC;EACJ,CAAC;EAED,MAAMC,WAAW,GAAGA,CAACC,IAAI,EAAEC,IAAI,KAAK;IAClCd,UAAU,CAACa,IAAI,CAAC;IAChBX,cAAc,CAACY,IAAI,CAAC;IACpBC,UAAU,CAAC,MAAM;MACff,UAAU,CAAC,EAAE,CAAC;MACdE,cAAc,CAAC,EAAE,CAAC;IACpB,CAAC,EAAE,IAAI,CAAC;EACV,CAAC;EAED,MAAMc,YAAY,GAAG,MAAOR,CAAC,IAAK;IAChCA,CAAC,CAACS,cAAc,CAAC,CAAC;IAClBnB,UAAU,CAAC,IAAI,CAAC;IAEhB,IAAI;MACF,IAAI,CAACT,OAAO,EAAE;QACZ;QACA,IAAIE,QAAQ,CAACI,QAAQ,KAAKJ,QAAQ,CAACK,eAAe,EAAE;UAClDgB,WAAW,CAAC,yBAAyB,EAAE,OAAO,CAAC;UAC/Cd,UAAU,CAAC,KAAK,CAAC;UACjB;QACF;QACA,IAAIP,QAAQ,CAACI,QAAQ,CAACuB,MAAM,GAAG,CAAC,EAAE;UAChCN,WAAW,CAAC,8CAA8C,EAAE,OAAO,CAAC;UACpEd,UAAU,CAAC,KAAK,CAAC;UACjB;QACF;MACF;MAEA,MAAMqB,QAAQ,GAAG9B,OAAO,GAAG,aAAa,GAAG,gBAAgB;MAC3D,MAAM+B,OAAO,GAAG/B,OAAO,GACnB;QAAEK,KAAK,EAAEH,QAAQ,CAACG,KAAK;QAAEC,QAAQ,EAAEJ,QAAQ,CAACI;MAAS,CAAC,GACtD;QACEF,QAAQ,EAAEF,QAAQ,CAACE,QAAQ;QAC3BC,KAAK,EAAEH,QAAQ,CAACG,KAAK;QACrBC,QAAQ,EAAEJ,QAAQ,CAACI;MACrB,CAAC;MAEL,MAAM0B,QAAQ,GAAG,MAAMC,KAAK,CAAC,GAAGnB,SAAS,CAAC,CAAC,GAAGgB,QAAQ,EAAE,EAAE;QACxDI,MAAM,EAAE,MAAM;QACdC,OAAO,EAAE;UACP,cAAc,EAAE;QAClB,CAAC;QACDC,IAAI,EAAEC,IAAI,CAACC,SAAS,CAACP,OAAO;MAC9B,CAAC,CAAC;MAEF,MAAMQ,IAAI,GAAG,MAAMP,QAAQ,CAACQ,IAAI,CAAC,CAAC;MAElC,IAAIR,QAAQ,CAACS,EAAE,EAAE;QACf;QACAC,YAAY,CAACC,OAAO,CAAC,WAAW,EAAEJ,IAAI,CAACK,KAAK,CAAC;QAC7CF,YAAY,CAACC,OAAO,CAAC,UAAU,EAAEN,IAAI,CAACC,SAAS,CAACC,IAAI,CAAC3C,IAAI,CAAC,CAAC;QAC3DC,OAAO,CAAC0C,IAAI,CAAC3C,IAAI,CAAC;QAClB2B,WAAW,CACTvB,OAAO,GACH,iBAAiBuC,IAAI,CAAC3C,IAAI,CAACQ,QAAQ,MAAM,GACzC,kDAAkDmC,IAAI,CAAC3C,IAAI,CAACQ,QAAQ,MAAM,EAC9E,SACF,CAAC;;QAED;QACAD,WAAW,CAAC;UACVC,QAAQ,EAAE,EAAE;UACZC,KAAK,EAAE,EAAE;UACTC,QAAQ,EAAE,EAAE;UACZC,eAAe,EAAE;QACnB,CAAC,CAAC;;QAEF;QACA,IAAI,CAACP,OAAO,IAAIF,UAAU,EAAE;UAC1B4B,UAAU,CAAC,MAAM;YACf5B,UAAU,CAAC,UAAU,CAAC;UACxB,CAAC,EAAE,IAAI,CAAC,CAAC,CAAC;QACZ;MACF,CAAC,MAAM;QACL;QACAyB,WAAW,CAACgB,IAAI,CAACM,KAAK,IAAI,uBAAuB,EAAE,OAAO,CAAC;MAC7D;IACF,CAAC,CAAC,OAAOA,KAAK,EAAE;MACdtB,WAAW,CAAC,0DAA0D,EAAE,OAAO,CAAC;IAClF,CAAC,SAAS;MACRd,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;EAED,MAAMqC,UAAU,GAAGA,CAAA,KAAM;IACvB7C,UAAU,CAAC,CAACD,OAAO,CAAC;IACpBW,UAAU,CAAC,EAAE,CAAC;IACdR,WAAW,CAAC;MACVC,QAAQ,EAAE,EAAE;MACZC,KAAK,EAAE,EAAE;MACTC,QAAQ,EAAE,EAAE;MACZC,eAAe,EAAE;IACnB,CAAC,CAAC;EACJ,CAAC;EAED,IAAIX,IAAI,EAAE;IACR,oBACEJ,OAAA;MAAKuD,SAAS,EAAC,gBAAgB;MAAAC,QAAA,eAC7BxD,OAAA;QAAKuD,SAAS,EAAC,cAAc;QAAAC,QAAA,gBAC3BxD,OAAA;UAAKuD,SAAS,EAAC,qBAAqB;UAAAC,QAAA,eAClCxD,OAAA;YAAKuD,SAAS,EAAC,oBAAoB;YAAAC,QAAA,gBACjCxD,OAAA;cAAMuD,SAAS,EAAC,mBAAmB;cAAAC,QAAA,EAAC;YAAE;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eAC7C5D,OAAA;cAAMuD,SAAS,EAAC,mBAAmB;cAAAC,QAAA,EAAC;YAAC;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eAC5C5D,OAAA;cAAMuD,SAAS,EAAC,mBAAmB;cAAAC,QAAA,EAAC;YAAE;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eAC7C5D,OAAA;cAAMuD,SAAS,EAAC,mBAAmB;cAAAC,QAAA,EAAC;YAAE;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC1C;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAEN5D,OAAA;UAAKuD,SAAS,EAAC,gBAAgB;UAAAC,QAAA,gBAC7BxD,OAAA;YAAKuD,SAAS,EAAC,gBAAgB;YAAAC,QAAA,gBAC7BxD,OAAA;cAAMuD,SAAS,EAAC,cAAc;cAAAC,QAAA,EAAC;YAAE;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eACxC5D,OAAA;cAAKuD,SAAS,EAAC;YAAa;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAChC,CAAC,eACN5D,OAAA;YAAKuD,SAAS,EAAC,cAAc;YAAAC,QAAA,gBAC3BxD,OAAA;cAAIuD,SAAS,EAAC,cAAc;cAAAC,QAAA,gBAC1BxD,OAAA;gBAAMuD,SAAS,EAAC,eAAe;gBAAAC,QAAA,EAAC;cAAE;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,aAChC,EAACxD,IAAI,CAACQ,QAAQ,EAAC,GAC1B;YAAA;cAAA6C,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACL5D,OAAA;cAAGuD,SAAS,EAAC,eAAe;cAAAC,QAAA,EAAEpD,IAAI,CAACS;YAAK;cAAA4C,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eAC7C5D,OAAA;cACEuD,SAAS,EAAC,eAAe;cACzBM,OAAO,EAAEA,CAAA,KAAM;gBACb;gBACA,MAAMC,KAAK,GAAGC,QAAQ,CAACC,aAAa,CAAC,gBAAgB,CAAC;gBACtDF,KAAK,CAACG,KAAK,CAACC,SAAS,GAAG,MAAM;gBAC9BhC,UAAU,CAAC,MAAM;kBACf4B,KAAK,CAACG,KAAK,CAACC,SAAS,GAAG,6DAA6D;gBACvF,CAAC,EAAE,EAAE,CAAC;cACR,CAAE;cACFC,KAAK,EAAC,+BAA0B;cAAAX,QAAA,gBAEhCxD,OAAA;gBAAMuD,SAAS,EAAC,YAAY;gBAAAC,QAAA,EAAC;cAAC;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,eACrC5D,OAAA;gBAAMuD,SAAS,EAAC,YAAY;gBAAAC,QAAA,EAAC;cAAiB;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAClD,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAEN5D,OAAA;UAAKuD,SAAS,EAAC,eAAe;UAAAC,QAAA,gBAC5BxD,OAAA;YAAKuD,SAAS,EAAC,WAAW;YAAAC,QAAA,gBACxBxD,OAAA;cAAMuD,SAAS,EAAC,WAAW;cAAAC,QAAA,EAAC;YAAE;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eACrC5D,OAAA;cAAKuD,SAAS,EAAC,cAAc;cAAAC,QAAA,gBAC3BxD,OAAA;gBAAAwD,QAAA,EAAI;cAAQ;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACjB5D,OAAA;gBAAAwD,QAAA,EAAG;cAAS;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACb,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eACN5D,OAAA;YAAKuD,SAAS,EAAC,WAAW;YAAAC,QAAA,gBACxBxD,OAAA;cAAMuD,SAAS,EAAC,WAAW;cAAAC,QAAA,EAAC;YAAC;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eACpC5D,OAAA;cAAKuD,SAAS,EAAC,cAAc;cAAAC,QAAA,gBAC3BxD,OAAA;gBAAAwD,QAAA,EAAI;cAAK;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACd5D,OAAA;gBAAAwD,QAAA,EAAG;cAAc;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAClB,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eACN5D,OAAA;YAAKuD,SAAS,EAAC,WAAW;YAAAC,QAAA,gBACxBxD,OAAA;cAAMuD,SAAS,EAAC,WAAW;cAAAC,QAAA,EAAC;YAAE;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eACrC5D,OAAA;cAAKuD,SAAS,EAAC,cAAc;cAAAC,QAAA,gBAC3BxD,OAAA;gBAAAwD,QAAA,EAAI;cAAU;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACnB5D,OAAA;gBAAAwD,QAAA,EAAG;cAAS;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACb,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAEN5D,OAAA;UAAKuD,SAAS,EAAC,aAAa;UAAAC,QAAA,gBAC1BxD,OAAA;YAAIuD,SAAS,EAAC,WAAW;YAAAC,QAAA,gBACvBxD,OAAA;cAAMuD,SAAS,EAAC,UAAU;cAAAC,QAAA,EAAC;YAAE;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,0BAEtC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACL5D,OAAA;YAAGuD,SAAS,EAAC,cAAc;YAAAC,QAAA,EAAC;UAE5B;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACD,CAAC,eAEN5D,OAAA;UAAKuD,SAAS,EAAC,iBAAiB;UAAAC,QAAA,gBAC9BxD,OAAA;YACEuD,SAAS,EAAC,mCAAmC;YAC7CM,OAAO,EAAEA,CAAA,KAAM;cACb;cACA,MAAMO,MAAM,GAAGL,QAAQ,CAACC,aAAa,CAAC,WAAW,CAAC;cAClDI,MAAM,CAACH,KAAK,CAACI,SAAS,GAAG,aAAa;cACtCnC,UAAU,CAAC,MAAM;gBACfkC,MAAM,CAACH,KAAK,CAACI,SAAS,GAAG,EAAE;gBAC3B/D,UAAU,IAAIA,UAAU,CAAC,UAAU,CAAC;cACtC,CAAC,EAAE,GAAG,CAAC;YACT,CAAE;YAAAkD,QAAA,gBAEFxD,OAAA;cAAKuD,SAAS,EAAC;YAAe;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eACrC5D,OAAA;cAAMuD,SAAS,EAAC,aAAa;cAAAC,QAAA,gBAC3BxD,OAAA;gBAAMuD,SAAS,EAAC,UAAU;gBAAAC,QAAA,EAAC;cAAE;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,eACpC5D,OAAA;gBAAMuD,SAAS,EAAC,UAAU;gBAAAC,QAAA,EAAC;cAAkB;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,eACpD5D,OAAA;gBAAKuD,SAAS,EAAC,eAAe;gBAAAC,QAAA,gBAC5BxD,OAAA;kBAAMuD,SAAS,EAAC,UAAU;kBAAAC,QAAA,EAAC;gBAAC;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,eACnC5D,OAAA;kBAAMuD,SAAS,EAAC,UAAU;kBAAAC,QAAA,EAAC;gBAAE;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,eACpC5D,OAAA;kBAAMuD,SAAS,EAAC,UAAU;kBAAAC,QAAA,EAAC;gBAAC;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAChC,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACF,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACD,CAAC,eACT5D,OAAA;YACEuD,SAAS,EAAC,uCAAuC;YACjDM,OAAO,EAAEA,CAAA,KAAMvD,UAAU,IAAIA,UAAU,CAAC,SAAS,CAAE;YAAAkD,QAAA,gBAEnDxD,OAAA;cAAMuD,SAAS,EAAC,UAAU;cAAAC,QAAA,EAAC;YAAE;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eACpC5D,OAAA;cAAMuD,SAAS,EAAC,UAAU;cAAAC,QAAA,EAAC;YAAY;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACxC,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAEV;EAEA,oBACE5D,OAAA;IAAKuD,SAAS,EAAC,gBAAgB;IAAAC,QAAA,eAC7BxD,OAAA;MAAKuD,SAAS,EAAC,mBAAmB;MAAAC,QAAA,gBAChCxD,OAAA;QAAKuD,SAAS,EAAC,aAAa;QAAAC,QAAA,gBAC1BxD,OAAA;UAAKuD,SAAS,EAAC,mBAAmB;UAAAC,QAAA,eAChCxD,OAAA;YAAKuD,SAAS,EAAC,cAAc;YAAAC,QAAA,gBAC3BxD,OAAA;cAAKuD,SAAS,EAAC,eAAe;cAAAC,QAAA,eAC5BxD,OAAA;gBAAMuD,SAAS,EAAC,aAAa;gBAAAC,QAAA,EAAC;cAAE;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACpC,CAAC,eACN5D,OAAA;cAAKuD,SAAS,EAAC,eAAe;cAAAC,QAAA,eAC5BxD,OAAA;gBAAMuD,SAAS,EAAC,aAAa;gBAAAC,QAAA,EAAC;cAAC;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACnC,CAAC,eACN5D,OAAA;cAAKuD,SAAS,EAAC,eAAe;cAAAC,QAAA,eAC5BxD,OAAA;gBAAMuD,SAAS,EAAC,aAAa;gBAAAC,QAAA,EAAC;cAAE;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACpC,CAAC,eACN5D,OAAA;cAAKuD,SAAS,EAAC,aAAa;cAAAC,QAAA,gBAC1BxD,OAAA;gBAAMuD,SAAS,EAAC,WAAW;gBAAAC,QAAA,EAAC;cAAE;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,eACrC5D,OAAA;gBAAKuD,SAAS,EAAC,cAAc;gBAAAC,QAAA,gBAC3BxD,OAAA;kBAAKuD,SAAS,EAAC;gBAAa;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,eACnC5D,OAAA;kBAAKuD,SAAS,EAAC;gBAAa;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,eACnC5D,OAAA;kBAAKuD,SAAS,EAAC;gBAAa;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAChC,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAEN5D,OAAA;UAAKuD,SAAS,EAAC,iBAAiB;UAAAC,QAAA,gBAC9BxD,OAAA;YAAIuD,SAAS,EAAC,YAAY;YAAAC,QAAA,gBACxBxD,OAAA;cAAMuD,SAAS,EAAC,YAAY;cAAAC,QAAA,EACzBhD,OAAO,GAAG,wBAAwB,GAAG;YAA8B;cAAAiD,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAChE,CAAC,eACP5D,OAAA;cAAKuD,SAAS,EAAC,eAAe;cAAAC,QAAA,gBAC5BxD,OAAA;gBAAMuD,SAAS,EAAC,4BAA4B;gBAAAC,QAAA,EACzChD,OAAO,GAAG,GAAG,GAAG;cAAI;gBAAAiD,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACjB,CAAC,eACP5D,OAAA;gBAAKuD,SAAS,EAAC,eAAe;gBAAAC,QAAA,gBAC5BxD,OAAA;kBAAMuD,SAAS,EAAC,SAAS;kBAAAC,QAAA,EAAC;gBAAC;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,eAClC5D,OAAA;kBAAMuD,SAAS,EAAC,SAAS;kBAAAC,QAAA,EAAC;gBAAC;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,eAClC5D,OAAA;kBAAMuD,SAAS,EAAC,SAAS;kBAAAC,QAAA,EAAC;gBAAE;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAChC,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACJ,CAAC,eAEL5D,OAAA;YAAKuD,SAAS,EAAC,oBAAoB;YAAAC,QAAA,gBACjCxD,OAAA;cAAGuD,SAAS,EAAC,eAAe;cAAAC,QAAA,EACzBhD,OAAO,GACJ,8DAA8D,GAC9D;YAAgE;cAAAiD,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAEnE,CAAC,eACJ5D,OAAA;cAAKuD,SAAS,EAAC,cAAc;cAAAC,QAAA,gBAC3BxD,OAAA;gBAAKuD,SAAS,EAAC,aAAa;gBAAAC,QAAA,gBAC1BxD,OAAA;kBAAMuD,SAAS,EAAC,aAAa;kBAAAC,QAAA,EAAC;gBAAI;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,eACzC5D,OAAA;kBAAMuD,SAAS,EAAC,YAAY;kBAAAC,QAAA,EAAC;gBAAQ;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACzC,CAAC,eACN5D,OAAA;gBAAKuD,SAAS,EAAC,aAAa;gBAAAC,QAAA,gBAC1BxD,OAAA;kBAAMuD,SAAS,EAAC,aAAa;kBAAAC,QAAA,EAAC;gBAAG;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,eACxC5D,OAAA;kBAAMuD,SAAS,EAAC,YAAY;kBAAAC,QAAA,EAAC;gBAAO;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACxC,CAAC,eACN5D,OAAA;gBAAKuD,SAAS,EAAC,aAAa;gBAAAC,QAAA,gBAC1BxD,OAAA;kBAAMuD,SAAS,EAAC,aAAa;kBAAAC,QAAA,EAAC;gBAAC;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,eACtC5D,OAAA;kBAAMuD,SAAS,EAAC,YAAY;kBAAAC,QAAA,EAAC;gBAAa;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC9C,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAEN5D,OAAA;QAAMsE,QAAQ,EAAEnC,YAAa;QAACoB,SAAS,EAAC,WAAW;QAAAC,QAAA,GAChD,CAAChD,OAAO,iBACPR,OAAA;UAAKuD,SAAS,EAAC,YAAY;UAAAC,QAAA,gBACzBxD,OAAA;YAAOuD,SAAS,EAAC,YAAY;YAAAC,QAAA,gBAC3BxD,OAAA;cAAMuD,SAAS,EAAC,YAAY;cAAAC,QAAA,EAAC;YAAE;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,YAExC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC,eACR5D,OAAA;YACEiC,IAAI,EAAC,MAAM;YACXJ,IAAI,EAAC,UAAU;YACfC,KAAK,EAAEpB,QAAQ,CAACE,QAAS;YACzB2D,QAAQ,EAAE7C,iBAAkB;YAC5B8C,WAAW,EAAC,4BAA4B;YACxCjB,SAAS,EAAC,YAAY;YACtBkB,QAAQ,EAAE,CAACjE,OAAQ;YACnBkE,QAAQ,EAAE1D;UAAQ;YAAAyC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACnB,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CACN,eAED5D,OAAA;UAAKuD,SAAS,EAAC,YAAY;UAAAC,QAAA,gBACzBxD,OAAA;YAAOuD,SAAS,EAAC,YAAY;YAAAC,QAAA,gBAC3BxD,OAAA;cAAMuD,SAAS,EAAC,YAAY;cAAAC,QAAA,EAAC;YAAE;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,SAExC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC,eACR5D,OAAA;YACEiC,IAAI,EAAC,OAAO;YACZJ,IAAI,EAAC,OAAO;YACZC,KAAK,EAAEpB,QAAQ,CAACG,KAAM;YACtB0D,QAAQ,EAAE7C,iBAAkB;YAC5B8C,WAAW,EAAC,wBAAwB;YACpCjB,SAAS,EAAC,YAAY;YACtBkB,QAAQ;YACRC,QAAQ,EAAE1D;UAAQ;YAAAyC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACnB,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC,eAEN5D,OAAA;UAAKuD,SAAS,EAAC,YAAY;UAAAC,QAAA,gBACzBxD,OAAA;YAAOuD,SAAS,EAAC,YAAY;YAAAC,QAAA,gBAC3BxD,OAAA;cAAMuD,SAAS,EAAC,YAAY;cAAAC,QAAA,EAAC;YAAE;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,YAExC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC,eACR5D,OAAA;YACEiC,IAAI,EAAC,UAAU;YACfJ,IAAI,EAAC,UAAU;YACfC,KAAK,EAAEpB,QAAQ,CAACI,QAAS;YACzByD,QAAQ,EAAE7C,iBAAkB;YAC5B8C,WAAW,EAAEhE,OAAO,GAAG,qBAAqB,GAAG,0CAA2C;YAC1F+C,SAAS,EAAC,YAAY;YACtBkB,QAAQ;YACRC,QAAQ,EAAE1D,OAAQ;YAClB2D,SAAS,EAAEnE,OAAO,GAAGoE,SAAS,GAAG;UAAE;YAAAnB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACpC,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC,EAEL,CAACpD,OAAO,iBACPR,OAAA;UAAKuD,SAAS,EAAC,YAAY;UAAAC,QAAA,gBACzBxD,OAAA;YAAOuD,SAAS,EAAC,YAAY;YAAAC,QAAA,gBAC3BxD,OAAA;cAAMuD,SAAS,EAAC,YAAY;cAAAC,QAAA,EAAC;YAAE;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,oBAExC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC,eACR5D,OAAA;YACEiC,IAAI,EAAC,UAAU;YACfJ,IAAI,EAAC,iBAAiB;YACtBC,KAAK,EAAEpB,QAAQ,CAACK,eAAgB;YAChCwD,QAAQ,EAAE7C,iBAAkB;YAC5B8C,WAAW,EAAC,uBAAuB;YACnCjB,SAAS,EAAC,YAAY;YACtBkB,QAAQ,EAAE,CAACjE,OAAQ;YACnBkE,QAAQ,EAAE1D;UAAQ;YAAAyC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACnB,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CACN,EAEA1C,OAAO,iBACNlB,OAAA;UAAKuD,SAAS,EAAE,WAAWnC,WAAW,EAAG;UAAAoC,QAAA,gBACvCxD,OAAA;YAAMuD,SAAS,EAAC,cAAc;YAAAC,QAAA,EAC3BpC,WAAW,KAAK,SAAS,GAAG,GAAG,GAAG;UAAG;YAAAqC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAClC,CAAC,EACN1C,OAAO;QAAA;UAAAuC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACL,CACN,eAED5D,OAAA;UACEiC,IAAI,EAAC,QAAQ;UACbsB,SAAS,EAAC,6BAA6B;UACvCmB,QAAQ,EAAE1D,OAAQ;UAAAwC,QAAA,gBAElBxD,OAAA;YAAKuD,SAAS,EAAC;UAAkB;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eACxC5D,OAAA;YAAKuD,SAAS,EAAC,gBAAgB;YAAAC,QAAA,EAC5BxC,OAAO,gBACNhB,OAAA,CAAAE,SAAA;cAAAsD,QAAA,gBACExD,OAAA;gBAAKuD,SAAS,EAAC,mBAAmB;gBAAAC,QAAA,gBAChCxD,OAAA;kBAAKuD,SAAS,EAAC;gBAAa;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,eACnC5D,OAAA;kBAAKuD,SAAS,EAAC,mBAAmB;kBAAAC,QAAA,gBAChCxD,OAAA;oBAAMuD,SAAS,EAAC,UAAU;oBAAAC,QAAA,EAAC;kBAAC;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC,eACnC5D,OAAA;oBAAMuD,SAAS,EAAC,UAAU;oBAAAC,QAAA,EAAC;kBAAC;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC,eACnC5D,OAAA;oBAAMuD,SAAS,EAAC,UAAU;oBAAAC,QAAA,EAAC;kBAAE;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACjC,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,eACN5D,OAAA;gBAAMuD,SAAS,EAAC,cAAc;gBAAAC,QAAA,EAC3BhD,OAAO,GAAG,4BAA4B,GAAG;cAA8B;gBAAAiD,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACpE,CAAC;YAAA,eACP,CAAC,gBAEH5D,OAAA,CAAAE,SAAA;cAAAsD,QAAA,gBACExD,OAAA;gBAAMuD,SAAS,EAAC,oBAAoB;gBAAAC,QAAA,EACjChD,OAAO,GAAG,GAAG,GAAG;cAAG;gBAAAiD,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAChB,CAAC,eACP5D,OAAA;gBAAMuD,SAAS,EAAC,UAAU;gBAAAC,QAAA,EACvBhD,OAAO,GAAG,oBAAoB,GAAG;cAAkB;gBAAAiD,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAChD,CAAC,eACP5D,OAAA;gBAAKuD,SAAS,EAAC,kBAAkB;gBAAAC,QAAA,gBAC/BxD,OAAA;kBAAMuD,SAAS,EAAC,cAAc;kBAAAC,QAAA,EAAC;gBAAC;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,eACvC5D,OAAA;kBAAMuD,SAAS,EAAC,cAAc;kBAAAC,QAAA,EAAC;gBAAE;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,eACxC5D,OAAA;kBAAMuD,SAAS,EAAC,cAAc;kBAAAC,QAAA,EAAC;gBAAE;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACrC,CAAC;YAAA,eACN;UACH;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACA,CAAC,EAER,CAACpD,OAAO,iBACPR,OAAA;UAAKuD,SAAS,EAAC,gBAAgB;UAAAC,QAAA,gBAC7BxD,OAAA;YAAIuD,SAAS,EAAC,gBAAgB;YAAAC,QAAA,gBAC5BxD,OAAA;cAAMuD,SAAS,EAAC,eAAe;cAAAC,QAAA,EAAC;YAAC;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,4BAE1C;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACL5D,OAAA;YAAKuD,SAAS,EAAC,eAAe;YAAAC,QAAA,gBAC5BxD,OAAA;cAAKuD,SAAS,EAAC,cAAc;cAAAC,QAAA,gBAC3BxD,OAAA;gBAAMuD,SAAS,EAAC,eAAe;gBAAAC,QAAA,EAAC;cAAE;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,eACzC5D,OAAA;gBAAMuD,SAAS,EAAC,cAAc;gBAAAC,QAAA,EAAC;cAAc;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACjD,CAAC,eACN5D,OAAA;cAAKuD,SAAS,EAAC,cAAc;cAAAC,QAAA,gBAC3BxD,OAAA;gBAAMuD,SAAS,EAAC,eAAe;gBAAAC,QAAA,EAAC;cAAC;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,eACxC5D,OAAA;gBAAMuD,SAAS,EAAC,cAAc;gBAAAC,QAAA,EAAC;cAAe;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAClD,CAAC,eACN5D,OAAA;cAAKuD,SAAS,EAAC,cAAc;cAAAC,QAAA,gBAC3BxD,OAAA;gBAAMuD,SAAS,EAAC,eAAe;gBAAAC,QAAA,EAAC;cAAE;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,eACzC5D,OAAA;gBAAMuD,SAAS,EAAC,cAAc;gBAAAC,QAAA,EAAC;cAAoB;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACvD,CAAC,eACN5D,OAAA;cAAKuD,SAAS,EAAC,cAAc;cAAAC,QAAA,gBAC3BxD,OAAA;gBAAMuD,SAAS,EAAC,eAAe;gBAAAC,QAAA,EAAC;cAAE;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,eACzC5D,OAAA;gBAAMuD,SAAS,EAAC,cAAc;gBAAAC,QAAA,EAAC;cAAe;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAClD,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CACN;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACG,CAAC,eAEP5D,OAAA;QAAKuD,SAAS,EAAC,aAAa;QAAAC,QAAA,gBAC1BxD,OAAA;UAAKuD,SAAS,EAAC,kBAAkB;UAAAC,QAAA,gBAC/BxD,OAAA;YAAGuD,SAAS,EAAC,kBAAkB;YAAAC,QAAA,EAC5BhD,OAAO,GAAG,kCAAkC,GAAG;UAA+B;YAAAiD,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC9E,CAAC,eACJ5D,OAAA;YACEiC,IAAI,EAAC,QAAQ;YACb4B,OAAO,EAAEP,UAAW;YACpBC,SAAS,EAAC,+BAA+B;YACzCmB,QAAQ,EAAE1D,OAAQ;YAAAwC,QAAA,gBAElBxD,OAAA;cAAKuD,SAAS,EAAC;YAAkB;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eACxC5D,OAAA;cAAMuD,SAAS,EAAC,gBAAgB;cAAAC,QAAA,gBAC9BxD,OAAA;gBAAMuD,SAAS,EAAC,uBAAuB;gBAAAC,QAAA,EACpChD,OAAO,GAAG,IAAI,GAAG;cAAG;gBAAAiD,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACjB,CAAC,eACP5D,OAAA;gBAAMuD,SAAS,EAAC,aAAa;gBAAAC,QAAA,EAC1BhD,OAAO,GAAG,qBAAqB,GAAG;cAAkB;gBAAAiD,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACjD,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACD,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC,eAEN5D,OAAA;UAAKuD,SAAS,EAAC,kBAAkB;UAAAC,QAAA,gBAC/BxD,OAAA;YAAKuD,SAAS,EAAC,YAAY;YAAAC,QAAA,gBACzBxD,OAAA;cAAMuD,SAAS,EAAC,YAAY;cAAAC,QAAA,EAAC;YAAE;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eACtC5D,OAAA;cAAMuD,SAAS,EAAC,YAAY;cAAAC,QAAA,EAAC;YAAmB;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACpD,CAAC,eACN5D,OAAA;YAAKuD,SAAS,EAAC,YAAY;YAAAC,QAAA,gBACzBxD,OAAA;cAAMuD,SAAS,EAAC,YAAY;cAAAC,QAAA,EAAC;YAAC;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eACrC5D,OAAA;cAAMuD,SAAS,EAAC,YAAY;cAAAC,QAAA,EAAC;YAAc;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC/C,CAAC,eACN5D,OAAA;YAAKuD,SAAS,EAAC,YAAY;YAAAC,QAAA,gBACzBxD,OAAA;cAAMuD,SAAS,EAAC,YAAY;cAAAC,QAAA,EAAC;YAAE;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eACtC5D,OAAA;cAAMuD,SAAS,EAAC,YAAY;cAAAC,QAAA,EAAC;YAAgB;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACjD,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAACrD,EAAA,CA7eIJ,QAAQ;AAAA0E,EAAA,GAAR1E,QAAQ;AA+ed,eAAeA,QAAQ;AAAC,IAAA0E,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}