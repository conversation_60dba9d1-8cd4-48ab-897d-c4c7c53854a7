# Vercel Deployment Guide

## Prerequisites
1. Create account at https://vercel.com
2. Install Vercel CLI: `npm install -g vercel`
3. Connect your GitHub account to Vercel

## Frontend Deployment (Vercel)

### Step 1: Prepare Frontend
```bash
cd ai-generative/client
```

### Step 2: Create vercel.json
```json
{
  "version": 2,
  "builds": [
    {
      "src": "package.json",
      "use": "@vercel/static-build",
      "config": {
        "distDir": "build"
      }
    }
  ],
  "routes": [
    {
      "src": "/static/(.*)",
      "headers": {
        "cache-control": "s-maxage=********,immutable"
      }
    },
    {
      "src": "/(.*)",
      "dest": "/index.html"
    }
  ]
}
```

### Step 3: Deploy Frontend
```bash
# Login to Vercel
vercel login

# Deploy
vercel

# Follow prompts:
# - Set up and deploy? Y
# - Which scope? (your account)
# - Link to existing project? N
# - Project name: ai-content-generator-frontend
# - Directory: ./
# - Override settings? N
```

### Step 4: Set Environment Variables
```bash
# Set production API URL
vercel env add REACT_APP_API_URL
# Enter: https://your-backend-app.herokuapp.com

# Deploy with new environment
vercel --prod
```

## Backend Deployment (Railway/Render)

### Option A: Railway
1. Go to https://railway.app
2. Connect GitHub account
3. Deploy from GitHub repository
4. Set environment variables in Railway dashboard

### Option B: Render
1. Go to https://render.com
2. Connect GitHub account
3. Create new Web Service
4. Set build command: `npm install`
5. Set start command: `npm start`
6. Add environment variables

## Full Stack Vercel Deployment

### Step 1: Create API Routes in Frontend
Create `api` folder in `client/src/`:

```javascript
// client/api/generate.js
export default async function handler(req, res) {
  if (req.method === 'POST') {
    // Your API logic here
    // This runs on Vercel's serverless functions
  }
}
```

### Step 2: Update vercel.json
```json
{
  "version": 2,
  "functions": {
    "api/**/*.js": {
      "runtime": "@vercel/node"
    }
  },
  "routes": [
    {
      "src": "/api/(.*)",
      "dest": "/api/$1"
    },
    {
      "src": "/(.*)",
      "dest": "/index.html"
    }
  ]
}
```

## Environment Variables
```bash
# Add all required environment variables
vercel env add OPENAI_API_KEY
vercel env add JWT_SECRET
vercel env add NODE_ENV

# Redeploy with new environment
vercel --prod
```

## Custom Domain
1. Go to Vercel dashboard
2. Select your project
3. Go to Settings > Domains
4. Add your custom domain
5. Configure DNS with your domain provider
