
@echo off
echo ========================================
echo AI Content Generator - Starting...
echo ========================================

echo.
echo [1/4] Checking prerequisites...
where node >nul 2>nul
if %errorlevel% neq 0 (
    echo ERROR: Node.js is not installed!
    echo Please install Node.js from https://nodejs.org/
    pause
    exit /b 1
)

echo [2/4] Starting Backend Server...
cd ai-generative\backend
if not exist node_modules (
    echo Installing backend dependencies...
    call npm install
)
start "AI Content Backend" cmd /k "npm start"
timeout /t 3 /nobreak >nul

echo [3/4] Starting Frontend Server...
cd ..\client
if not exist node_modules (
    echo Installing frontend dependencies...
    call npm install
)
start "AI Content Frontend" cmd /k "npm start"

cd ..\..

echo [4/4] Deployment Complete!
echo.
echo ========================================
echo AI Content Generator is now running!
echo ========================================
echo Frontend: http://localhost:3000
echo Backend:  http://localhost:5000
echo ========================================
echo.
echo Both servers are running in separate windows.
echo Close those windows to stop the servers.
echo.
pause