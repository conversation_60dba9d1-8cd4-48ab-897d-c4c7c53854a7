{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\vedika\\\\ai-generative\\\\client\\\\src\\\\components\\\\Editor.jsx\",\n  _s = $RefreshSig$();\nimport { useState } from 'react';\nimport './Editor.css';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst Editor = ({\n  value,\n  onChange,\n  onGenerate,\n  loading\n}) => {\n  _s();\n  const [copied, setCopied] = useState(false);\n  const handleCopy = async () => {\n    if (value) {\n      try {\n        await navigator.clipboard.writeText(value);\n        setCopied(true);\n        setTimeout(() => setCopied(false), 2000);\n      } catch (err) {\n        console.error('Failed to copy text: ', err);\n      }\n    }\n  };\n  const handleClear = () => {\n    onChange && onChange('');\n  };\n  const wordCount = value ? value.trim().split(/\\s+/).length : 0;\n  const charCount = value ? value.length : 0;\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"editor-container\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"editor-header\",\n      children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n        className: \"editor-title\",\n        children: [/*#__PURE__*/_jsxDEV(\"span\", {\n          className: \"editor-icon\",\n          children: \"\\u270D\\uFE0F\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 30,\n          columnNumber: 11\n        }, this), \"Generated Content\"]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 29,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"editor-stats\",\n        children: [/*#__PURE__*/_jsxDEV(\"span\", {\n          className: \"stat\",\n          children: [/*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"stat-icon\",\n            children: \"\\uD83D\\uDCDD\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 35,\n            columnNumber: 13\n          }, this), wordCount, \" words\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 34,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n          className: \"stat\",\n          children: [/*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"stat-icon\",\n            children: \"\\uD83D\\uDD24\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 39,\n            columnNumber: 13\n          }, this), charCount, \" characters\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 38,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 33,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 28,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"editor-wrapper\",\n      children: [loading && /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"loading-overlay\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"loading-content\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"loading-spinner\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 49,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"loading-text\",\n            children: \"Creating amazing content...\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 50,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"loading-emojis\",\n            children: [/*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"loading-emoji\",\n              children: \"\\uD83E\\uDD16\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 52,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"loading-emoji\",\n              children: \"\\u2728\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 53,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"loading-emoji\",\n              children: \"\\uD83D\\uDCDD\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 54,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 51,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 48,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 47,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"textarea\", {\n        className: \"editor-textarea\",\n        rows: \"15\",\n        placeholder: \"\\uD83C\\uDFAF Your AI-generated content will appear here like magic! \\u2728\\r Click 'Generate Content' to start creating amazing content with AI assistance.\",\n        value: value,\n        onChange: e => onChange && onChange(e.target.value),\n        disabled: loading\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 60,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 45,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"editor-actions\",\n      children: [/*#__PURE__*/_jsxDEV(\"button\", {\n        className: \"action-btn copy-btn\",\n        onClick: handleCopy,\n        disabled: !value || loading,\n        title: \"Copy to clipboard\",\n        children: [/*#__PURE__*/_jsxDEV(\"span\", {\n          className: \"btn-icon\",\n          children: copied ? '✅' : '📋'\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 79,\n          columnNumber: 11\n        }, this), copied ? 'Copied!' : 'Copy']\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 73,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n        className: \"action-btn clear-btn\",\n        onClick: handleClear,\n        disabled: !value || loading,\n        title: \"Clear content\",\n        children: [/*#__PURE__*/_jsxDEV(\"span\", {\n          className: \"btn-icon\",\n          children: \"\\uD83D\\uDDD1\\uFE0F\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 89,\n          columnNumber: 11\n        }, this), \"Clear\"]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 83,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n        className: \"action-btn regenerate-btn\",\n        onClick: onGenerate,\n        disabled: loading,\n        title: \"Generate new content\",\n        children: [/*#__PURE__*/_jsxDEV(\"span\", {\n          className: \"btn-icon\",\n          children: \"\\uD83D\\uDD04\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 99,\n          columnNumber: 11\n        }, this), \"Regenerate\"]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 93,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 72,\n      columnNumber: 7\n    }, this), value && !loading && /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"editor-footer\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"success-message\",\n        children: [/*#__PURE__*/_jsxDEV(\"span\", {\n          className: \"success-icon\",\n          children: \"\\uD83C\\uDF89\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 107,\n          columnNumber: 13\n        }, this), \"Content generated successfully! You can edit it above or copy it to use elsewhere.\"]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 106,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 105,\n      columnNumber: 9\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 27,\n    columnNumber: 5\n  }, this);\n};\n_s(Editor, \"NE86rL3vg4NVcTTWDavsT0hUBJs=\");\n_c = Editor;\nexport default Editor;\nvar _c;\n$RefreshReg$(_c, \"Editor\");", "map": {"version": 3, "names": ["useState", "jsxDEV", "_jsxDEV", "Editor", "value", "onChange", "onGenerate", "loading", "_s", "copied", "setCopied", "handleCopy", "navigator", "clipboard", "writeText", "setTimeout", "err", "console", "error", "handleClear", "wordCount", "trim", "split", "length", "charCount", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "rows", "placeholder", "e", "target", "disabled", "onClick", "title", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/vedika/ai-generative/client/src/components/Editor.jsx"], "sourcesContent": ["import { useState } from 'react';\r\nimport './Editor.css';\r\n\r\nconst Editor = ({ value, onChange, onGenerate, loading }) => {\r\n  const [copied, setCopied] = useState(false);\r\n\r\n  const handleCopy = async () => {\r\n    if (value) {\r\n      try {\r\n        await navigator.clipboard.writeText(value);\r\n        setCopied(true);\r\n        setTimeout(() => setCopied(false), 2000);\r\n      } catch (err) {\r\n        console.error('Failed to copy text: ', err);\r\n      }\r\n    }\r\n  };\r\n\r\n  const handleClear = () => {\r\n    onChange && onChange('');\r\n  };\r\n\r\n  const wordCount = value ? value.trim().split(/\\s+/).length : 0;\r\n  const charCount = value ? value.length : 0;\r\n\r\n  return (\r\n    <div className=\"editor-container\">\r\n      <div className=\"editor-header\">\r\n        <h3 className=\"editor-title\">\r\n          <span className=\"editor-icon\">✍️</span>\r\n          Generated Content\r\n        </h3>\r\n        <div className=\"editor-stats\">\r\n          <span className=\"stat\">\r\n            <span className=\"stat-icon\">📝</span>\r\n            {wordCount} words\r\n          </span>\r\n          <span className=\"stat\">\r\n            <span className=\"stat-icon\">🔤</span>\r\n            {charCount} characters\r\n          </span>\r\n        </div>\r\n      </div>\r\n\r\n      <div className=\"editor-wrapper\">\r\n        {loading && (\r\n          <div className=\"loading-overlay\">\r\n            <div className=\"loading-content\">\r\n              <div className=\"loading-spinner\"></div>\r\n              <span className=\"loading-text\">Creating amazing content...</span>\r\n              <div className=\"loading-emojis\">\r\n                <span className=\"loading-emoji\">🤖</span>\r\n                <span className=\"loading-emoji\">✨</span>\r\n                <span className=\"loading-emoji\">📝</span>\r\n              </div>\r\n            </div>\r\n          </div>\r\n        )}\r\n\r\n        <textarea\r\n          className=\"editor-textarea\"\r\n          rows=\"15\"\r\n          placeholder=\"🎯 Your AI-generated content will appear here like magic! ✨\r\n\r\nClick 'Generate Content' to start creating amazing content with AI assistance.\"\r\n          value={value}\r\n          onChange={e => onChange && onChange(e.target.value)}\r\n          disabled={loading}\r\n        />\r\n      </div>\r\n\r\n      <div className=\"editor-actions\">\r\n        <button\r\n          className=\"action-btn copy-btn\"\r\n          onClick={handleCopy}\r\n          disabled={!value || loading}\r\n          title=\"Copy to clipboard\"\r\n        >\r\n          <span className=\"btn-icon\">{copied ? '✅' : '📋'}</span>\r\n          {copied ? 'Copied!' : 'Copy'}\r\n        </button>\r\n\r\n        <button\r\n          className=\"action-btn clear-btn\"\r\n          onClick={handleClear}\r\n          disabled={!value || loading}\r\n          title=\"Clear content\"\r\n        >\r\n          <span className=\"btn-icon\">🗑️</span>\r\n          Clear\r\n        </button>\r\n\r\n        <button\r\n          className=\"action-btn regenerate-btn\"\r\n          onClick={onGenerate}\r\n          disabled={loading}\r\n          title=\"Generate new content\"\r\n        >\r\n          <span className=\"btn-icon\">🔄</span>\r\n          Regenerate\r\n        </button>\r\n      </div>\r\n\r\n      {value && !loading && (\r\n        <div className=\"editor-footer\">\r\n          <div className=\"success-message\">\r\n            <span className=\"success-icon\">🎉</span>\r\n            Content generated successfully! You can edit it above or copy it to use elsewhere.\r\n          </div>\r\n        </div>\r\n      )}\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default Editor;"], "mappings": ";;AAAA,SAASA,QAAQ,QAAQ,OAAO;AAChC,OAAO,cAAc;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEtB,MAAMC,MAAM,GAAGA,CAAC;EAAEC,KAAK;EAAEC,QAAQ;EAAEC,UAAU;EAAEC;AAAQ,CAAC,KAAK;EAAAC,EAAA;EAC3D,MAAM,CAACC,MAAM,EAAEC,SAAS,CAAC,GAAGV,QAAQ,CAAC,KAAK,CAAC;EAE3C,MAAMW,UAAU,GAAG,MAAAA,CAAA,KAAY;IAC7B,IAAIP,KAAK,EAAE;MACT,IAAI;QACF,MAAMQ,SAAS,CAACC,SAAS,CAACC,SAAS,CAACV,KAAK,CAAC;QAC1CM,SAAS,CAAC,IAAI,CAAC;QACfK,UAAU,CAAC,MAAML,SAAS,CAAC,KAAK,CAAC,EAAE,IAAI,CAAC;MAC1C,CAAC,CAAC,OAAOM,GAAG,EAAE;QACZC,OAAO,CAACC,KAAK,CAAC,uBAAuB,EAAEF,GAAG,CAAC;MAC7C;IACF;EACF,CAAC;EAED,MAAMG,WAAW,GAAGA,CAAA,KAAM;IACxBd,QAAQ,IAAIA,QAAQ,CAAC,EAAE,CAAC;EAC1B,CAAC;EAED,MAAMe,SAAS,GAAGhB,KAAK,GAAGA,KAAK,CAACiB,IAAI,CAAC,CAAC,CAACC,KAAK,CAAC,KAAK,CAAC,CAACC,MAAM,GAAG,CAAC;EAC9D,MAAMC,SAAS,GAAGpB,KAAK,GAAGA,KAAK,CAACmB,MAAM,GAAG,CAAC;EAE1C,oBACErB,OAAA;IAAKuB,SAAS,EAAC,kBAAkB;IAAAC,QAAA,gBAC/BxB,OAAA;MAAKuB,SAAS,EAAC,eAAe;MAAAC,QAAA,gBAC5BxB,OAAA;QAAIuB,SAAS,EAAC,cAAc;QAAAC,QAAA,gBAC1BxB,OAAA;UAAMuB,SAAS,EAAC,aAAa;UAAAC,QAAA,EAAC;QAAE;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,qBAEzC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eACL5B,OAAA;QAAKuB,SAAS,EAAC,cAAc;QAAAC,QAAA,gBAC3BxB,OAAA;UAAMuB,SAAS,EAAC,MAAM;UAAAC,QAAA,gBACpBxB,OAAA;YAAMuB,SAAS,EAAC,WAAW;YAAAC,QAAA,EAAC;UAAE;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,EACpCV,SAAS,EAAC,QACb;QAAA;UAAAO,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,eACP5B,OAAA;UAAMuB,SAAS,EAAC,MAAM;UAAAC,QAAA,gBACpBxB,OAAA;YAAMuB,SAAS,EAAC,WAAW;YAAAC,QAAA,EAAC;UAAE;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,EACpCN,SAAS,EAAC,aACb;QAAA;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAEN5B,OAAA;MAAKuB,SAAS,EAAC,gBAAgB;MAAAC,QAAA,GAC5BnB,OAAO,iBACNL,OAAA;QAAKuB,SAAS,EAAC,iBAAiB;QAAAC,QAAA,eAC9BxB,OAAA;UAAKuB,SAAS,EAAC,iBAAiB;UAAAC,QAAA,gBAC9BxB,OAAA;YAAKuB,SAAS,EAAC;UAAiB;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eACvC5B,OAAA;YAAMuB,SAAS,EAAC,cAAc;YAAAC,QAAA,EAAC;UAA2B;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eACjE5B,OAAA;YAAKuB,SAAS,EAAC,gBAAgB;YAAAC,QAAA,gBAC7BxB,OAAA;cAAMuB,SAAS,EAAC,eAAe;cAAAC,QAAA,EAAC;YAAE;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eACzC5B,OAAA;cAAMuB,SAAS,EAAC,eAAe;cAAAC,QAAA,EAAC;YAAC;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eACxC5B,OAAA;cAAMuB,SAAS,EAAC,eAAe;cAAAC,QAAA,EAAC;YAAE;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACtC,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CACN,eAED5B,OAAA;QACEuB,SAAS,EAAC,iBAAiB;QAC3BM,IAAI,EAAC,IAAI;QACTC,WAAW,EAAC,6JAEyD;QACrE5B,KAAK,EAAEA,KAAM;QACbC,QAAQ,EAAE4B,CAAC,IAAI5B,QAAQ,IAAIA,QAAQ,CAAC4B,CAAC,CAACC,MAAM,CAAC9B,KAAK,CAAE;QACpD+B,QAAQ,EAAE5B;MAAQ;QAAAoB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACnB,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC,CAAC,eAEN5B,OAAA;MAAKuB,SAAS,EAAC,gBAAgB;MAAAC,QAAA,gBAC7BxB,OAAA;QACEuB,SAAS,EAAC,qBAAqB;QAC/BW,OAAO,EAAEzB,UAAW;QACpBwB,QAAQ,EAAE,CAAC/B,KAAK,IAAIG,OAAQ;QAC5B8B,KAAK,EAAC,mBAAmB;QAAAX,QAAA,gBAEzBxB,OAAA;UAAMuB,SAAS,EAAC,UAAU;UAAAC,QAAA,EAAEjB,MAAM,GAAG,GAAG,GAAG;QAAI;UAAAkB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CAAC,EACtDrB,MAAM,GAAG,SAAS,GAAG,MAAM;MAAA;QAAAkB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACtB,CAAC,eAET5B,OAAA;QACEuB,SAAS,EAAC,sBAAsB;QAChCW,OAAO,EAAEjB,WAAY;QACrBgB,QAAQ,EAAE,CAAC/B,KAAK,IAAIG,OAAQ;QAC5B8B,KAAK,EAAC,eAAe;QAAAX,QAAA,gBAErBxB,OAAA;UAAMuB,SAAS,EAAC,UAAU;UAAAC,QAAA,EAAC;QAAG;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,SAEvC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC,eAET5B,OAAA;QACEuB,SAAS,EAAC,2BAA2B;QACrCW,OAAO,EAAE9B,UAAW;QACpB6B,QAAQ,EAAE5B,OAAQ;QAClB8B,KAAK,EAAC,sBAAsB;QAAAX,QAAA,gBAE5BxB,OAAA;UAAMuB,SAAS,EAAC,UAAU;UAAAC,QAAA,EAAC;QAAE;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,cAEtC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACN,CAAC,EAEL1B,KAAK,IAAI,CAACG,OAAO,iBAChBL,OAAA;MAAKuB,SAAS,EAAC,eAAe;MAAAC,QAAA,eAC5BxB,OAAA;QAAKuB,SAAS,EAAC,iBAAiB;QAAAC,QAAA,gBAC9BxB,OAAA;UAAMuB,SAAS,EAAC,cAAc;UAAAC,QAAA,EAAC;QAAE;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,sFAE1C;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAK;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CACN;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACE,CAAC;AAEV,CAAC;AAACtB,EAAA,CA9GIL,MAAM;AAAAmC,EAAA,GAANnC,MAAM;AAgHZ,eAAeA,MAAM;AAAC,IAAAmC,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}