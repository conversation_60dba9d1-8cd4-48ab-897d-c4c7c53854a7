{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\vedika\\\\ai-generative\\\\client\\\\src\\\\components\\\\AuthForm.jsx\";\nimport React from 'react';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst AuthForm = () => /*#__PURE__*/_jsxDEV(\"div\", {\n  children: /*#__PURE__*/_jsxDEV(\"p\", {\n    children: \"(Authentication form will appear here.)\"\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 5,\n    columnNumber: 5\n  }, this)\n}, void 0, false, {\n  fileName: _jsxFileName,\n  lineNumber: 4,\n  columnNumber: 3\n}, this);\n_c = AuthForm;\nexport default AuthForm;\nvar _c;\n$RefreshReg$(_c, \"AuthForm\");", "map": {"version": 3, "names": ["React", "jsxDEV", "_jsxDEV", "AuthForm", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/vedika/ai-generative/client/src/components/AuthForm.jsx"], "sourcesContent": ["import React from 'react';\r\n\r\nconst AuthForm = () => (\r\n  <div>\r\n    <p>(Authentication form will appear here.)</p>\r\n  </div>\r\n);\r\n\r\nexport default AuthForm;"], "mappings": ";AAAA,OAAOA,KAAK,MAAM,OAAO;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE1B,MAAMC,QAAQ,GAAGA,CAAA,kBACfD,OAAA;EAAAE,QAAA,eACEF,OAAA;IAAAE,QAAA,EAAG;EAAuC;IAAAC,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAAG;AAAC;EAAAH,QAAA,EAAAC,YAAA;EAAAC,UAAA;EAAAC,YAAA;AAAA,OAC3C,CACN;AAACC,EAAA,GAJIN,QAAQ;AAMd,eAAeA,QAAQ;AAAC,IAAAM,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}