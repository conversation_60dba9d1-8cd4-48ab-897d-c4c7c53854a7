import React, { useState, useEffect } from 'react';
import ContentTypeSelector from './components/ContentTypeSelector';
import ToneSelector from './components/ToneSelector';
import LengthSelector from './components/LengthSelector';
import Editor from './components/Editor';
import History from './components/History';
import AuthForm from './components/AuthForm';
import './App.css';

function App() {
  // State for content generation form
  const [contentType, setContentType] = useState('blog');
  const [tone, setTone] = useState('professional');
  const [length, setLength] = useState(200);
  const [editorValue, setEditorValue] = useState('');
  const [loading, setLoading] = useState(false);
  const [topic, setTopic] = useState('');
  const [activeTab, setActiveTab] = useState('generate');
  const [user, setUser] = useState(null);

  // Check for existing auth token
  useEffect(() => {
    const token = localStorage.getItem('authToken');
    const userData = localStorage.getItem('userData');
    if (token && userData) {
      try {
        const parsedUser = JSON.parse(userData);
        console.log('App: Loading user from localStorage:', parsedUser);
        setUser(parsedUser);
      } catch (error) {
        console.error('Error parsing user data from localStorage:', error);
        // Clear invalid data
        localStorage.removeItem('authToken');
        localStorage.removeItem('userData');
      }
    }
  }, []);

  // Also check when activeTab changes to ensure user state is maintained
  useEffect(() => {
    if (activeTab === 'history' && !user) {
      const token = localStorage.getItem('authToken');
      const userData = localStorage.getItem('userData');
      if (token && userData) {
        try {
          const parsedUser = JSON.parse(userData);
          console.log('App: Restoring user state for history tab:', parsedUser);
          setUser(parsedUser);
        } catch (error) {
          console.error('Error restoring user state:', error);
        }
      }
    }
  }, [activeTab, user]);

  // Get API base URL
  const getApiUrl = () => {
    return process.env.NODE_ENV === 'production'
      ? '/api'
      : 'http://localhost:5000/api';
  };

  // Connect to backend API to generate content
  const handleGenerate = async () => {
    if (!topic.trim()) {
      alert('Please enter a topic');
      return;
    }

    setLoading(true);
    setEditorValue('');

    try {
      const token = localStorage.getItem('authToken');
      const headers = { 'Content-Type': 'application/json' };
      if (token) {
        headers.Authorization = `Bearer ${token}`;
      }

      const response = await fetch(`${getApiUrl()}/generate`, {
        method: 'POST',
        headers,
        body: JSON.stringify({
          contentType: contentType === 'social' ? 'social post' : contentType,
          tone,
          length,
          topic
        })
      });

      const data = await response.json();
      if (response.ok) {
        setEditorValue(data.content || 'No content generated.');
      } else {
        setEditorValue(`Error: ${data.error || 'Failed to generate content'}`);
      }
    } catch (error) {
      setEditorValue('Error: Unable to connect to the server.');
    } finally {
      setLoading(false);
    }
  };

  const handleLogout = () => {
    localStorage.removeItem('authToken');
    localStorage.removeItem('userData');
    setUser(null);
  };

  return (
    <div className="app">
      {/* Floating cartoon elements */}
      <div className="floating-elements">
        <div className="floating-element">🎨</div>
        <div className="floating-element">✨</div>
        <div className="floating-element">📝</div>
        <div className="floating-element">🚀</div>
        <div className="floating-element">💡</div>
        <div className="floating-element">🎯</div>
      </div>

      {/* Header */}
      <header className="header">
        <div className="header-content">
          <div className="logo">
            <div className="logo-icon">✨</div>
            <h1>AI Content Generator</h1>
          </div>
          <nav className="nav">
            <button
              className={`nav-btn ${activeTab === 'generate' ? 'active' : ''}`}
              onClick={() => setActiveTab('generate')}
            >
              <span className="nav-icon">🚀</span>
              Generate
            </button>
            <button
              className={`nav-btn ${activeTab === 'history' ? 'active' : ''}`}
              onClick={() => setActiveTab('history')}
            >
              <span className="nav-icon">📚</span>
              History
            </button>
            <button
              className={`nav-btn ${activeTab === 'auth' ? 'active' : ''}`}
              onClick={() => setActiveTab('auth')}
            >
              <span className="nav-icon">👤</span>
              {user ? user.username : 'Login'}
            </button>
            {user && (
              <button className="logout-btn" onClick={handleLogout}>
                <span className="nav-icon">🚪</span>
                Logout
              </button>
            )}
          </nav>
        </div>
      </header>

      {/* Main Content */}
      <main className="main">
        {activeTab === 'generate' && (
          <div className="tab-content">
            <div className="hero-section">
              <div className="hero-illustration">
                <span className="hero-emoji hero-emoji-1">🤖</span>
                <span className="hero-emoji hero-emoji-2">✍️</span>
                <span className="hero-emoji hero-emoji-3">📱</span>
                <span className="hero-emoji hero-emoji-4">💻</span>
              </div>
              <h2 className="hero-title">
                Create Amazing Content with AI
                <span className="title-decoration">✨</span>
              </h2>
              <p className="hero-subtitle">
                Generate professional blog posts, engaging social media content, and compelling advertisements in seconds
                <br />
                <span className="subtitle-emoji">🎯 Powered by AI • 🚀 Lightning Fast • 🎨 Creative</span>
              </p>
            </div>

            <div className="content-generator">
              <div className="generator-form">
                <div className="form-section">
                  <h3 className="section-title">Content Settings</h3>
                  <div className="form-grid">
                    <ContentTypeSelector value={contentType} onChange={setContentType} />
                    <ToneSelector value={tone} onChange={setTone} />
                    <LengthSelector value={length} onChange={setLength} />
                  </div>
                </div>

                <div className="form-section">
                  <h3 className="section-title">Topic</h3>
                  <div className="topic-input-container">
                    <input
                      type="text"
                      value={topic}
                      onChange={e => setTopic(e.target.value)}
                      placeholder="What would you like to write about?"
                      className="topic-input"
                      onKeyDown={e => e.key === 'Enter' && handleGenerate()}
                    />
                    <button
                      onClick={handleGenerate}
                      disabled={loading || !topic.trim()}
                      className="generate-btn"
                    >
                      {loading ? (
                        <>
                          <span className="spinner"></span>
                          Generating...
                        </>
                      ) : (
                        <>
                          <span className="btn-icon">✨</span>
                          Generate Content
                        </>
                      )}
                    </button>
                  </div>
                </div>
              </div>

              <div className="editor-section">
                <Editor
                  value={editorValue}
                  onChange={setEditorValue}
                  onGenerate={handleGenerate}
                  loading={loading}
                />
              </div>
            </div>
          </div>
        )}

        {activeTab === 'history' && (
          <div className="tab-content">
            <div className="section-header">
              <h2 className="section-title">Content History</h2>
              <p className="section-subtitle">View and manage your previously generated content</p>
            </div>
            <History user={user} onNavigate={setActiveTab} />
          </div>
        )}

        {activeTab === 'auth' && (
          <div className="tab-content">
            <div className="section-header">
              <h2 className="section-title">
                {user ? `Welcome, ${user.username}!` : 'Login or Register'}
              </h2>
              <p className="section-subtitle">
                {user ? 'Manage your account and view your content history' : 'Create an account to save your generated content'}
              </p>
            </div>
            <AuthForm user={user} setUser={setUser} onNavigate={setActiveTab} />
          </div>
        )}
      </main>

      {/* Footer */}
      <footer className="footer">
        <div className="footer-content">
          <p>&copy; 2025 AI Content Generator. Powered by OpenAI.</p>
          <div className="footer-links">
            <a href="#about">About</a>
            <a href="#privacy">Privacy</a>
            <a href="#terms">Terms</a>
          </div>
        </div>
      </footer>
    </div>
  );
}

export default App;
