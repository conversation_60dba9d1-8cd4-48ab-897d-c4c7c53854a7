# DigitalOcean Deployment Guide

## Prerequisites
1. DigitalOcean account
2. <PERSON><PERSON> installed locally
3. Basic knowledge of Linux/Ubuntu

## Option 1: DigitalOcean App Platform (Easiest)

### Step 1: Prepare Repository
```bash
# Ensure your code is in a Git repository
git add .
git commit -m "Prepare for deployment"
git push origin main
```

### Step 2: Deploy via App Platform
1. Go to DigitalOcean App Platform
2. Connect your GitHub repository
3. Configure build settings:
   - **Backend**: 
     - Source: `/ai-generative/backend`
     - Build Command: `npm install`
     - Run Command: `npm start`
   - **Frontend**:
     - Source: `/ai-generative/client`
     - Build Command: `npm run build`
     - Output Directory: `build`

### Step 3: Set Environment Variables
```env
OPENAI_API_KEY=your_openai_key
JWT_SECRET=your_jwt_secret
NODE_ENV=production
```

### Step 4: Configure Domains
- Add custom domain in App Platform settings
- Configure DNS with your domain provider

## Option 2: Docker Droplet (More Control)

### Step 1: Create Droplet
```bash
# Create Ubuntu droplet with Dock<PERSON> pre-installed
# Choose $5/month basic droplet for testing
# $10/month for production
```

### Step 2: Prepare Production Docker Compose
```yaml
# docker-compose.prod.yml
version: '3.8'

services:
  backend:
    build:
      context: ./ai-generative/backend
      dockerfile: Dockerfile
    container_name: ai-content-backend
    ports:
      - "5000:5000"
    environment:
      - NODE_ENV=production
      - PORT=5000
      - OPENAI_API_KEY=${OPENAI_API_KEY}
      - JWT_SECRET=${JWT_SECRET}
    restart: unless-stopped
    networks:
      - ai-content-network

  frontend:
    build:
      context: ./ai-generative/client
      dockerfile: Dockerfile
    container_name: ai-content-frontend
    ports:
      - "80:80"
      - "443:443"
    depends_on:
      - backend
    restart: unless-stopped
    networks:
      - ai-content-network
    volumes:
      - ./ssl:/etc/nginx/ssl

  nginx:
    image: nginx:alpine
    container_name: ai-content-nginx
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx.conf:/etc/nginx/nginx.conf
      - ./ssl:/etc/nginx/ssl
    depends_on:
      - backend
      - frontend
    restart: unless-stopped
    networks:
      - ai-content-network

networks:
  ai-content-network:
    driver: bridge
```

### Step 3: Create Nginx Configuration
```nginx
# nginx.conf
events {
    worker_connections 1024;
}

http {
    upstream backend {
        server backend:5000;
    }

    upstream frontend {
        server frontend:80;
    }

    # Redirect HTTP to HTTPS
    server {
        listen 80;
        server_name yourdomain.com www.yourdomain.com;
        return 301 https://$server_name$request_uri;
    }

    # HTTPS server
    server {
        listen 443 ssl http2;
        server_name yourdomain.com www.yourdomain.com;

        ssl_certificate /etc/nginx/ssl/cert.pem;
        ssl_certificate_key /etc/nginx/ssl/key.pem;

        # Frontend
        location / {
            proxy_pass http://frontend;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
        }

        # Backend API
        location /api/ {
            proxy_pass http://backend;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
        }
    }
}
```

### Step 4: Deploy to Droplet
```bash
# SSH into your droplet
ssh root@your_droplet_ip

# Clone your repository
git clone https://github.com/yourusername/your-repo.git
cd your-repo

# Create environment file
nano .env
# Add your environment variables

# Install Docker Compose if not installed
curl -L "https://github.com/docker/compose/releases/download/v2.20.0/docker-compose-$(uname -s)-$(uname -m)" -o /usr/local/bin/docker-compose
chmod +x /usr/local/bin/docker-compose

# Deploy
docker-compose -f docker-compose.prod.yml up -d

# Check status
docker-compose ps
docker-compose logs
```

### Step 5: SSL Certificate (Let's Encrypt)
```bash
# Install Certbot
apt update
apt install certbot

# Get SSL certificate
certbot certonly --standalone -d yourdomain.com -d www.yourdomain.com

# Copy certificates
mkdir ssl
cp /etc/letsencrypt/live/yourdomain.com/fullchain.pem ssl/cert.pem
cp /etc/letsencrypt/live/yourdomain.com/privkey.pem ssl/key.pem

# Restart containers
docker-compose -f docker-compose.prod.yml restart
```

### Step 6: Automatic Updates
```bash
# Create update script
cat > update.sh << 'EOF'
#!/bin/bash
cd /root/your-repo
git pull origin main
docker-compose -f docker-compose.prod.yml down
docker-compose -f docker-compose.prod.yml build --no-cache
docker-compose -f docker-compose.prod.yml up -d
EOF

chmod +x update.sh

# Set up cron job for automatic updates (optional)
crontab -e
# Add: 0 2 * * 0 /root/your-repo/update.sh
```

## Monitoring and Maintenance

### Step 1: Set up Monitoring
```bash
# Install monitoring tools
docker run -d \
  --name=grafana \
  -p 3001:3000 \
  grafana/grafana

# Monitor logs
docker-compose logs -f
```

### Step 2: Backup Strategy
```bash
# Create backup script
cat > backup.sh << 'EOF'
#!/bin/bash
DATE=$(date +%Y%m%d_%H%M%S)
tar -czf backup_$DATE.tar.gz .env docker-compose.prod.yml ssl/
# Upload to DigitalOcean Spaces or AWS S3
EOF
```

## Cost Optimization
- Use $5/month droplet for development
- Use $10/month droplet for production
- Enable monitoring to track resource usage
- Set up alerts for high CPU/memory usage
- Use DigitalOcean Spaces for file storage
