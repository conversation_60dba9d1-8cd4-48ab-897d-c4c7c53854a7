import { useState } from 'react';
import './Editor.css';

const Editor = ({ value, onChange, onGenerate, loading }) => {
  const [copied, setCopied] = useState(false);

  const handleCopy = async () => {
    if (value) {
      try {
        await navigator.clipboard.writeText(value);
        setCopied(true);
        setTimeout(() => setCopied(false), 2000);
      } catch (err) {
        console.error('Failed to copy text: ', err);
      }
    }
  };

  const handleClear = () => {
    onChange && onChange('');
  };

  const wordCount = value ? value.trim().split(/\s+/).length : 0;
  const charCount = value ? value.length : 0;

  return (
    <div className="editor-container">
      <div className="editor-header">
        <h3 className="editor-title">
          <span className="editor-icon">✍️</span>
          Generated Content
        </h3>
        <div className="editor-stats">
          <span className="stat">
            <span className="stat-icon">📝</span>
            {wordCount} words
          </span>
          <span className="stat">
            <span className="stat-icon">🔤</span>
            {charCount} characters
          </span>
        </div>
      </div>

      <div className="editor-wrapper">
        {loading && (
          <div className="loading-overlay">
            <div className="loading-content">
              <div className="loading-spinner"></div>
              <span className="loading-text">Creating amazing content...</span>
              <div className="loading-emojis">
                <span className="loading-emoji">🤖</span>
                <span className="loading-emoji">✨</span>
                <span className="loading-emoji">📝</span>
              </div>
            </div>
          </div>
        )}

        <textarea
          className="editor-textarea"
          rows="15"
          placeholder="🎯 Your AI-generated content will appear here like magic! ✨

Click 'Generate Content' to start creating amazing content with AI assistance."
          value={value}
          onChange={e => onChange && onChange(e.target.value)}
          disabled={loading}
        />
      </div>

      <div className="editor-actions">
        <button
          className="action-btn copy-btn"
          onClick={handleCopy}
          disabled={!value || loading}
          title="Copy to clipboard"
        >
          <span className="btn-icon">{copied ? '✅' : '📋'}</span>
          {copied ? 'Copied!' : 'Copy'}
        </button>

        <button
          className="action-btn clear-btn"
          onClick={handleClear}
          disabled={!value || loading}
          title="Clear content"
        >
          <span className="btn-icon">🗑️</span>
          Clear
        </button>

        <button
          className="action-btn regenerate-btn"
          onClick={onGenerate}
          disabled={loading}
          title="Generate new content"
        >
          <span className="btn-icon">🔄</span>
          Regenerate
        </button>
      </div>

      {value && !loading && (
        <div className="editor-footer">
          <div className="success-message">
            <span className="success-icon">🎉</span>
            Content generated successfully! You can edit it above or copy it to use elsewhere.
          </div>
        </div>
      )}
    </div>
  );
};

export default Editor;