import { useState } from 'react';
import './LengthSelector.css';

const LengthSelector = ({ value, onChange }) => {
  const [customLength, setCustomLength] = useState(value);

  const presetLengths = [
    { value: 100, label: 'Short', icon: '📝', description: '~100 words' },
    { value: 300, label: 'Medium', icon: '📄', description: '~300 words' },
    { value: 500, label: 'Long', icon: '📚', description: '~500 words' }
  ];

  const handlePresetClick = (length) => {
    setCustomLength(length);
    onChange && onChange(length);
  };

  const handleCustomChange = (e) => {
    const newValue = parseInt(e.target.value) || 100;
    setCustomLength(newValue);
    onChange && onChange(newValue);
  };

  return (
    <div className="length-selector">
      <label className="selector-label">
        <span className="label-icon">📏</span>
        Content Length
      </label>

      <div className="length-presets">
        {presetLengths.map((preset) => (
          <button
            key={preset.value}
            className={`length-preset ${value === preset.value ? 'active' : ''}`}
            onClick={() => handlePresetClick(preset.value)}
          >
            <div className="preset-icon">{preset.icon}</div>
            <div className="preset-content">
              <h4 className="preset-title">{preset.label}</h4>
              <p className="preset-description">{preset.description}</p>
            </div>
          </button>
        ))}
      </div>

      <div className="custom-length">
        <label className="custom-label">
          <span className="custom-icon">🎯</span>
          Custom Length
        </label>
        <div className="custom-input-container">
          <input
            type="range"
            min="50"
            max="2000"
            step="50"
            value={customLength}
            onChange={handleCustomChange}
            className="length-slider"
          />
          <div className="length-display">
            <input
              type="number"
              min="50"
              max="2000"
              value={customLength}
              onChange={handleCustomChange}
              className="length-input"
            />
            <span className="length-unit">words</span>
          </div>
        </div>
      </div>
    </div>
  );
};

export default LengthSelector;