{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\vedika\\\\ai-generative\\\\client\\\\src\\\\App.js\",\n  _s = $RefreshSig$();\nimport React, { useState } from 'react';\nimport BlogGenerator from './components/BlogGenerator';\nimport ContentTypeSelector from './components/ContentTypeSelector';\nimport ToneSelector from './components/ToneSelector';\nimport LengthSelector from './components/LengthSelector';\nimport Editor from './components/Editor';\nimport History from './components/History';\nimport AuthForm from './components/AuthForm';\nimport logo from './logo.svg';\nimport './App.css';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nfunction App() {\n  _s();\n  // State for content generation form\n  const [contentType, setContentType] = useState('blog');\n  const [tone, setTone] = useState('formal');\n  const [length, setLength] = useState('short');\n  const [editorValue, setEditorValue] = useState('');\n  const [loading, setLoading] = useState(false);\n  const [topic, setTopic] = useState('');\n\n  // Connect to backend API to generate content\n  const handleGenerate = async () => {\n    setLoading(true);\n    setEditorValue('');\n    try {\n      const response = await fetch('http://localhost:5000/api/generate', {\n        method: 'POST',\n        headers: {\n          'Content-Type': 'application/json'\n        },\n        body: JSON.stringify({\n          contentType,\n          tone,\n          length,\n          topic\n        })\n      });\n      const data = await response.json();\n      setEditorValue(data.content || 'No content generated.');\n    } catch (error) {\n      setEditorValue('Error generating content.');\n    } finally {\n      setLoading(false);\n    }\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"App\",\n    children: [/*#__PURE__*/_jsxDEV(\"header\", {\n      children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n        children: \"AI Content Generator\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 43,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"nav\", {\n        children: [/*#__PURE__*/_jsxDEV(\"a\", {\n          href: \"#generate\",\n          children: \"Generate\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 45,\n          columnNumber: 11\n        }, this), \" | \", /*#__PURE__*/_jsxDEV(\"a\", {\n          href: \"#history\",\n          children: \"History\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 45,\n          columnNumber: 46\n        }, this), \" | \", /*#__PURE__*/_jsxDEV(\"a\", {\n          href: \"#auth\",\n          children: \"Login/Register\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 45,\n          columnNumber: 79\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 44,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 42,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"main\", {\n      children: [/*#__PURE__*/_jsxDEV(\"section\", {\n        id: \"generate\",\n        children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n          children: \"Generate Content\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 50,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(ContentTypeSelector, {\n          value: contentType,\n          onChange: setContentType\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 52,\n          columnNumber: 7\n        }, this), /*#__PURE__*/_jsxDEV(ToneSelector, {\n          value: tone,\n          onChange: setTone\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 53,\n          columnNumber: 7\n        }, this), /*#__PURE__*/_jsxDEV(LengthSelector, {\n          value: length,\n          onChange: setLength\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 54,\n          columnNumber: 7\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          children: [/*#__PURE__*/_jsxDEV(\"label\", {\n            children: \"Topic: \"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 56,\n            columnNumber: 9\n          }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n            type: \"text\",\n            value: topic,\n            onChange: e => setTopic(e.target.value),\n            placeholder: \"Enter topic...\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 57,\n            columnNumber: 9\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 55,\n          columnNumber: 7\n        }, this), loading ? /*#__PURE__*/_jsxDEV(\"p\", {\n          children: \"Generating content...\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 65,\n          columnNumber: 9\n        }, this) : /*#__PURE__*/_jsxDEV(Editor, {\n          value: editorValue,\n          onChange: setEditorValue,\n          onGenerate: handleGenerate\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 67,\n          columnNumber: 9\n        }, this), /*#__PURE__*/_jsxDEV(\"hr\", {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 69,\n          columnNumber: 7\n        }, this), /*#__PURE__*/_jsxDEV(BlogGenerator, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 70,\n          columnNumber: 7\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 49,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"section\", {\n        id: \"history\",\n        children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n          children: \"History\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 73,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(History, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 74,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 72,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"section\", {\n        id: \"auth\",\n        children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n          children: \"Login / Register\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 77,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(AuthForm, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 78,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 76,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 48,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 41,\n    columnNumber: 5\n  }, this);\n}\n_s(App, \"DlmBFUVBRXRu5/V/LP58IjobHBU=\");\n_c = App;\nexport default App;\nvar _c;\n$RefreshReg$(_c, \"App\");", "map": {"version": 3, "names": ["React", "useState", "BlogGenerator", "ContentTypeSelector", "ToneSelector", "LengthSelector", "Editor", "History", "AuthForm", "logo", "jsxDEV", "_jsxDEV", "App", "_s", "contentType", "setContentType", "tone", "setTone", "length", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "setEditorValue", "loading", "setLoading", "topic", "setTopic", "handleGenerate", "response", "fetch", "method", "headers", "body", "JSON", "stringify", "data", "json", "content", "error", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "href", "id", "value", "onChange", "type", "e", "target", "placeholder", "onGenerate", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/vedika/ai-generative/client/src/App.js"], "sourcesContent": ["import React, { useState } from 'react';\nimport BlogGenerator from './components/BlogGenerator';\nimport ContentTypeSelector from './components/ContentTypeSelector';\nimport ToneSelector from './components/ToneSelector';\nimport LengthSelector from './components/LengthSelector';\nimport Editor from './components/Editor';\nimport History from './components/History';\nimport AuthForm from './components/AuthForm';\nimport logo from './logo.svg';\nimport './App.css';\n\nfunction App() {\n  // State for content generation form\n  const [contentType, setContentType] = useState('blog');\n  const [tone, setTone] = useState('formal');\n  const [length, setLength] = useState('short');\n  const [editorValue, setEditorValue] = useState('');\n  const [loading, setLoading] = useState(false);\n  const [topic, setTopic] = useState('');\n\n  // Connect to backend API to generate content\n  const handleGenerate = async () => {\n    setLoading(true);\n    setEditorValue('');\n    try {\n      const response = await fetch('http://localhost:5000/api/generate', {\n        method: 'POST',\n        headers: { 'Content-Type': 'application/json' },\n        body: JSON.stringify({ contentType, tone, length, topic })\n      });\n      const data = await response.json();\n      setEditorValue(data.content || 'No content generated.');\n    } catch (error) {\n      setEditorValue('Error generating content.');\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  return (\n    <div className=\"App\">\n      <header>\n        <h1>AI Content Generator</h1>\n        <nav>\n          <a href=\"#generate\">Generate</a> | <a href=\"#history\">History</a> | <a href=\"#auth\">Login/Register</a>\n        </nav>\n      </header>\n      <main>\n        <section id=\"generate\">\n          <h2>Generate Content</h2>\n      {/* Existing content generator UI ... */}\n      <ContentTypeSelector value={contentType} onChange={setContentType} />\n      <ToneSelector value={tone} onChange={setTone} />\n      <LengthSelector value={length} onChange={setLength} />\n      <div>\n        <label>Topic: </label>\n        <input\n          type=\"text\"\n          value={topic}\n          onChange={e => setTopic(e.target.value)}\n          placeholder=\"Enter topic...\"\n        />\n      </div>\n      {loading ? (\n        <p>Generating content...</p>\n      ) : (\n        <Editor value={editorValue} onChange={setEditorValue} onGenerate={handleGenerate} />\n      )}\n      <hr />\n      <BlogGenerator />\n        </section>\n        <section id=\"history\">\n          <h2>History</h2>\n          <History />\n        </section>\n        <section id=\"auth\">\n          <h2>Login / Register</h2>\n          <AuthForm />\n        </section>\n      </main>\n    </div>\n  );\n}\n\nexport default App;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,QAAQ,OAAO;AACvC,OAAOC,aAAa,MAAM,4BAA4B;AACtD,OAAOC,mBAAmB,MAAM,kCAAkC;AAClE,OAAOC,YAAY,MAAM,2BAA2B;AACpD,OAAOC,cAAc,MAAM,6BAA6B;AACxD,OAAOC,MAAM,MAAM,qBAAqB;AACxC,OAAOC,OAAO,MAAM,sBAAsB;AAC1C,OAAOC,QAAQ,MAAM,uBAAuB;AAC5C,OAAOC,IAAI,MAAM,YAAY;AAC7B,OAAO,WAAW;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEnB,SAASC,GAAGA,CAAA,EAAG;EAAAC,EAAA;EACb;EACA,MAAM,CAACC,WAAW,EAAEC,cAAc,CAAC,GAAGd,QAAQ,CAAC,MAAM,CAAC;EACtD,MAAM,CAACe,IAAI,EAAEC,OAAO,CAAC,GAAGhB,QAAQ,CAAC,QAAQ,CAAC;EAC1C,MAAM,CAACiB,MAAM,EAAEC,SAAS,CAAC,GAAGlB,QAAQ,CAAC,OAAO,CAAC;EAC7C,MAAM,CAACmB,WAAW,EAAEC,cAAc,CAAC,GAAGpB,QAAQ,CAAC,EAAE,CAAC;EAClD,MAAM,CAACqB,OAAO,EAAEC,UAAU,CAAC,GAAGtB,QAAQ,CAAC,KAAK,CAAC;EAC7C,MAAM,CAACuB,KAAK,EAAEC,QAAQ,CAAC,GAAGxB,QAAQ,CAAC,EAAE,CAAC;;EAEtC;EACA,MAAMyB,cAAc,GAAG,MAAAA,CAAA,KAAY;IACjCH,UAAU,CAAC,IAAI,CAAC;IAChBF,cAAc,CAAC,EAAE,CAAC;IAClB,IAAI;MACF,MAAMM,QAAQ,GAAG,MAAMC,KAAK,CAAC,oCAAoC,EAAE;QACjEC,MAAM,EAAE,MAAM;QACdC,OAAO,EAAE;UAAE,cAAc,EAAE;QAAmB,CAAC;QAC/CC,IAAI,EAAEC,IAAI,CAACC,SAAS,CAAC;UAAEnB,WAAW;UAAEE,IAAI;UAAEE,MAAM;UAAEM;QAAM,CAAC;MAC3D,CAAC,CAAC;MACF,MAAMU,IAAI,GAAG,MAAMP,QAAQ,CAACQ,IAAI,CAAC,CAAC;MAClCd,cAAc,CAACa,IAAI,CAACE,OAAO,IAAI,uBAAuB,CAAC;IACzD,CAAC,CAAC,OAAOC,KAAK,EAAE;MACdhB,cAAc,CAAC,2BAA2B,CAAC;IAC7C,CAAC,SAAS;MACRE,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;EAED,oBACEZ,OAAA;IAAK2B,SAAS,EAAC,KAAK;IAAAC,QAAA,gBAClB5B,OAAA;MAAA4B,QAAA,gBACE5B,OAAA;QAAA4B,QAAA,EAAI;MAAoB;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eAC7BhC,OAAA;QAAA4B,QAAA,gBACE5B,OAAA;UAAGiC,IAAI,EAAC,WAAW;UAAAL,QAAA,EAAC;QAAQ;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC,OAAG,eAAAhC,OAAA;UAAGiC,IAAI,EAAC,UAAU;UAAAL,QAAA,EAAC;QAAO;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC,OAAG,eAAAhC,OAAA;UAAGiC,IAAI,EAAC,OAAO;UAAAL,QAAA,EAAC;QAAc;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACnG,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACA,CAAC,eACThC,OAAA;MAAA4B,QAAA,gBACE5B,OAAA;QAASkC,EAAE,EAAC,UAAU;QAAAN,QAAA,gBACpB5B,OAAA;UAAA4B,QAAA,EAAI;QAAgB;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eAE7BhC,OAAA,CAACR,mBAAmB;UAAC2C,KAAK,EAAEhC,WAAY;UAACiC,QAAQ,EAAEhC;QAAe;UAAAyB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eACrEhC,OAAA,CAACP,YAAY;UAAC0C,KAAK,EAAE9B,IAAK;UAAC+B,QAAQ,EAAE9B;QAAQ;UAAAuB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eAChDhC,OAAA,CAACN,cAAc;UAACyC,KAAK,EAAE5B,MAAO;UAAC6B,QAAQ,EAAE5B;QAAU;UAAAqB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eACtDhC,OAAA;UAAA4B,QAAA,gBACE5B,OAAA;YAAA4B,QAAA,EAAO;UAAO;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC,eACtBhC,OAAA;YACEqC,IAAI,EAAC,MAAM;YACXF,KAAK,EAAEtB,KAAM;YACbuB,QAAQ,EAAEE,CAAC,IAAIxB,QAAQ,CAACwB,CAAC,CAACC,MAAM,CAACJ,KAAK,CAAE;YACxCK,WAAW,EAAC;UAAgB;YAAAX,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC7B,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC,EACLrB,OAAO,gBACNX,OAAA;UAAA4B,QAAA,EAAG;QAAqB;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC,gBAE5BhC,OAAA,CAACL,MAAM;UAACwC,KAAK,EAAE1B,WAAY;UAAC2B,QAAQ,EAAE1B,cAAe;UAAC+B,UAAU,EAAE1B;QAAe;UAAAc,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CACpF,eACDhC,OAAA;UAAA6B,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC,eACNhC,OAAA,CAACT,aAAa;UAAAsC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC,eACVhC,OAAA;QAASkC,EAAE,EAAC,SAAS;QAAAN,QAAA,gBACnB5B,OAAA;UAAA4B,QAAA,EAAI;QAAO;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eAChBhC,OAAA,CAACJ,OAAO;UAAAiC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC,eACVhC,OAAA;QAASkC,EAAE,EAAC,MAAM;QAAAN,QAAA,gBAChB5B,OAAA;UAAA4B,QAAA,EAAI;QAAgB;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACzBhC,OAAA,CAACH,QAAQ;UAAAgC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACL,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACN,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACJ,CAAC;AAEV;AAAC9B,EAAA,CAvEQD,GAAG;AAAAyC,EAAA,GAAHzC,GAAG;AAyEZ,eAAeA,GAAG;AAAC,IAAAyC,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}