const request = require('supertest');
const express = require('express');
const cors = require('cors');

// Mock OpenAI to avoid API calls during testing
jest.mock('openai', () => {
  return jest.fn().mockImplementation(() => ({
    chat: {
      completions: {
        create: jest.fn().mockRejectedValue({
          code: 'insufficient_quota',
          message: 'Mock quota exceeded for testing'
        })
      }
    }
  }));
});

// Import the app after mocking
const app = require('../index.js');

describe('Content Generation API', () => {
  describe('Authentication', () => {
    describe('POST /api/auth/register', () => {
      it('should register a new user successfully', async () => {
        const userData = {
          username: 'testuser',
          email: '<EMAIL>',
          password: 'password123'
        };

        const response = await request(app)
          .post('/api/auth/register')
          .send(userData)
          .expect(201);

        expect(response.body).toHaveProperty('message', 'User registered successfully');
        expect(response.body).toHaveProperty('token');
        expect(response.body).toHaveProperty('user');
        expect(response.body.user).toHaveProperty('username', 'testuser');
        expect(response.body.user).toHaveProperty('email', '<EMAIL>');
        expect(response.body.user).not.toHaveProperty('password');
      });

      it('should return 400 for missing required fields', async () => {
        const response = await request(app)
          .post('/api/auth/register')
          .send({ username: 'testuser' })
          .expect(400);

        expect(response.body).toHaveProperty('error');
        expect(response.body.error).toContain('required');
      });

      it('should return 400 for duplicate user', async () => {
        const userData = {
          username: 'duplicate',
          email: '<EMAIL>',
          password: 'password123'
        };

        // Register first user
        await request(app)
          .post('/api/auth/register')
          .send(userData)
          .expect(201);

        // Try to register same user again
        const response = await request(app)
          .post('/api/auth/register')
          .send(userData)
          .expect(400);

        expect(response.body.error).toContain('already exists');
      });
    });

    describe('POST /api/auth/login', () => {
      beforeEach(async () => {
        // Register a test user
        await request(app)
          .post('/api/auth/register')
          .send({
            username: 'logintest',
            email: '<EMAIL>',
            password: 'password123'
          });
      });

      it('should login successfully with valid credentials', async () => {
        const response = await request(app)
          .post('/api/auth/login')
          .send({
            email: '<EMAIL>',
            password: 'password123'
          })
          .expect(200);

        expect(response.body).toHaveProperty('message', 'Login successful');
        expect(response.body).toHaveProperty('token');
        expect(response.body).toHaveProperty('user');
        expect(response.body.user).toHaveProperty('email', '<EMAIL>');
      });

      it('should return 401 for invalid credentials', async () => {
        const response = await request(app)
          .post('/api/auth/login')
          .send({
            email: '<EMAIL>',
            password: 'wrongpassword'
          })
          .expect(401);

        expect(response.body.error).toBe('Invalid credentials');
      });

      it('should return 400 for missing fields', async () => {
        const response = await request(app)
          .post('/api/auth/login')
          .send({ email: '<EMAIL>' })
          .expect(400);

        expect(response.body.error).toContain('required');
      });
    });

    describe('GET /api/auth/profile', () => {
      let authToken;

      beforeEach(async () => {
        // Register and login to get token
        await request(app)
          .post('/api/auth/register')
          .send({
            username: 'profiletest',
            email: '<EMAIL>',
            password: 'password123'
          });

        const loginResponse = await request(app)
          .post('/api/auth/login')
          .send({
            email: '<EMAIL>',
            password: 'password123'
          });

        authToken = loginResponse.body.token;
      });

      it('should return user profile with valid token', async () => {
        const response = await request(app)
          .get('/api/auth/profile')
          .set('Authorization', `Bearer ${authToken}`)
          .expect(200);

        expect(response.body).toHaveProperty('user');
        expect(response.body.user).toHaveProperty('email', '<EMAIL>');
      });

      it('should return 401 without token', async () => {
        const response = await request(app)
          .get('/api/auth/profile')
          .expect(401);

        expect(response.body.error).toBe('Access token required');
      });

      it('should return 403 with invalid token', async () => {
        const response = await request(app)
          .get('/api/auth/profile')
          .set('Authorization', 'Bearer invalidtoken')
          .expect(403);

        expect(response.body.error).toBe('Invalid or expired token');
      });
    });
  });

  describe('GET /', () => {
    it('should return API status message', async () => {
      const response = await request(app)
        .get('/')
        .expect(200);

      expect(response.body).toEqual({
        message: 'AI Content Generator API is running!'
      });
    });
  });

  describe('POST /api/generate', () => {
    it('should generate blog content with valid parameters', async () => {
      const requestBody = {
        contentType: 'blog',
        tone: 'professional',
        length: 300,
        topic: 'artificial intelligence'
      };

      const response = await request(app)
        .post('/api/generate')
        .send(requestBody)
        .expect(200);

      expect(response.body).toHaveProperty('content');
      expect(response.body).toHaveProperty('grammar');
      expect(response.body.content).toContain('artificial intelligence');
      expect(response.body.content).toContain('Professional Analysis');
      expect(response.body.grammar).toHaveProperty('suggestions');
      expect(response.body.grammar).toHaveProperty('readability');
    });

    it('should generate social post content with casual tone', async () => {
      const requestBody = {
        contentType: 'social post',
        tone: 'casual',
        length: 100,
        topic: 'sustainable living'
      };

      const response = await request(app)
        .post('/api/generate')
        .send(requestBody)
        .expect(200);

      expect(response.body.content).toContain('sustainable living');
      expect(response.body.content).toContain('🤯');
      expect(response.body.content).toContain('#sustainableliving');
    });

    it('should generate ad content with friendly tone', async () => {
      const requestBody = {
        contentType: 'ad',
        tone: 'friendly',
        length: 150,
        topic: 'online learning'
      };

      const response = await request(app)
        .post('/api/generate')
        .send(requestBody)
        .expect(200);

      expect(response.body.content).toContain('online learning');
      expect(response.body.content).toContain('🌟');
      expect(response.body.content).toContain('Join');
    });

    it('should return 400 for missing contentType', async () => {
      const requestBody = {
        tone: 'professional',
        length: 300,
        topic: 'test topic'
      };

      const response = await request(app)
        .post('/api/generate')
        .send(requestBody)
        .expect(400);

      expect(response.body).toHaveProperty('error');
      expect(response.body.error).toContain('Missing required fields');
    });

    it('should return 400 for missing tone', async () => {
      const requestBody = {
        contentType: 'blog',
        length: 300,
        topic: 'test topic'
      };

      const response = await request(app)
        .post('/api/generate')
        .send(requestBody)
        .expect(400);

      expect(response.body.error).toContain('Missing required fields');
    });

    it('should return 400 for missing length', async () => {
      const requestBody = {
        contentType: 'blog',
        tone: 'professional',
        topic: 'test topic'
      };

      const response = await request(app)
        .post('/api/generate')
        .send(requestBody)
        .expect(400);

      expect(response.body.error).toContain('Missing required fields');
    });

    it('should return 400 for missing topic', async () => {
      const requestBody = {
        contentType: 'blog',
        tone: 'professional',
        length: 300
      };

      const response = await request(app)
        .post('/api/generate')
        .send(requestBody)
        .expect(400);

      expect(response.body.error).toContain('Missing required fields');
    });

    it('should handle different content types correctly', async () => {
      const contentTypes = ['blog', 'social post', 'ad'];
      
      for (const contentType of contentTypes) {
        const requestBody = {
          contentType,
          tone: 'professional',
          length: 200,
          topic: 'technology'
        };

        const response = await request(app)
          .post('/api/generate')
          .send(requestBody)
          .expect(200);

        expect(response.body.content).toContain('technology');
        expect(response.body).toHaveProperty('grammar');
      }
    });

    it('should handle different tones correctly', async () => {
      const tones = ['professional', 'casual', 'friendly'];
      
      for (const tone of tones) {
        const requestBody = {
          contentType: 'blog',
          tone,
          length: 200,
          topic: 'innovation'
        };

        const response = await request(app)
          .post('/api/generate')
          .send(requestBody)
          .expect(200);

        expect(response.body.content).toContain('innovation');
        expect(response.body.content.length).toBeGreaterThan(100);
      }
    });
  });

  describe('Content History', () => {
    let authToken;
    let userId;

    beforeEach(async () => {
      // Register and login to get token with unique email
      const uniqueId = Date.now();
      const registerResponse = await request(app)
        .post('/api/auth/register')
        .send({
          username: `historytest${uniqueId}`,
          email: `history${uniqueId}@example.com`,
          password: 'password123'
        });

      authToken = registerResponse.body.token;
      userId = registerResponse.body.user.id;

      // Generate some content to create history
      await request(app)
        .post('/api/generate')
        .set('Authorization', `Bearer ${authToken}`)
        .send({
          contentType: 'blog',
          tone: 'professional',
          length: 200,
          topic: 'test topic'
        });
    });

    describe('GET /api/history', () => {
      it('should return user content history', async () => {
        const response = await request(app)
          .get('/api/history')
          .set('Authorization', `Bearer ${authToken}`)
          .expect(200);

        expect(response.body).toHaveProperty('history');
        expect(response.body).toHaveProperty('total');
        expect(response.body.history).toBeInstanceOf(Array);
        expect(response.body.total).toBeGreaterThan(0);
        expect(response.body.history[0]).toHaveProperty('contentType', 'blog');
        expect(response.body.history[0]).toHaveProperty('topic', 'test topic');
      });

      it('should require authentication', async () => {
        const response = await request(app)
          .get('/api/history')
          .expect(401);

        expect(response.body.error).toBe('Access token required');
      });
    });

    describe('GET /api/history/:id', () => {
      it('should return specific content entry', async () => {
        // Get history to find an entry ID
        const historyResponse = await request(app)
          .get('/api/history')
          .set('Authorization', `Bearer ${authToken}`);

        const entryId = historyResponse.body.history[0].id;

        const response = await request(app)
          .get(`/api/history/${entryId}`)
          .set('Authorization', `Bearer ${authToken}`)
          .expect(200);

        expect(response.body).toHaveProperty('id', entryId);
        expect(response.body).toHaveProperty('content');
        expect(response.body).toHaveProperty('userId', userId);
      });

      it('should return 404 for non-existent entry', async () => {
        const response = await request(app)
          .get('/api/history/99999')
          .set('Authorization', `Bearer ${authToken}`)
          .expect(404);

        expect(response.body.error).toBe('Content not found');
      });
    });

    describe('DELETE /api/history/:id', () => {
      it('should delete specific content entry', async () => {
        // Get history to find an entry ID
        const historyResponse = await request(app)
          .get('/api/history')
          .set('Authorization', `Bearer ${authToken}`);

        const entryId = historyResponse.body.history[0].id;

        const response = await request(app)
          .delete(`/api/history/${entryId}`)
          .set('Authorization', `Bearer ${authToken}`)
          .expect(200);

        expect(response.body.message).toBe('Content deleted successfully');

        // Verify it's deleted
        await request(app)
          .get(`/api/history/${entryId}`)
          .set('Authorization', `Bearer ${authToken}`)
          .expect(404);
      });
    });

    describe('DELETE /api/history', () => {
      it('should clear all user content history', async () => {
        const response = await request(app)
          .delete('/api/history')
          .set('Authorization', `Bearer ${authToken}`)
          .expect(200);

        expect(response.body).toHaveProperty('deletedCount');
        expect(response.body.deletedCount).toBeGreaterThan(0);

        // Verify history is empty
        const historyResponse = await request(app)
          .get('/api/history')
          .set('Authorization', `Bearer ${authToken}`);

        expect(historyResponse.body.total).toBe(0);
      });
    });
  });
});
