import { useState } from 'react';
import './AuthForm.css';

const AuthForm = ({ user, setUser }) => {
  const [isLogin, setIsLogin] = useState(true);
  const [formData, setFormData] = useState({
    username: '',
    email: '',
    password: '',
    confirmPassword: ''
  });
  const [loading, setLoading] = useState(false);
  const [message, setMessage] = useState('');
  const [messageType, setMessageType] = useState(''); // 'success' or 'error'

  // Get API base URL
  const getApiUrl = () => {
    return process.env.NODE_ENV === 'production'
      ? '/api'
      : 'http://localhost:5000/api';
  };

  const handleInputChange = (e) => {
    setFormData({
      ...formData,
      [e.target.name]: e.target.value
    });
  };

  const showMessage = (text, type) => {
    setMessage(text);
    setMessageType(type);
    setTimeout(() => {
      setMessage('');
      setMessageType('');
    }, 5000);
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    setLoading(true);

    try {
      if (!isLogin) {
        // Registration validation
        if (formData.password !== formData.confirmPassword) {
          showMessage('Passwords do not match!', 'error');
          setLoading(false);
          return;
        }
        if (formData.password.length < 6) {
          showMessage('Password must be at least 6 characters long!', 'error');
          setLoading(false);
          return;
        }
      }

      const endpoint = isLogin ? '/auth/login' : '/auth/register';
      const payload = isLogin
        ? { email: formData.email, password: formData.password }
        : {
            username: formData.username,
            email: formData.email,
            password: formData.password
          };

      const response = await fetch(`${getApiUrl()}${endpoint}`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(payload),
      });

      const data = await response.json();

      if (response.ok) {
        // Success
        localStorage.setItem('authToken', data.token);
        localStorage.setItem('userData', JSON.stringify(data.user));
        setUser(data.user);
        showMessage(
          isLogin
            ? `Welcome back, ${data.user.username}! 🎉`
            : `Account created successfully! Welcome, ${data.user.username}! 🎉`,
          'success'
        );

        // Clear form
        setFormData({
          username: '',
          email: '',
          password: '',
          confirmPassword: ''
        });
      } else {
        // Error
        showMessage(data.error || 'Something went wrong!', 'error');
      }
    } catch (error) {
      showMessage('Unable to connect to the server. Please try again later.', 'error');
    } finally {
      setLoading(false);
    }
  };

  const toggleMode = () => {
    setIsLogin(!isLogin);
    setMessage('');
    setFormData({
      username: '',
      email: '',
      password: '',
      confirmPassword: ''
    });
  };

  if (user) {
    return (
      <div className="auth-container">
        <div className="user-profile">
          <div className="profile-header">
            <div className="profile-avatar">
              <span className="avatar-emoji">👤</span>
            </div>
            <div className="profile-info">
              <h3 className="profile-name">
                <span className="welcome-emoji">👋</span>
                Welcome, {user.username}!
              </h3>
              <p className="profile-email">{user.email}</p>
            </div>
          </div>

          <div className="profile-stats">
            <div className="stat-card">
              <span className="stat-icon">📝</span>
              <div className="stat-content">
                <h4>Content Generated</h4>
                <p>Ready to create!</p>
              </div>
            </div>
            <div className="stat-card">
              <span className="stat-icon">⭐</span>
              <div className="stat-content">
                <h4>Account Status</h4>
                <p>Active Member</p>
              </div>
            </div>
          </div>

          <div className="profile-actions">
            <button className="profile-btn generate-btn">
              <span className="btn-icon">🚀</span>
              Start Creating
            </button>
            <button className="profile-btn history-btn">
              <span className="btn-icon">📚</span>
              View History
            </button>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="auth-container">
      <div className="auth-form-wrapper">
        <div className="auth-header">
          <div className="auth-illustration">
            <span className="auth-emoji auth-emoji-1">🎨</span>
            <span className="auth-emoji auth-emoji-2">✨</span>
            <span className="auth-emoji auth-emoji-3">🚀</span>
          </div>
          <h2 className="auth-title">
            {isLogin ? 'Welcome Back!' : 'Join the Creative Community!'}
            <span className="title-decoration">
              {isLogin ? '👋' : '🎉'}
            </span>
          </h2>
          <p className="auth-subtitle">
            {isLogin
              ? 'Sign in to continue creating amazing content with AI'
              : 'Create your account and start generating incredible content'
            }
          </p>
        </div>

        <form onSubmit={handleSubmit} className="auth-form">
          {!isLogin && (
            <div className="form-group">
              <label className="form-label">
                <span className="label-icon">👤</span>
                Username
              </label>
              <input
                type="text"
                name="username"
                value={formData.username}
                onChange={handleInputChange}
                placeholder="Choose a creative username"
                className="form-input"
                required={!isLogin}
                disabled={loading}
              />
            </div>
          )}

          <div className="form-group">
            <label className="form-label">
              <span className="label-icon">📧</span>
              Email
            </label>
            <input
              type="email"
              name="email"
              value={formData.email}
              onChange={handleInputChange}
              placeholder="<EMAIL>"
              className="form-input"
              required
              disabled={loading}
            />
          </div>

          <div className="form-group">
            <label className="form-label">
              <span className="label-icon">🔒</span>
              Password
            </label>
            <input
              type="password"
              name="password"
              value={formData.password}
              onChange={handleInputChange}
              placeholder={isLogin ? "Enter your password" : "Create a secure password (6+ characters)"}
              className="form-input"
              required
              disabled={loading}
              minLength={isLogin ? undefined : 6}
            />
          </div>

          {!isLogin && (
            <div className="form-group">
              <label className="form-label">
                <span className="label-icon">🔐</span>
                Confirm Password
              </label>
              <input
                type="password"
                name="confirmPassword"
                value={formData.confirmPassword}
                onChange={handleInputChange}
                placeholder="Confirm your password"
                className="form-input"
                required={!isLogin}
                disabled={loading}
              />
            </div>
          )}

          {message && (
            <div className={`message ${messageType}`}>
              <span className="message-icon">
                {messageType === 'success' ? '✅' : '❌'}
              </span>
              {message}
            </div>
          )}

          <button
            type="submit"
            className="auth-submit-btn"
            disabled={loading}
          >
            {loading ? (
              <>
                <span className="spinner"></span>
                {isLogin ? 'Signing In...' : 'Creating Account...'}
              </>
            ) : (
              <>
                <span className="btn-icon">
                  {isLogin ? '🚪' : '🎯'}
                </span>
                {isLogin ? 'Sign In' : 'Create Account'}
              </>
            )}
          </button>
        </form>

        <div className="auth-footer">
          <p className="auth-switch-text">
            {isLogin ? "Don't have an account?" : "Already have an account?"}
          </p>
          <button
            type="button"
            onClick={toggleMode}
            className="auth-switch-btn"
            disabled={loading}
          >
            <span className="switch-icon">
              {isLogin ? '🆕' : '🔄'}
            </span>
            {isLogin ? 'Create Account' : 'Sign In'}
          </button>
        </div>
      </div>
    </div>
  );
};

export default AuthForm;