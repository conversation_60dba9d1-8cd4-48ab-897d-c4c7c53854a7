import { useState } from 'react';
import './AuthForm.css';

const AuthForm = ({ user, setUser }) => {
  const [isLogin, setIsLogin] = useState(true);
  const [formData, setFormData] = useState({
    username: '',
    email: '',
    password: '',
    confirmPassword: ''
  });
  const [loading, setLoading] = useState(false);
  const [message, setMessage] = useState('');
  const [messageType, setMessageType] = useState(''); // 'success' or 'error'

  // Get API base URL
  const getApiUrl = () => {
    return process.env.NODE_ENV === 'production'
      ? '/api'
      : 'http://localhost:5000/api';
  };

  const handleInputChange = (e) => {
    setFormData({
      ...formData,
      [e.target.name]: e.target.value
    });
  };

  const showMessage = (text, type) => {
    setMessage(text);
    setMessageType(type);
    setTimeout(() => {
      setMessage('');
      setMessageType('');
    }, 5000);
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    setLoading(true);

    try {
      if (!isLogin) {
        // Registration validation
        if (formData.password !== formData.confirmPassword) {
          showMessage('Passwords do not match!', 'error');
          setLoading(false);
          return;
        }
        if (formData.password.length < 6) {
          showMessage('Password must be at least 6 characters long!', 'error');
          setLoading(false);
          return;
        }
      }

      const endpoint = isLogin ? '/auth/login' : '/auth/register';
      const payload = isLogin
        ? { email: formData.email, password: formData.password }
        : {
            username: formData.username,
            email: formData.email,
            password: formData.password
          };

      const response = await fetch(`${getApiUrl()}${endpoint}`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(payload),
      });

      const data = await response.json();

      if (response.ok) {
        // Success
        localStorage.setItem('authToken', data.token);
        localStorage.setItem('userData', JSON.stringify(data.user));
        setUser(data.user);
        showMessage(
          isLogin
            ? `Welcome back, ${data.user.username}! 🎉`
            : `Account created successfully! Welcome, ${data.user.username}! 🎉`,
          'success'
        );

        // Clear form
        setFormData({
          username: '',
          email: '',
          password: '',
          confirmPassword: ''
        });
      } else {
        // Error
        showMessage(data.error || 'Something went wrong!', 'error');
      }
    } catch (error) {
      showMessage('Unable to connect to the server. Please try again later.', 'error');
    } finally {
      setLoading(false);
    }
  };

  const toggleMode = () => {
    setIsLogin(!isLogin);
    setMessage('');
    setFormData({
      username: '',
      email: '',
      password: '',
      confirmPassword: ''
    });
  };

  if (user) {
    return (
      <div className="auth-container">
        <div className="user-profile">
          <div className="profile-header">
            <div className="profile-avatar">
              <span className="avatar-emoji">👤</span>
            </div>
            <div className="profile-info">
              <h3 className="profile-name">
                <span className="welcome-emoji">👋</span>
                Welcome, {user.username}!
              </h3>
              <p className="profile-email">{user.email}</p>
            </div>
          </div>

          <div className="profile-stats">
            <div className="stat-card">
              <span className="stat-icon">📝</span>
              <div className="stat-content">
                <h4>Content Generated</h4>
                <p>Ready to create!</p>
              </div>
            </div>
            <div className="stat-card">
              <span className="stat-icon">⭐</span>
              <div className="stat-content">
                <h4>Account Status</h4>
                <p>Active Member</p>
              </div>
            </div>
          </div>

          <div className="profile-actions">
            <button className="profile-btn generate-btn">
              <span className="btn-icon">🚀</span>
              Start Creating
            </button>
            <button className="profile-btn history-btn">
              <span className="btn-icon">📚</span>
              View History
            </button>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="auth-container">
      <div className="auth-form-wrapper">
        <div className="auth-header">
          <div className="auth-illustration">
            <div className="magic-circle">
              <div className="orbit orbit-1">
                <span className="orbit-emoji">🎨</span>
              </div>
              <div className="orbit orbit-2">
                <span className="orbit-emoji">✨</span>
              </div>
              <div className="orbit orbit-3">
                <span className="orbit-emoji">🚀</span>
              </div>
              <div className="center-logo">
                <span className="logo-main">🤖</span>
                <div className="energy-rings">
                  <div className="ring ring-1"></div>
                  <div className="ring ring-2"></div>
                  <div className="ring ring-3"></div>
                </div>
              </div>
            </div>
          </div>

          <div className="title-container">
            <h2 className="auth-title">
              <span className="title-text">
                {isLogin ? 'Welcome Back, Creator!' : 'Unleash Your Creative Power!'}
              </span>
              <div className="title-effects">
                <span className="title-decoration explosive">
                  {isLogin ? '⚡' : '💥'}
                </span>
                <div className="sparkle-trail">
                  <span className="sparkle">✨</span>
                  <span className="sparkle">⭐</span>
                  <span className="sparkle">💫</span>
                </div>
              </div>
            </h2>

            <div className="subtitle-container">
              <p className="auth-subtitle">
                {isLogin
                  ? '🎯 Ready to create mind-blowing content with AI superpowers?'
                  : '🌟 Join thousands of creators transforming ideas into reality!'
                }
              </p>
              <div className="impact-stats">
                <div className="stat-bubble">
                  <span className="stat-number">10K+</span>
                  <span className="stat-label">Creators</span>
                </div>
                <div className="stat-bubble">
                  <span className="stat-number">1M+</span>
                  <span className="stat-label">Content</span>
                </div>
                <div className="stat-bubble">
                  <span className="stat-number">∞</span>
                  <span className="stat-label">Possibilities</span>
                </div>
              </div>
            </div>
          </div>
        </div>

        <form onSubmit={handleSubmit} className="auth-form">
          {!isLogin && (
            <div className="form-group">
              <label className="form-label">
                <span className="label-icon">👤</span>
                Username
              </label>
              <input
                type="text"
                name="username"
                value={formData.username}
                onChange={handleInputChange}
                placeholder="Choose a creative username"
                className="form-input"
                required={!isLogin}
                disabled={loading}
              />
            </div>
          )}

          <div className="form-group">
            <label className="form-label">
              <span className="label-icon">📧</span>
              Email
            </label>
            <input
              type="email"
              name="email"
              value={formData.email}
              onChange={handleInputChange}
              placeholder="<EMAIL>"
              className="form-input"
              required
              disabled={loading}
            />
          </div>

          <div className="form-group">
            <label className="form-label">
              <span className="label-icon">🔒</span>
              Password
            </label>
            <input
              type="password"
              name="password"
              value={formData.password}
              onChange={handleInputChange}
              placeholder={isLogin ? "Enter your password" : "Create a secure password (6+ characters)"}
              className="form-input"
              required
              disabled={loading}
              minLength={isLogin ? undefined : 6}
            />
          </div>

          {!isLogin && (
            <div className="form-group">
              <label className="form-label">
                <span className="label-icon">🔐</span>
                Confirm Password
              </label>
              <input
                type="password"
                name="confirmPassword"
                value={formData.confirmPassword}
                onChange={handleInputChange}
                placeholder="Confirm your password"
                className="form-input"
                required={!isLogin}
                disabled={loading}
              />
            </div>
          )}

          {message && (
            <div className={`message ${messageType}`}>
              <span className="message-icon">
                {messageType === 'success' ? '✅' : '❌'}
              </span>
              {message}
            </div>
          )}

          <button
            type="submit"
            className="auth-submit-btn mega-button"
            disabled={loading}
          >
            <div className="button-bg-effect"></div>
            <div className="button-content">
              {loading ? (
                <>
                  <div className="loading-animation">
                    <div className="loading-orb"></div>
                    <div className="loading-particles">
                      <span className="particle">✨</span>
                      <span className="particle">⭐</span>
                      <span className="particle">💫</span>
                    </div>
                  </div>
                  <span className="loading-text">
                    {isLogin ? '🔮 Authenticating Magic...' : '🚀 Launching Your Journey...'}
                  </span>
                </>
              ) : (
                <>
                  <span className="btn-icon mega-icon">
                    {isLogin ? '⚡' : '�'}
                  </span>
                  <span className="btn-text">
                    {isLogin ? 'UNLEASH CREATIVITY' : 'START MY JOURNEY'}
                  </span>
                  <div className="button-particles">
                    <span className="btn-particle">✨</span>
                    <span className="btn-particle">💥</span>
                    <span className="btn-particle">🚀</span>
                  </div>
                </>
              )}
            </div>
          </button>

          {!isLogin && (
            <div className="power-features">
              <h4 className="features-title">
                <span className="features-icon">⚡</span>
                Unlock These Superpowers
              </h4>
              <div className="features-grid">
                <div className="feature-item">
                  <span className="feature-emoji">🧠</span>
                  <span className="feature-text">AI Brain Power</span>
                </div>
                <div className="feature-item">
                  <span className="feature-emoji">⚡</span>
                  <span className="feature-text">Lightning Speed</span>
                </div>
                <div className="feature-item">
                  <span className="feature-emoji">🎨</span>
                  <span className="feature-text">Unlimited Creativity</span>
                </div>
                <div className="feature-item">
                  <span className="feature-emoji">🚀</span>
                  <span className="feature-text">Instant Results</span>
                </div>
              </div>
            </div>
          )}
        </form>

        <div className="auth-footer">
          <div className="switch-container">
            <p className="auth-switch-text">
              {isLogin ? "🌟 Ready to join the revolution?" : "🚀 Already part of the magic?"}
            </p>
            <button
              type="button"
              onClick={toggleMode}
              className="auth-switch-btn impact-switch"
              disabled={loading}
            >
              <div className="switch-bg-effect"></div>
              <span className="switch-content">
                <span className="switch-icon explosive">
                  {isLogin ? '💥' : '⚡'}
                </span>
                <span className="switch-text">
                  {isLogin ? 'JOIN THE REVOLUTION' : 'ENTER THE MATRIX'}
                </span>
              </span>
            </button>
          </div>

          <div className="trust-indicators">
            <div className="trust-item">
              <span className="trust-icon">🔒</span>
              <span className="trust-text">Bank-Level Security</span>
            </div>
            <div className="trust-item">
              <span className="trust-icon">⚡</span>
              <span className="trust-text">Instant Access</span>
            </div>
            <div className="trust-item">
              <span className="trust-icon">🌍</span>
              <span className="trust-text">Global Community</span>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default AuthForm;