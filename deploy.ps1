# AI Content Generator Deployment Script
# This script helps deploy both frontend and backend components

param(
    [Parameter(Mandatory=$false)]
    [ValidateSet("dev", "prod")]
    [string]$Environment = "dev",

    [Parameter(Mandatory=$false)]
    [switch]$SkipTests,
    
    [Parameter(Mandatory=$false)]
    [switch]$BackendOnly,
    
    [Parameter(Mandatory=$false)]
    [switch]$FrontendOnly
)

Write-Host "AI Content Generator Deployment Script" -ForegroundColor Cyan
Write-Host "Environment: $Environment" -ForegroundColor Yellow

# Function to check if a command exists
function Test-Command($cmdname) {
    return [bool](Get-Command -Name $cmdname -ErrorAction SilentlyContinue)
}

# Check prerequisites
Write-Host "`nChecking prerequisites..." -ForegroundColor Green

if (-not (Test-Command "node")) {
    Write-Error "Node.js is not installed. Please install Node.js 16+ and try again."
    exit 1
}

if (-not (Test-Command "npm")) {
    Write-Error "npm is not installed. Please install npm and try again."
    exit 1
}

$nodeVersion = node --version
Write-Host "Node.js version: $nodeVersion" -ForegroundColor Green

# Backend deployment
if (-not $FrontendOnly) {
    Write-Host "`nDeploying Backend..." -ForegroundColor Cyan

    Set-Location "ai-generative/backend"

    # Install dependencies
    Write-Host "Installing backend dependencies..." -ForegroundColor Yellow
    npm install
    if ($LASTEXITCODE -ne 0) {
        Write-Error "Failed to install backend dependencies"
        exit 1
    }
    
    # Run tests (unless skipped)
    if (-not $SkipTests) {
        Write-Host "Running backend tests..." -ForegroundColor Yellow
        npm test
        if ($LASTEXITCODE -ne 0) {
            Write-Error "Backend tests failed"
            exit 1
        }
        Write-Host "All backend tests passed!" -ForegroundColor Green
    }
    
    # Check environment variables
    if (-not (Test-Path ".env")) {
        Write-Warning ".env file not found. Creating template..."
        $envContent = @"
OPENAI_API_KEY=your_openai_api_key_here
JWT_SECRET=your_jwt_secret_here
PORT=5000
"@
        $envContent | Out-File -FilePath ".env" -Encoding UTF8
        Write-Host "Please update the .env file with your actual values" -ForegroundColor Yellow
    }
    
    if ($Environment -eq "prod") {
        Write-Host "Production backend setup complete" -ForegroundColor Green
        Write-Host "   - Ensure environment variables are set in production" -ForegroundColor Yellow
        Write-Host "   - Consider using a process manager like PM2" -ForegroundColor Yellow
    } else {
        Write-Host "Starting backend in development mode..." -ForegroundColor Yellow
        Start-Process -FilePath "npm" -ArgumentList "start" -NoNewWindow
        Start-Sleep -Seconds 3
        Write-Host "Backend started on http://localhost:5000" -ForegroundColor Green
    }
    
    Set-Location "../.."
}

# Frontend deployment
if (-not $BackendOnly) {
    Write-Host "`nDeploying Frontend..." -ForegroundColor Cyan

    Set-Location "ai-generative/client"

    # Install dependencies
    Write-Host "Installing frontend dependencies..." -ForegroundColor Yellow
    npm install
    if ($LASTEXITCODE -ne 0) {
        Write-Error "Failed to install frontend dependencies"
        exit 1
    }
    
    # Run tests (unless skipped)
    if (-not $SkipTests) {
        Write-Host "Running frontend tests..." -ForegroundColor Yellow
        $env:CI = "true"  # Prevents watch mode in CI
        npm test -- --watchAll=false
        if ($LASTEXITCODE -ne 0) {
            Write-Error "Frontend tests failed"
            exit 1
        }
        Write-Host "All frontend tests passed!" -ForegroundColor Green
    }
    
    if ($Environment -eq "prod") {
        Write-Host "Building frontend for production..." -ForegroundColor Yellow
        npm run build
        if ($LASTEXITCODE -ne 0) {
            Write-Error "Frontend build failed"
            exit 1
        }
        Write-Host "Frontend built successfully!" -ForegroundColor Green
        Write-Host "   - Deploy the 'build' folder to your hosting service" -ForegroundColor Yellow
        Write-Host "   - Update API endpoints for production" -ForegroundColor Yellow
    } else {
        Write-Host "Starting frontend in development mode..." -ForegroundColor Yellow
        Start-Process -FilePath "npm" -ArgumentList "start" -NoNewWindow
        Start-Sleep -Seconds 5
        Write-Host "Frontend started on http://localhost:3000" -ForegroundColor Green
    }
    
    Set-Location "../.."
}

Write-Host "`nDeployment completed successfully!" -ForegroundColor Green

if ($Environment -eq "dev") {
    Write-Host "`nApplication URLs:" -ForegroundColor Cyan
    Write-Host "   Frontend: http://localhost:3000" -ForegroundColor White
    Write-Host "   Backend:  http://localhost:5000" -ForegroundColor White
    Write-Host "   API Docs: http://localhost:5000/api" -ForegroundColor White

    Write-Host "`nDevelopment Commands:" -ForegroundColor Cyan
    Write-Host "   Backend tests: cd ai-generative/backend; npm test" -ForegroundColor White
    Write-Host "   Frontend tests: cd ai-generative/client; npm test" -ForegroundColor White
    Write-Host "   Stop servers: Ctrl+C in respective terminals" -ForegroundColor White
}

if ($Environment -eq "prod") {
    Write-Host "`nProduction Deployment Notes:" -ForegroundColor Cyan
    Write-Host "   1. Set environment variables in production" -ForegroundColor White
    Write-Host "   2. Use a process manager (PM2, systemd, etc.)" -ForegroundColor White
    Write-Host "   3. Set up reverse proxy (nginx, Apache)" -ForegroundColor White
    Write-Host "   4. Enable HTTPS in production" -ForegroundColor White
    Write-Host "   5. Monitor logs and performance" -ForegroundColor White
}

Write-Host "`n✨ Happy coding!" -ForegroundColor Magenta
