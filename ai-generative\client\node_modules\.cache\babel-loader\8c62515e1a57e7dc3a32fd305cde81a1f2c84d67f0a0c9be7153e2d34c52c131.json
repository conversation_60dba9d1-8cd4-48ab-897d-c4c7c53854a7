{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\vedika\\\\ai-generative\\\\client\\\\src\\\\App.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport ContentTypeSelector from './components/ContentTypeSelector';\nimport ToneSelector from './components/ToneSelector';\nimport LengthSelector from './components/LengthSelector';\nimport Editor from './components/Editor';\nimport History from './components/History';\nimport AuthForm from './components/AuthForm';\nimport './App.css';\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nfunction App() {\n  _s();\n  // State for content generation form\n  const [contentType, setContentType] = useState('blog');\n  const [tone, setTone] = useState('professional');\n  const [length, setLength] = useState(200);\n  const [editorValue, setEditorValue] = useState('');\n  const [loading, setLoading] = useState(false);\n  const [topic, setTopic] = useState('');\n  const [activeTab, setActiveTab] = useState('generate');\n  const [user, setUser] = useState(null);\n\n  // Check for existing auth token\n  useEffect(() => {\n    const token = localStorage.getItem('authToken');\n    const userData = localStorage.getItem('userData');\n    if (token && userData) {\n      setUser(JSON.parse(userData));\n    }\n  }, []);\n\n  // Get API base URL\n  const getApiUrl = () => {\n    return process.env.NODE_ENV === 'production' ? '/api' : 'http://localhost:5000/api';\n  };\n\n  // Connect to backend API to generate content\n  const handleGenerate = async () => {\n    if (!topic.trim()) {\n      alert('Please enter a topic');\n      return;\n    }\n    setLoading(true);\n    setEditorValue('');\n    try {\n      const token = localStorage.getItem('authToken');\n      const headers = {\n        'Content-Type': 'application/json'\n      };\n      if (token) {\n        headers.Authorization = `Bearer ${token}`;\n      }\n      const response = await fetch(`${getApiUrl()}/generate`, {\n        method: 'POST',\n        headers,\n        body: JSON.stringify({\n          contentType: contentType === 'social' ? 'social post' : contentType,\n          tone,\n          length,\n          topic\n        })\n      });\n      const data = await response.json();\n      if (response.ok) {\n        setEditorValue(data.content || 'No content generated.');\n      } else {\n        setEditorValue(`Error: ${data.error || 'Failed to generate content'}`);\n      }\n    } catch (error) {\n      setEditorValue('Error: Unable to connect to the server.');\n    } finally {\n      setLoading(false);\n    }\n  };\n  const handleLogout = () => {\n    localStorage.removeItem('authToken');\n    localStorage.removeItem('userData');\n    setUser(null);\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"app\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"floating-elements\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"floating-element\",\n        children: \"\\uD83C\\uDFA8\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 88,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"floating-element\",\n        children: \"\\u2728\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 89,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"floating-element\",\n        children: \"\\uD83D\\uDCDD\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 90,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"floating-element\",\n        children: \"\\uD83D\\uDE80\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 91,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"floating-element\",\n        children: \"\\uD83D\\uDCA1\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 92,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"floating-element\",\n        children: \"\\uD83C\\uDFAF\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 93,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 87,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"header\", {\n      className: \"header\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"header-content\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"logo\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"logo-icon\",\n            children: \"\\u2728\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 100,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"h1\", {\n            children: \"AI Content Generator\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 101,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 99,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"nav\", {\n          className: \"nav\",\n          children: [/*#__PURE__*/_jsxDEV(\"button\", {\n            className: `nav-btn ${activeTab === 'generate' ? 'active' : ''}`,\n            onClick: () => setActiveTab('generate'),\n            children: [/*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"nav-icon\",\n              children: \"\\uD83D\\uDE80\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 108,\n              columnNumber: 15\n            }, this), \"Generate\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 104,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            className: `nav-btn ${activeTab === 'history' ? 'active' : ''}`,\n            onClick: () => setActiveTab('history'),\n            children: [/*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"nav-icon\",\n              children: \"\\uD83D\\uDCDA\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 115,\n              columnNumber: 15\n            }, this), \"History\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 111,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            className: `nav-btn ${activeTab === 'auth' ? 'active' : ''}`,\n            onClick: () => setActiveTab('auth'),\n            children: [/*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"nav-icon\",\n              children: \"\\uD83D\\uDC64\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 122,\n              columnNumber: 15\n            }, this), user ? user.username : 'Login']\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 118,\n            columnNumber: 13\n          }, this), user && /*#__PURE__*/_jsxDEV(\"button\", {\n            className: \"logout-btn\",\n            onClick: handleLogout,\n            children: [/*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"nav-icon\",\n              children: \"\\uD83D\\uDEAA\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 127,\n              columnNumber: 17\n            }, this), \"Logout\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 126,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 103,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 98,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 97,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"main\", {\n      className: \"main\",\n      children: [activeTab === 'generate' && /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"tab-content\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"hero-section\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"hero-illustration\",\n            children: [/*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"hero-emoji hero-emoji-1\",\n              children: \"\\uD83E\\uDD16\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 141,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"hero-emoji hero-emoji-2\",\n              children: \"\\u270D\\uFE0F\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 142,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"hero-emoji hero-emoji-3\",\n              children: \"\\uD83D\\uDCF1\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 143,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"hero-emoji hero-emoji-4\",\n              children: \"\\uD83D\\uDCBB\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 144,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 140,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"h2\", {\n            className: \"hero-title\",\n            children: [\"Create Amazing Content with AI\", /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"title-decoration\",\n              children: \"\\u2728\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 148,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 146,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"hero-subtitle\",\n            children: [\"Generate professional blog posts, engaging social media content, and compelling advertisements in seconds\", /*#__PURE__*/_jsxDEV(\"br\", {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 152,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"subtitle-emoji\",\n              children: \"\\uD83C\\uDFAF Powered by AI \\u2022 \\uD83D\\uDE80 Lightning Fast \\u2022 \\uD83C\\uDFA8 Creative\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 153,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 150,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 139,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"content-generator\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"generator-form\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"form-section\",\n              children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n                className: \"section-title\",\n                children: \"Content Settings\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 160,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"form-grid\",\n                children: [/*#__PURE__*/_jsxDEV(ContentTypeSelector, {\n                  value: contentType,\n                  onChange: setContentType\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 162,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(ToneSelector, {\n                  value: tone,\n                  onChange: setTone\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 163,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(LengthSelector, {\n                  value: length,\n                  onChange: setLength\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 164,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 161,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 159,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"form-section\",\n              children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n                className: \"section-title\",\n                children: \"Topic\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 169,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"topic-input-container\",\n                children: [/*#__PURE__*/_jsxDEV(\"input\", {\n                  type: \"text\",\n                  value: topic,\n                  onChange: e => setTopic(e.target.value),\n                  placeholder: \"What would you like to write about?\",\n                  className: \"topic-input\",\n                  onKeyDown: e => e.key === 'Enter' && handleGenerate()\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 171,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                  onClick: handleGenerate,\n                  disabled: loading || !topic.trim(),\n                  className: \"generate-btn\",\n                  children: loading ? /*#__PURE__*/_jsxDEV(_Fragment, {\n                    children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                      className: \"spinner\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 186,\n                      columnNumber: 27\n                    }, this), \"Generating...\"]\n                  }, void 0, true) : /*#__PURE__*/_jsxDEV(_Fragment, {\n                    children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                      className: \"btn-icon\",\n                      children: \"\\u2728\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 191,\n                      columnNumber: 27\n                    }, this), \"Generate Content\"]\n                  }, void 0, true)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 179,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 170,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 168,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 158,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"editor-section\",\n            children: /*#__PURE__*/_jsxDEV(Editor, {\n              value: editorValue,\n              onChange: setEditorValue,\n              onGenerate: handleGenerate,\n              loading: loading\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 201,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 200,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 157,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 138,\n        columnNumber: 11\n      }, this), activeTab === 'history' && /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"tab-content\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"section-header\",\n          children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n            className: \"section-title\",\n            children: \"Content History\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 215,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"section-subtitle\",\n            children: \"View and manage your previously generated content\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 216,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 214,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(History, {\n          user: user\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 218,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 213,\n        columnNumber: 11\n      }, this), activeTab === 'auth' && /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"tab-content\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"section-header\",\n          children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n            className: \"section-title\",\n            children: user ? `Welcome, ${user.username}!` : 'Login or Register'\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 225,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"section-subtitle\",\n            children: user ? 'Manage your account and view your content history' : 'Create an account to save your generated content'\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 228,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 224,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(AuthForm, {\n          user: user,\n          setUser: setUser\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 232,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 223,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 136,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"footer\", {\n      className: \"footer\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"footer-content\",\n        children: [/*#__PURE__*/_jsxDEV(\"p\", {\n          children: \"\\xA9 2025 AI Content Generator. Powered by OpenAI.\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 240,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"footer-links\",\n          children: [/*#__PURE__*/_jsxDEV(\"a\", {\n            href: \"#about\",\n            children: \"About\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 242,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"a\", {\n            href: \"#privacy\",\n            children: \"Privacy\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 243,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"a\", {\n            href: \"#terms\",\n            children: \"Terms\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 244,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 241,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 239,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 238,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 85,\n    columnNumber: 5\n  }, this);\n}\n_s(App, \"3OvULWt7sZdWPaRNbgGHQUXj4fE=\");\n_c = App;\nexport default App;\nvar _c;\n$RefreshReg$(_c, \"App\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "ContentTypeSelector", "ToneSelector", "LengthSelector", "Editor", "History", "AuthForm", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "App", "_s", "contentType", "setContentType", "tone", "setTone", "length", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "setEditorValue", "loading", "setLoading", "topic", "setTopic", "activeTab", "setActiveTab", "user", "setUser", "token", "localStorage", "getItem", "userData", "JSON", "parse", "getApiUrl", "process", "env", "NODE_ENV", "handleGenerate", "trim", "alert", "headers", "Authorization", "response", "fetch", "method", "body", "stringify", "data", "json", "ok", "content", "error", "handleLogout", "removeItem", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "onClick", "username", "value", "onChange", "type", "e", "target", "placeholder", "onKeyDown", "key", "disabled", "onGenerate", "href", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/vedika/ai-generative/client/src/App.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport ContentTypeSelector from './components/ContentTypeSelector';\nimport ToneSelector from './components/ToneSelector';\nimport LengthSelector from './components/LengthSelector';\nimport Editor from './components/Editor';\nimport History from './components/History';\nimport AuthForm from './components/AuthForm';\nimport './App.css';\n\nfunction App() {\n  // State for content generation form\n  const [contentType, setContentType] = useState('blog');\n  const [tone, setTone] = useState('professional');\n  const [length, setLength] = useState(200);\n  const [editorValue, setEditorValue] = useState('');\n  const [loading, setLoading] = useState(false);\n  const [topic, setTopic] = useState('');\n  const [activeTab, setActiveTab] = useState('generate');\n  const [user, setUser] = useState(null);\n\n  // Check for existing auth token\n  useEffect(() => {\n    const token = localStorage.getItem('authToken');\n    const userData = localStorage.getItem('userData');\n    if (token && userData) {\n      setUser(JSON.parse(userData));\n    }\n  }, []);\n\n  // Get API base URL\n  const getApiUrl = () => {\n    return process.env.NODE_ENV === 'production'\n      ? '/api'\n      : 'http://localhost:5000/api';\n  };\n\n  // Connect to backend API to generate content\n  const handleGenerate = async () => {\n    if (!topic.trim()) {\n      alert('Please enter a topic');\n      return;\n    }\n\n    setLoading(true);\n    setEditorValue('');\n\n    try {\n      const token = localStorage.getItem('authToken');\n      const headers = { 'Content-Type': 'application/json' };\n      if (token) {\n        headers.Authorization = `Bearer ${token}`;\n      }\n\n      const response = await fetch(`${getApiUrl()}/generate`, {\n        method: 'POST',\n        headers,\n        body: JSON.stringify({\n          contentType: contentType === 'social' ? 'social post' : contentType,\n          tone,\n          length,\n          topic\n        })\n      });\n\n      const data = await response.json();\n      if (response.ok) {\n        setEditorValue(data.content || 'No content generated.');\n      } else {\n        setEditorValue(`Error: ${data.error || 'Failed to generate content'}`);\n      }\n    } catch (error) {\n      setEditorValue('Error: Unable to connect to the server.');\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const handleLogout = () => {\n    localStorage.removeItem('authToken');\n    localStorage.removeItem('userData');\n    setUser(null);\n  };\n\n  return (\n    <div className=\"app\">\n      {/* Floating cartoon elements */}\n      <div className=\"floating-elements\">\n        <div className=\"floating-element\">🎨</div>\n        <div className=\"floating-element\">✨</div>\n        <div className=\"floating-element\">📝</div>\n        <div className=\"floating-element\">🚀</div>\n        <div className=\"floating-element\">💡</div>\n        <div className=\"floating-element\">🎯</div>\n      </div>\n\n      {/* Header */}\n      <header className=\"header\">\n        <div className=\"header-content\">\n          <div className=\"logo\">\n            <div className=\"logo-icon\">✨</div>\n            <h1>AI Content Generator</h1>\n          </div>\n          <nav className=\"nav\">\n            <button\n              className={`nav-btn ${activeTab === 'generate' ? 'active' : ''}`}\n              onClick={() => setActiveTab('generate')}\n            >\n              <span className=\"nav-icon\">🚀</span>\n              Generate\n            </button>\n            <button\n              className={`nav-btn ${activeTab === 'history' ? 'active' : ''}`}\n              onClick={() => setActiveTab('history')}\n            >\n              <span className=\"nav-icon\">📚</span>\n              History\n            </button>\n            <button\n              className={`nav-btn ${activeTab === 'auth' ? 'active' : ''}`}\n              onClick={() => setActiveTab('auth')}\n            >\n              <span className=\"nav-icon\">👤</span>\n              {user ? user.username : 'Login'}\n            </button>\n            {user && (\n              <button className=\"logout-btn\" onClick={handleLogout}>\n                <span className=\"nav-icon\">🚪</span>\n                Logout\n              </button>\n            )}\n          </nav>\n        </div>\n      </header>\n\n      {/* Main Content */}\n      <main className=\"main\">\n        {activeTab === 'generate' && (\n          <div className=\"tab-content\">\n            <div className=\"hero-section\">\n              <div className=\"hero-illustration\">\n                <span className=\"hero-emoji hero-emoji-1\">🤖</span>\n                <span className=\"hero-emoji hero-emoji-2\">✍️</span>\n                <span className=\"hero-emoji hero-emoji-3\">📱</span>\n                <span className=\"hero-emoji hero-emoji-4\">💻</span>\n              </div>\n              <h2 className=\"hero-title\">\n                Create Amazing Content with AI\n                <span className=\"title-decoration\">✨</span>\n              </h2>\n              <p className=\"hero-subtitle\">\n                Generate professional blog posts, engaging social media content, and compelling advertisements in seconds\n                <br />\n                <span className=\"subtitle-emoji\">🎯 Powered by AI • 🚀 Lightning Fast • 🎨 Creative</span>\n              </p>\n            </div>\n\n            <div className=\"content-generator\">\n              <div className=\"generator-form\">\n                <div className=\"form-section\">\n                  <h3 className=\"section-title\">Content Settings</h3>\n                  <div className=\"form-grid\">\n                    <ContentTypeSelector value={contentType} onChange={setContentType} />\n                    <ToneSelector value={tone} onChange={setTone} />\n                    <LengthSelector value={length} onChange={setLength} />\n                  </div>\n                </div>\n\n                <div className=\"form-section\">\n                  <h3 className=\"section-title\">Topic</h3>\n                  <div className=\"topic-input-container\">\n                    <input\n                      type=\"text\"\n                      value={topic}\n                      onChange={e => setTopic(e.target.value)}\n                      placeholder=\"What would you like to write about?\"\n                      className=\"topic-input\"\n                      onKeyDown={e => e.key === 'Enter' && handleGenerate()}\n                    />\n                    <button\n                      onClick={handleGenerate}\n                      disabled={loading || !topic.trim()}\n                      className=\"generate-btn\"\n                    >\n                      {loading ? (\n                        <>\n                          <span className=\"spinner\"></span>\n                          Generating...\n                        </>\n                      ) : (\n                        <>\n                          <span className=\"btn-icon\">✨</span>\n                          Generate Content\n                        </>\n                      )}\n                    </button>\n                  </div>\n                </div>\n              </div>\n\n              <div className=\"editor-section\">\n                <Editor\n                  value={editorValue}\n                  onChange={setEditorValue}\n                  onGenerate={handleGenerate}\n                  loading={loading}\n                />\n              </div>\n            </div>\n          </div>\n        )}\n\n        {activeTab === 'history' && (\n          <div className=\"tab-content\">\n            <div className=\"section-header\">\n              <h2 className=\"section-title\">Content History</h2>\n              <p className=\"section-subtitle\">View and manage your previously generated content</p>\n            </div>\n            <History user={user} />\n          </div>\n        )}\n\n        {activeTab === 'auth' && (\n          <div className=\"tab-content\">\n            <div className=\"section-header\">\n              <h2 className=\"section-title\">\n                {user ? `Welcome, ${user.username}!` : 'Login or Register'}\n              </h2>\n              <p className=\"section-subtitle\">\n                {user ? 'Manage your account and view your content history' : 'Create an account to save your generated content'}\n              </p>\n            </div>\n            <AuthForm user={user} setUser={setUser} />\n          </div>\n        )}\n      </main>\n\n      {/* Footer */}\n      <footer className=\"footer\">\n        <div className=\"footer-content\">\n          <p>&copy; 2025 AI Content Generator. Powered by OpenAI.</p>\n          <div className=\"footer-links\">\n            <a href=\"#about\">About</a>\n            <a href=\"#privacy\">Privacy</a>\n            <a href=\"#terms\">Terms</a>\n          </div>\n        </div>\n      </footer>\n    </div>\n  );\n}\n\nexport default App;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,OAAOC,mBAAmB,MAAM,kCAAkC;AAClE,OAAOC,YAAY,MAAM,2BAA2B;AACpD,OAAOC,cAAc,MAAM,6BAA6B;AACxD,OAAOC,MAAM,MAAM,qBAAqB;AACxC,OAAOC,OAAO,MAAM,sBAAsB;AAC1C,OAAOC,QAAQ,MAAM,uBAAuB;AAC5C,OAAO,WAAW;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAEnB,SAASC,GAAGA,CAAA,EAAG;EAAAC,EAAA;EACb;EACA,MAAM,CAACC,WAAW,EAAEC,cAAc,CAAC,GAAGf,QAAQ,CAAC,MAAM,CAAC;EACtD,MAAM,CAACgB,IAAI,EAAEC,OAAO,CAAC,GAAGjB,QAAQ,CAAC,cAAc,CAAC;EAChD,MAAM,CAACkB,MAAM,EAAEC,SAAS,CAAC,GAAGnB,QAAQ,CAAC,GAAG,CAAC;EACzC,MAAM,CAACoB,WAAW,EAAEC,cAAc,CAAC,GAAGrB,QAAQ,CAAC,EAAE,CAAC;EAClD,MAAM,CAACsB,OAAO,EAAEC,UAAU,CAAC,GAAGvB,QAAQ,CAAC,KAAK,CAAC;EAC7C,MAAM,CAACwB,KAAK,EAAEC,QAAQ,CAAC,GAAGzB,QAAQ,CAAC,EAAE,CAAC;EACtC,MAAM,CAAC0B,SAAS,EAAEC,YAAY,CAAC,GAAG3B,QAAQ,CAAC,UAAU,CAAC;EACtD,MAAM,CAAC4B,IAAI,EAAEC,OAAO,CAAC,GAAG7B,QAAQ,CAAC,IAAI,CAAC;;EAEtC;EACAC,SAAS,CAAC,MAAM;IACd,MAAM6B,KAAK,GAAGC,YAAY,CAACC,OAAO,CAAC,WAAW,CAAC;IAC/C,MAAMC,QAAQ,GAAGF,YAAY,CAACC,OAAO,CAAC,UAAU,CAAC;IACjD,IAAIF,KAAK,IAAIG,QAAQ,EAAE;MACrBJ,OAAO,CAACK,IAAI,CAACC,KAAK,CAACF,QAAQ,CAAC,CAAC;IAC/B;EACF,CAAC,EAAE,EAAE,CAAC;;EAEN;EACA,MAAMG,SAAS,GAAGA,CAAA,KAAM;IACtB,OAAOC,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,GACxC,MAAM,GACN,2BAA2B;EACjC,CAAC;;EAED;EACA,MAAMC,cAAc,GAAG,MAAAA,CAAA,KAAY;IACjC,IAAI,CAAChB,KAAK,CAACiB,IAAI,CAAC,CAAC,EAAE;MACjBC,KAAK,CAAC,sBAAsB,CAAC;MAC7B;IACF;IAEAnB,UAAU,CAAC,IAAI,CAAC;IAChBF,cAAc,CAAC,EAAE,CAAC;IAElB,IAAI;MACF,MAAMS,KAAK,GAAGC,YAAY,CAACC,OAAO,CAAC,WAAW,CAAC;MAC/C,MAAMW,OAAO,GAAG;QAAE,cAAc,EAAE;MAAmB,CAAC;MACtD,IAAIb,KAAK,EAAE;QACTa,OAAO,CAACC,aAAa,GAAG,UAAUd,KAAK,EAAE;MAC3C;MAEA,MAAMe,QAAQ,GAAG,MAAMC,KAAK,CAAC,GAAGV,SAAS,CAAC,CAAC,WAAW,EAAE;QACtDW,MAAM,EAAE,MAAM;QACdJ,OAAO;QACPK,IAAI,EAAEd,IAAI,CAACe,SAAS,CAAC;UACnBnC,WAAW,EAAEA,WAAW,KAAK,QAAQ,GAAG,aAAa,GAAGA,WAAW;UACnEE,IAAI;UACJE,MAAM;UACNM;QACF,CAAC;MACH,CAAC,CAAC;MAEF,MAAM0B,IAAI,GAAG,MAAML,QAAQ,CAACM,IAAI,CAAC,CAAC;MAClC,IAAIN,QAAQ,CAACO,EAAE,EAAE;QACf/B,cAAc,CAAC6B,IAAI,CAACG,OAAO,IAAI,uBAAuB,CAAC;MACzD,CAAC,MAAM;QACLhC,cAAc,CAAC,UAAU6B,IAAI,CAACI,KAAK,IAAI,4BAA4B,EAAE,CAAC;MACxE;IACF,CAAC,CAAC,OAAOA,KAAK,EAAE;MACdjC,cAAc,CAAC,yCAAyC,CAAC;IAC3D,CAAC,SAAS;MACRE,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;EAED,MAAMgC,YAAY,GAAGA,CAAA,KAAM;IACzBxB,YAAY,CAACyB,UAAU,CAAC,WAAW,CAAC;IACpCzB,YAAY,CAACyB,UAAU,CAAC,UAAU,CAAC;IACnC3B,OAAO,CAAC,IAAI,CAAC;EACf,CAAC;EAED,oBACEpB,OAAA;IAAKgD,SAAS,EAAC,KAAK;IAAAC,QAAA,gBAElBjD,OAAA;MAAKgD,SAAS,EAAC,mBAAmB;MAAAC,QAAA,gBAChCjD,OAAA;QAAKgD,SAAS,EAAC,kBAAkB;QAAAC,QAAA,EAAC;MAAE;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAK,CAAC,eAC1CrD,OAAA;QAAKgD,SAAS,EAAC,kBAAkB;QAAAC,QAAA,EAAC;MAAC;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAK,CAAC,eACzCrD,OAAA;QAAKgD,SAAS,EAAC,kBAAkB;QAAAC,QAAA,EAAC;MAAE;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAK,CAAC,eAC1CrD,OAAA;QAAKgD,SAAS,EAAC,kBAAkB;QAAAC,QAAA,EAAC;MAAE;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAK,CAAC,eAC1CrD,OAAA;QAAKgD,SAAS,EAAC,kBAAkB;QAAAC,QAAA,EAAC;MAAE;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAK,CAAC,eAC1CrD,OAAA;QAAKgD,SAAS,EAAC,kBAAkB;QAAAC,QAAA,EAAC;MAAE;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAK,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACvC,CAAC,eAGNrD,OAAA;MAAQgD,SAAS,EAAC,QAAQ;MAAAC,QAAA,eACxBjD,OAAA;QAAKgD,SAAS,EAAC,gBAAgB;QAAAC,QAAA,gBAC7BjD,OAAA;UAAKgD,SAAS,EAAC,MAAM;UAAAC,QAAA,gBACnBjD,OAAA;YAAKgD,SAAS,EAAC,WAAW;YAAAC,QAAA,EAAC;UAAC;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC,eAClCrD,OAAA;YAAAiD,QAAA,EAAI;UAAoB;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC1B,CAAC,eACNrD,OAAA;UAAKgD,SAAS,EAAC,KAAK;UAAAC,QAAA,gBAClBjD,OAAA;YACEgD,SAAS,EAAE,WAAW/B,SAAS,KAAK,UAAU,GAAG,QAAQ,GAAG,EAAE,EAAG;YACjEqC,OAAO,EAAEA,CAAA,KAAMpC,YAAY,CAAC,UAAU,CAAE;YAAA+B,QAAA,gBAExCjD,OAAA;cAAMgD,SAAS,EAAC,UAAU;cAAAC,QAAA,EAAC;YAAE;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,YAEtC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eACTrD,OAAA;YACEgD,SAAS,EAAE,WAAW/B,SAAS,KAAK,SAAS,GAAG,QAAQ,GAAG,EAAE,EAAG;YAChEqC,OAAO,EAAEA,CAAA,KAAMpC,YAAY,CAAC,SAAS,CAAE;YAAA+B,QAAA,gBAEvCjD,OAAA;cAAMgD,SAAS,EAAC,UAAU;cAAAC,QAAA,EAAC;YAAE;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,WAEtC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eACTrD,OAAA;YACEgD,SAAS,EAAE,WAAW/B,SAAS,KAAK,MAAM,GAAG,QAAQ,GAAG,EAAE,EAAG;YAC7DqC,OAAO,EAAEA,CAAA,KAAMpC,YAAY,CAAC,MAAM,CAAE;YAAA+B,QAAA,gBAEpCjD,OAAA;cAAMgD,SAAS,EAAC,UAAU;cAAAC,QAAA,EAAC;YAAE;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,EACnClC,IAAI,GAAGA,IAAI,CAACoC,QAAQ,GAAG,OAAO;UAAA;YAAAL,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACzB,CAAC,EACRlC,IAAI,iBACHnB,OAAA;YAAQgD,SAAS,EAAC,YAAY;YAACM,OAAO,EAAER,YAAa;YAAAG,QAAA,gBACnDjD,OAAA;cAAMgD,SAAS,EAAC,UAAU;cAAAC,QAAA,EAAC;YAAE;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,UAEtC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CACT;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACA,CAAC,eAGTrD,OAAA;MAAMgD,SAAS,EAAC,MAAM;MAAAC,QAAA,GACnBhC,SAAS,KAAK,UAAU,iBACvBjB,OAAA;QAAKgD,SAAS,EAAC,aAAa;QAAAC,QAAA,gBAC1BjD,OAAA;UAAKgD,SAAS,EAAC,cAAc;UAAAC,QAAA,gBAC3BjD,OAAA;YAAKgD,SAAS,EAAC,mBAAmB;YAAAC,QAAA,gBAChCjD,OAAA;cAAMgD,SAAS,EAAC,yBAAyB;cAAAC,QAAA,EAAC;YAAE;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eACnDrD,OAAA;cAAMgD,SAAS,EAAC,yBAAyB;cAAAC,QAAA,EAAC;YAAE;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eACnDrD,OAAA;cAAMgD,SAAS,EAAC,yBAAyB;cAAAC,QAAA,EAAC;YAAE;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eACnDrD,OAAA;cAAMgD,SAAS,EAAC,yBAAyB;cAAAC,QAAA,EAAC;YAAE;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAChD,CAAC,eACNrD,OAAA;YAAIgD,SAAS,EAAC,YAAY;YAAAC,QAAA,GAAC,gCAEzB,eAAAjD,OAAA;cAAMgD,SAAS,EAAC,kBAAkB;cAAAC,QAAA,EAAC;YAAC;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACzC,CAAC,eACLrD,OAAA;YAAGgD,SAAS,EAAC,eAAe;YAAAC,QAAA,GAAC,2GAE3B,eAAAjD,OAAA;cAAAkD,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC,eACNrD,OAAA;cAAMgD,SAAS,EAAC,gBAAgB;cAAAC,QAAA,EAAC;YAAkD;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACzF,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACD,CAAC,eAENrD,OAAA;UAAKgD,SAAS,EAAC,mBAAmB;UAAAC,QAAA,gBAChCjD,OAAA;YAAKgD,SAAS,EAAC,gBAAgB;YAAAC,QAAA,gBAC7BjD,OAAA;cAAKgD,SAAS,EAAC,cAAc;cAAAC,QAAA,gBAC3BjD,OAAA;gBAAIgD,SAAS,EAAC,eAAe;gBAAAC,QAAA,EAAC;cAAgB;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACnDrD,OAAA;gBAAKgD,SAAS,EAAC,WAAW;gBAAAC,QAAA,gBACxBjD,OAAA,CAACP,mBAAmB;kBAAC+D,KAAK,EAAEnD,WAAY;kBAACoD,QAAQ,EAAEnD;gBAAe;kBAAA4C,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,eACrErD,OAAA,CAACN,YAAY;kBAAC8D,KAAK,EAAEjD,IAAK;kBAACkD,QAAQ,EAAEjD;gBAAQ;kBAAA0C,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,eAChDrD,OAAA,CAACL,cAAc;kBAAC6D,KAAK,EAAE/C,MAAO;kBAACgD,QAAQ,EAAE/C;gBAAU;kBAAAwC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACnD,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eAENrD,OAAA;cAAKgD,SAAS,EAAC,cAAc;cAAAC,QAAA,gBAC3BjD,OAAA;gBAAIgD,SAAS,EAAC,eAAe;gBAAAC,QAAA,EAAC;cAAK;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACxCrD,OAAA;gBAAKgD,SAAS,EAAC,uBAAuB;gBAAAC,QAAA,gBACpCjD,OAAA;kBACE0D,IAAI,EAAC,MAAM;kBACXF,KAAK,EAAEzC,KAAM;kBACb0C,QAAQ,EAAEE,CAAC,IAAI3C,QAAQ,CAAC2C,CAAC,CAACC,MAAM,CAACJ,KAAK,CAAE;kBACxCK,WAAW,EAAC,qCAAqC;kBACjDb,SAAS,EAAC,aAAa;kBACvBc,SAAS,EAAEH,CAAC,IAAIA,CAAC,CAACI,GAAG,KAAK,OAAO,IAAIhC,cAAc,CAAC;gBAAE;kBAAAmB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACvD,CAAC,eACFrD,OAAA;kBACEsD,OAAO,EAAEvB,cAAe;kBACxBiC,QAAQ,EAAEnD,OAAO,IAAI,CAACE,KAAK,CAACiB,IAAI,CAAC,CAAE;kBACnCgB,SAAS,EAAC,cAAc;kBAAAC,QAAA,EAEvBpC,OAAO,gBACNb,OAAA,CAAAE,SAAA;oBAAA+C,QAAA,gBACEjD,OAAA;sBAAMgD,SAAS,EAAC;oBAAS;sBAAAE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAO,CAAC,iBAEnC;kBAAA,eAAE,CAAC,gBAEHrD,OAAA,CAAAE,SAAA;oBAAA+C,QAAA,gBACEjD,OAAA;sBAAMgD,SAAS,EAAC,UAAU;sBAAAC,QAAA,EAAC;oBAAC;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM,CAAC,oBAErC;kBAAA,eAAE;gBACH;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACK,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACN,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAENrD,OAAA;YAAKgD,SAAS,EAAC,gBAAgB;YAAAC,QAAA,eAC7BjD,OAAA,CAACJ,MAAM;cACL4D,KAAK,EAAE7C,WAAY;cACnB8C,QAAQ,EAAE7C,cAAe;cACzBqD,UAAU,EAAElC,cAAe;cAC3BlB,OAAO,EAAEA;YAAQ;cAAAqC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAClB;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CACN,EAEApC,SAAS,KAAK,SAAS,iBACtBjB,OAAA;QAAKgD,SAAS,EAAC,aAAa;QAAAC,QAAA,gBAC1BjD,OAAA;UAAKgD,SAAS,EAAC,gBAAgB;UAAAC,QAAA,gBAC7BjD,OAAA;YAAIgD,SAAS,EAAC,eAAe;YAAAC,QAAA,EAAC;UAAe;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eAClDrD,OAAA;YAAGgD,SAAS,EAAC,kBAAkB;YAAAC,QAAA,EAAC;UAAiD;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAClF,CAAC,eACNrD,OAAA,CAACH,OAAO;UAACsB,IAAI,EAAEA;QAAK;UAAA+B,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACpB,CACN,EAEApC,SAAS,KAAK,MAAM,iBACnBjB,OAAA;QAAKgD,SAAS,EAAC,aAAa;QAAAC,QAAA,gBAC1BjD,OAAA;UAAKgD,SAAS,EAAC,gBAAgB;UAAAC,QAAA,gBAC7BjD,OAAA;YAAIgD,SAAS,EAAC,eAAe;YAAAC,QAAA,EAC1B9B,IAAI,GAAG,YAAYA,IAAI,CAACoC,QAAQ,GAAG,GAAG;UAAmB;YAAAL,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACxD,CAAC,eACLrD,OAAA;YAAGgD,SAAS,EAAC,kBAAkB;YAAAC,QAAA,EAC5B9B,IAAI,GAAG,mDAAmD,GAAG;UAAkD;YAAA+B,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC/G,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACD,CAAC,eACNrD,OAAA,CAACF,QAAQ;UAACqB,IAAI,EAAEA,IAAK;UAACC,OAAO,EAAEA;QAAQ;UAAA8B,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACvC,CACN;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACG,CAAC,eAGPrD,OAAA;MAAQgD,SAAS,EAAC,QAAQ;MAAAC,QAAA,eACxBjD,OAAA;QAAKgD,SAAS,EAAC,gBAAgB;QAAAC,QAAA,gBAC7BjD,OAAA;UAAAiD,QAAA,EAAG;QAAoD;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC,eAC3DrD,OAAA;UAAKgD,SAAS,EAAC,cAAc;UAAAC,QAAA,gBAC3BjD,OAAA;YAAGkE,IAAI,EAAC,QAAQ;YAAAjB,QAAA,EAAC;UAAK;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC,eAC1BrD,OAAA;YAAGkE,IAAI,EAAC,UAAU;YAAAjB,QAAA,EAAC;UAAO;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC,eAC9BrD,OAAA;YAAGkE,IAAI,EAAC,QAAQ;YAAAjB,QAAA,EAAC;UAAK;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACvB,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACA,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACN,CAAC;AAEV;AAACjD,EAAA,CAhPQD,GAAG;AAAAgE,EAAA,GAAHhE,GAAG;AAkPZ,eAAeA,GAAG;AAAC,IAAAgE,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}