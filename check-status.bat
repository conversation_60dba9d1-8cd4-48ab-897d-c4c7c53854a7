@echo off
echo ========================================
echo AI Content Generator - Status Check
echo ========================================

echo.
echo Checking Backend Server (http://localhost:5000)...
curl -s http://localhost:5000 >nul 2>&1
if %errorlevel% equ 0 (
    echo ✓ Backend Server: RUNNING
) else (
    echo ✗ Backend Server: NOT RUNNING
)

echo.
echo Checking Frontend Server (http://localhost:3000)...
curl -s http://localhost:3000 >nul 2>&1
if %errorlevel% equ 0 (
    echo ✓ Frontend Server: RUNNING
) else (
    echo ✗ Frontend Server: NOT RUNNING
)

echo.
echo Testing API Endpoint...
curl -s -X POST http://localhost:5000/api/generate -H "Content-Type: application/json" -d "{\"contentType\":\"blog\",\"tone\":\"casual\",\"length\":50,\"topic\":\"test\"}" >nul 2>&1
if %errorlevel% equ 0 (
    echo ✓ API Endpoint: WORKING
) else (
    echo ✗ API Endpoint: NOT WORKING
)

echo.
echo ========================================
echo Status Check Complete
echo ========================================
echo.
echo If all services are running:
echo   Frontend: http://localhost:3000
echo   Backend:  http://localhost:5000
echo.
pause
