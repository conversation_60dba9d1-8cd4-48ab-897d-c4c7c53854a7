{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\vedika\\\\ai-generative\\\\client\\\\src\\\\components\\\\History.jsx\",\n  _s = $RefreshSig$();\nimport { useState, useEffect } from 'react';\nimport './History.css';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst History = ({\n  user,\n  onNavigate\n}) => {\n  _s();\n  const [history, setHistory] = useState([]);\n  const [loading, setLoading] = useState(false);\n  const [filter, setFilter] = useState('all');\n  const [sortBy, setSortBy] = useState('newest');\n  const [userCheckTime, setUserCheckTime] = useState(Date.now());\n\n  // Mock history data for demonstration (in real app, this would come from API)\n  const mockHistory = [{\n    id: 1,\n    contentType: 'blog',\n    tone: 'professional',\n    length: 300,\n    topic: 'AI in Healthcare',\n    content: 'Artificial Intelligence is revolutionizing healthcare by enabling faster diagnoses, personalized treatments, and improved patient outcomes. From medical imaging to drug discovery, AI technologies are transforming how we approach medicine...',\n    createdAt: new Date('2024-01-15T10:30:00'),\n    wordCount: 287\n  }, {\n    id: 2,\n    contentType: 'social',\n    tone: 'casual',\n    length: 100,\n    topic: 'Morning Motivation',\n    content: 'Good morning, creators! ☀️ Today is another chance to turn your ideas into reality. Remember, every expert was once a beginner. What amazing thing will you create today? #MondayMotivation #CreateDaily',\n    createdAt: new Date('2024-01-14T08:15:00'),\n    wordCount: 34\n  }, {\n    id: 3,\n    contentType: 'ad',\n    tone: 'friendly',\n    length: 150,\n    topic: 'Eco-Friendly Products',\n    content: 'Make a difference with every purchase! 🌱 Our eco-friendly products help you live sustainably without compromising on quality. Join thousands of happy customers who are making the planet greener, one choice at a time. Shop now and get 20% off your first order!',\n    createdAt: new Date('2024-01-13T16:45:00'),\n    wordCount: 48\n  }, {\n    id: 4,\n    contentType: 'blog',\n    tone: 'casual',\n    length: 500,\n    topic: 'Remote Work Tips',\n    content: 'Working from home can be amazing, but it definitely comes with its challenges! Here are some game-changing tips that have helped me stay productive and sane while working remotely...',\n    createdAt: new Date('2024-01-12T14:20:00'),\n    wordCount: 456\n  }];\n\n  // Enhanced user detection function\n  const detectUser = () => {\n    // First check the prop\n    if (user && user.username) {\n      console.log('History: User found in props:', user);\n      return user;\n    }\n\n    // Then check localStorage\n    const storedToken = localStorage.getItem('authToken');\n    const storedUserData = localStorage.getItem('userData');\n    if (storedToken && storedUserData) {\n      try {\n        const parsedUser = JSON.parse(storedUserData);\n        if (parsedUser && parsedUser.username) {\n          console.log('History: User found in localStorage:', parsedUser);\n          return parsedUser;\n        }\n      } catch (error) {\n        console.error('Error parsing stored user data:', error);\n        // Clear corrupted data\n        localStorage.removeItem('authToken');\n        localStorage.removeItem('userData');\n      }\n    }\n    console.log('History: No user found');\n    return null;\n  };\n  useEffect(() => {\n    const currentUser = detectUser();\n\n    // Simulate loading\n    setLoading(true);\n    setTimeout(() => {\n      if (currentUser) {\n        console.log('Setting mock history for user:', currentUser.username);\n        setHistory(mockHistory);\n      } else {\n        console.log('No user found, setting empty history');\n        setHistory([]);\n      }\n      setLoading(false);\n    }, 500);\n  }, [user, userCheckTime]); // Re-run when user prop changes or manual refresh\n\n  // Listen for localStorage changes (when user logs in/out in another tab)\n  useEffect(() => {\n    const handleStorageChange = e => {\n      if (e.key === 'authToken' || e.key === 'userData') {\n        console.log('Storage changed, refreshing user state');\n        setUserCheckTime(Date.now()); // Trigger re-render\n      }\n    };\n    window.addEventListener('storage', handleStorageChange);\n    return () => window.removeEventListener('storage', handleStorageChange);\n  }, []);\n\n  // Periodic check for user state changes (every 5 seconds)\n  useEffect(() => {\n    const interval = setInterval(() => {\n      const currentUser = detectUser();\n      const hasHistory = history.length > 0;\n\n      // If we have history but no user, or no history but have user, refresh\n      if (hasHistory && !currentUser || !hasHistory && currentUser) {\n        console.log('User state mismatch detected, refreshing');\n        setUserCheckTime(Date.now());\n      }\n    }, 5000);\n    return () => clearInterval(interval);\n  }, [history]);\n  const getFilteredAndSortedHistory = () => {\n    let filtered = history;\n    if (filter !== 'all') {\n      filtered = history.filter(item => item.contentType === filter);\n    }\n    return filtered.sort((a, b) => {\n      if (sortBy === 'newest') {\n        return new Date(b.createdAt) - new Date(a.createdAt);\n      } else if (sortBy === 'oldest') {\n        return new Date(a.createdAt) - new Date(b.createdAt);\n      } else if (sortBy === 'longest') {\n        return b.wordCount - a.wordCount;\n      } else if (sortBy === 'shortest') {\n        return a.wordCount - b.wordCount;\n      }\n      return 0;\n    });\n  };\n  const formatDate = date => {\n    return new Date(date).toLocaleDateString('en-US', {\n      year: 'numeric',\n      month: 'short',\n      day: 'numeric',\n      hour: '2-digit',\n      minute: '2-digit'\n    });\n  };\n  const getContentTypeIcon = type => {\n    switch (type) {\n      case 'blog':\n        return '📝';\n      case 'social':\n        return '📱';\n      case 'ad':\n        return '📢';\n      default:\n        return '📄';\n    }\n  };\n  const getToneColor = tone => {\n    switch (tone) {\n      case 'professional':\n        return '#667eea';\n      case 'casual':\n        return '#ff6b6b';\n      case 'friendly':\n        return '#10b981';\n      default:\n        return '#64748b';\n    }\n  };\n  const copyToClipboard = async content => {\n    try {\n      await navigator.clipboard.writeText(content);\n      // You could add a toast notification here\n    } catch (err) {\n      console.error('Failed to copy text: ', err);\n    }\n  };\n\n  // Use the enhanced user detection\n  const currentUser = detectUser();\n  if (!currentUser) {\n    console.log('History: No user found, showing demo with sign-up prompt');\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"history-container\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"demo-banner\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"banner-content\",\n          children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n            className: \"banner-title\",\n            children: [/*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"banner-icon\",\n              children: \"\\uD83C\\uDFAF\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 200,\n              columnNumber: 15\n            }, this), \"Preview Mode - Sign Up to Save Your Content!\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 199,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"banner-subtitle\",\n            children: \"Create an account to automatically save and manage all your generated content\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 203,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            className: \"banner-cta\",\n            onClick: () => {\n              console.log('Create account button clicked');\n              if (onNavigate) {\n                onNavigate('auth');\n              } else {\n                console.error('onNavigate function not available');\n              }\n            },\n            children: [/*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"cta-icon\",\n              children: \"\\uFFFD\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 217,\n              columnNumber: 15\n            }, this), \"Create Free Account\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 206,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 198,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 197,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"demo-content\",\n        children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n          className: \"demo-title\",\n          children: [/*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"demo-icon\",\n            children: \"\\uD83D\\uDC40\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 225,\n            columnNumber: 13\n          }, this), \"Here's what your history will look like:\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 224,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"history-grid\",\n          children: mockHistory.slice(0, 2).map(item => /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"history-card demo-card\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"demo-overlay\",\n              onClick: () => {\n                console.log('Demo card lock clicked');\n                if (onNavigate) {\n                  onNavigate('auth');\n                }\n              },\n              children: /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"demo-lock\",\n                children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"lock-icon\",\n                  children: \"\\uD83D\\uDD12\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 242,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"lock-text\",\n                  children: \"Sign up to unlock\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 243,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"lock-subtitle\",\n                  children: \"Click here to register\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 244,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 241,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 232,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"card-header\",\n              children: /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"card-type\",\n                children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"type-icon\",\n                  children: getContentTypeIcon(item.contentType)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 250,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"type-text\",\n                  children: item.contentType\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 251,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 249,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 248,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"card-content\",\n              children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n                className: \"content-topic\",\n                children: item.topic\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 256,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"content-preview\",\n                children: item.content.length > 100 ? `${item.content.substring(0, 100)}...` : item.content\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 257,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 255,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"card-meta\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"meta-tags\",\n                children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"tone-tag\",\n                  style: {\n                    backgroundColor: getToneColor(item.tone)\n                  },\n                  children: item.tone\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 267,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"word-count\",\n                  children: [item.wordCount, \" words\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 273,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 266,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"meta-date\",\n                children: formatDate(item.createdAt)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 277,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 265,\n              columnNumber: 17\n            }, this)]\n          }, item.id, true, {\n            fileName: _jsxFileName,\n            lineNumber: 231,\n            columnNumber: 15\n          }, this))\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 229,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 223,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 196,\n      columnNumber: 7\n    }, this);\n  }\n  if (loading) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"history-container\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"history-loading\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"loading-spinner\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 293,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"loading-text\",\n          children: \"Loading your creative history...\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 294,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"loading-emojis\",\n          children: [/*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"loading-emoji\",\n            children: \"\\uD83D\\uDCDD\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 296,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"loading-emoji\",\n            children: \"\\u2728\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 297,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"loading-emoji\",\n            children: \"\\uD83D\\uDCDA\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 298,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 295,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 292,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 291,\n      columnNumber: 7\n    }, this);\n  }\n  const filteredHistory = getFilteredAndSortedHistory();\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"history-container\",\n    children: [process.env.NODE_ENV === 'development' && /*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        background: '#f0f0f0',\n        padding: '1rem',\n        marginBottom: '1rem',\n        borderRadius: '8px',\n        fontSize: '0.8rem',\n        fontFamily: 'monospace'\n      },\n      children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n        children: \"Debug Info:\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 319,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"br\", {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 319,\n        columnNumber: 39\n      }, this), \"User Prop: \", user ? JSON.stringify(user) : 'null', /*#__PURE__*/_jsxDEV(\"br\", {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 320,\n        columnNumber: 60\n      }, this), \"Current User: \", currentUser ? JSON.stringify(currentUser) : 'null', /*#__PURE__*/_jsxDEV(\"br\", {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 321,\n        columnNumber: 77\n      }, this), \"Auth Token: \", localStorage.getItem('authToken') ? 'Present' : 'Missing', /*#__PURE__*/_jsxDEV(\"br\", {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 322,\n        columnNumber: 82\n      }, this), \"User Data: \", localStorage.getItem('userData') ? 'Present' : 'Missing', /*#__PURE__*/_jsxDEV(\"br\", {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 323,\n        columnNumber: 80\n      }, this), \"History Length: \", history.length, /*#__PURE__*/_jsxDEV(\"br\", {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 324,\n        columnNumber: 43\n      }, this), \"Loading: \", loading.toString()]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 311,\n      columnNumber: 9\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"history-header\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"history-stats\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"stat-item\",\n          children: [/*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"stat-number\",\n            children: history.length\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 331,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"stat-label\",\n            children: \"Total Created\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 332,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 330,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"stat-item\",\n          children: [/*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"stat-number\",\n            children: history.reduce((sum, item) => sum + item.wordCount, 0)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 335,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"stat-label\",\n            children: \"Words Written\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 336,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 334,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"stat-item\",\n          children: [/*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"stat-number\",\n            children: new Set(history.map(item => item.contentType)).size\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 339,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"stat-label\",\n            children: \"Content Types\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 340,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 338,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 329,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"history-controls\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"filter-group\",\n          children: [/*#__PURE__*/_jsxDEV(\"label\", {\n            className: \"control-label\",\n            children: [/*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"label-icon\",\n              children: \"\\uD83C\\uDFAF\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 347,\n              columnNumber: 15\n            }, this), \"Filter by Type\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 346,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"select\", {\n            value: filter,\n            onChange: e => setFilter(e.target.value),\n            className: \"control-select\",\n            children: [/*#__PURE__*/_jsxDEV(\"option\", {\n              value: \"all\",\n              children: \"All Content\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 355,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n              value: \"blog\",\n              children: \"\\uD83D\\uDCDD Blog Posts\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 356,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n              value: \"social\",\n              children: \"\\uD83D\\uDCF1 Social Posts\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 357,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n              value: \"ad\",\n              children: \"\\uD83D\\uDCE2 Advertisements\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 358,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 350,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 345,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"filter-group\",\n          children: [/*#__PURE__*/_jsxDEV(\"label\", {\n            className: \"control-label\",\n            children: [/*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"label-icon\",\n              children: \"\\uD83D\\uDCCA\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 364,\n              columnNumber: 15\n            }, this), \"Sort by\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 363,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"select\", {\n            value: sortBy,\n            onChange: e => setSortBy(e.target.value),\n            className: \"control-select\",\n            children: [/*#__PURE__*/_jsxDEV(\"option\", {\n              value: \"newest\",\n              children: \"Newest First\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 372,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n              value: \"oldest\",\n              children: \"Oldest First\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 373,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n              value: \"longest\",\n              children: \"Longest First\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 374,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n              value: \"shortest\",\n              children: \"Shortest First\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 375,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 367,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 362,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 344,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 328,\n      columnNumber: 7\n    }, this), filteredHistory.length === 0 ? /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"history-empty\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"empty-illustration\",\n        children: /*#__PURE__*/_jsxDEV(\"span\", {\n          className: \"empty-emoji\",\n          children: \"\\uD83D\\uDCDD\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 384,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 383,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n        className: \"empty-title\",\n        children: \"No Content Yet\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 386,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n        className: \"empty-subtitle\",\n        children: \"Start creating amazing content to see your history here!\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 387,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 382,\n      columnNumber: 9\n    }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"history-grid\",\n      children: filteredHistory.map(item => /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"history-card\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"card-header\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"card-type\",\n            children: [/*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"type-icon\",\n              children: getContentTypeIcon(item.contentType)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 397,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"type-text\",\n              children: item.contentType\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 398,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 396,\n            columnNumber: 17\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"card-actions\",\n            children: /*#__PURE__*/_jsxDEV(\"button\", {\n              className: \"action-btn copy-btn\",\n              onClick: () => copyToClipboard(item.content),\n              title: \"Copy content\",\n              children: \"\\uD83D\\uDCCB\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 401,\n              columnNumber: 19\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 400,\n            columnNumber: 17\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 395,\n          columnNumber: 15\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"card-content\",\n          children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n            className: \"content-topic\",\n            children: item.topic\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 412,\n            columnNumber: 17\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"content-preview\",\n            children: item.content.length > 150 ? `${item.content.substring(0, 150)}...` : item.content\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 413,\n            columnNumber: 17\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 411,\n          columnNumber: 15\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"card-meta\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"meta-tags\",\n            children: [/*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"tone-tag\",\n              style: {\n                backgroundColor: getToneColor(item.tone)\n              },\n              children: item.tone\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 423,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"word-count\",\n              children: [item.wordCount, \" words\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 429,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 422,\n            columnNumber: 17\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"meta-date\",\n            children: formatDate(item.createdAt)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 433,\n            columnNumber: 17\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 421,\n          columnNumber: 15\n        }, this)]\n      }, item.id, true, {\n        fileName: _jsxFileName,\n        lineNumber: 394,\n        columnNumber: 13\n      }, this))\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 392,\n      columnNumber: 9\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 308,\n    columnNumber: 5\n  }, this);\n};\n_s(History, \"2u8JtKack0CKdai7Y1mRh1pNM5s=\");\n_c = History;\nexport default History;\nvar _c;\n$RefreshReg$(_c, \"History\");", "map": {"version": 3, "names": ["useState", "useEffect", "jsxDEV", "_jsxDEV", "History", "user", "onNavigate", "_s", "history", "setHistory", "loading", "setLoading", "filter", "setFilter", "sortBy", "setSortBy", "userCheckTime", "setUserCheckTime", "Date", "now", "mockHistory", "id", "contentType", "tone", "length", "topic", "content", "createdAt", "wordCount", "detectUser", "username", "console", "log", "storedToken", "localStorage", "getItem", "storedUserData", "parsedUser", "JSON", "parse", "error", "removeItem", "currentUser", "setTimeout", "handleStorageChange", "e", "key", "window", "addEventListener", "removeEventListener", "interval", "setInterval", "hasHistory", "clearInterval", "getFilteredAndSortedHistory", "filtered", "item", "sort", "a", "b", "formatDate", "date", "toLocaleDateString", "year", "month", "day", "hour", "minute", "getContentTypeIcon", "type", "getToneColor", "copyToClipboard", "navigator", "clipboard", "writeText", "err", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "onClick", "slice", "map", "substring", "style", "backgroundColor", "filteredHistory", "process", "env", "NODE_ENV", "background", "padding", "marginBottom", "borderRadius", "fontSize", "fontFamily", "stringify", "toString", "reduce", "sum", "Set", "size", "value", "onChange", "target", "title", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/vedika/ai-generative/client/src/components/History.jsx"], "sourcesContent": ["import { useState, useEffect } from 'react';\r\nimport './History.css';\r\n\r\nconst History = ({ user, onNavigate }) => {\r\n  const [history, setHistory] = useState([]);\r\n  const [loading, setLoading] = useState(false);\r\n  const [filter, setFilter] = useState('all');\r\n  const [sortBy, setSortBy] = useState('newest');\r\n  const [userCheckTime, setUserCheckTime] = useState(Date.now());\r\n\r\n  // Mock history data for demonstration (in real app, this would come from API)\r\n  const mockHistory = [\r\n    {\r\n      id: 1,\r\n      contentType: 'blog',\r\n      tone: 'professional',\r\n      length: 300,\r\n      topic: 'AI in Healthcare',\r\n      content: 'Artificial Intelligence is revolutionizing healthcare by enabling faster diagnoses, personalized treatments, and improved patient outcomes. From medical imaging to drug discovery, AI technologies are transforming how we approach medicine...',\r\n      createdAt: new Date('2024-01-15T10:30:00'),\r\n      wordCount: 287\r\n    },\r\n    {\r\n      id: 2,\r\n      contentType: 'social',\r\n      tone: 'casual',\r\n      length: 100,\r\n      topic: 'Morning Motivation',\r\n      content: 'Good morning, creators! ☀️ Today is another chance to turn your ideas into reality. Remember, every expert was once a beginner. What amazing thing will you create today? #MondayMotivation #CreateDaily',\r\n      createdAt: new Date('2024-01-14T08:15:00'),\r\n      wordCount: 34\r\n    },\r\n    {\r\n      id: 3,\r\n      contentType: 'ad',\r\n      tone: 'friendly',\r\n      length: 150,\r\n      topic: 'Eco-Friendly Products',\r\n      content: 'Make a difference with every purchase! 🌱 Our eco-friendly products help you live sustainably without compromising on quality. Join thousands of happy customers who are making the planet greener, one choice at a time. Shop now and get 20% off your first order!',\r\n      createdAt: new Date('2024-01-13T16:45:00'),\r\n      wordCount: 48\r\n    },\r\n    {\r\n      id: 4,\r\n      contentType: 'blog',\r\n      tone: 'casual',\r\n      length: 500,\r\n      topic: 'Remote Work Tips',\r\n      content: 'Working from home can be amazing, but it definitely comes with its challenges! Here are some game-changing tips that have helped me stay productive and sane while working remotely...',\r\n      createdAt: new Date('2024-01-12T14:20:00'),\r\n      wordCount: 456\r\n    }\r\n  ];\r\n\r\n  // Enhanced user detection function\r\n  const detectUser = () => {\r\n    // First check the prop\r\n    if (user && user.username) {\r\n      console.log('History: User found in props:', user);\r\n      return user;\r\n    }\r\n\r\n    // Then check localStorage\r\n    const storedToken = localStorage.getItem('authToken');\r\n    const storedUserData = localStorage.getItem('userData');\r\n\r\n    if (storedToken && storedUserData) {\r\n      try {\r\n        const parsedUser = JSON.parse(storedUserData);\r\n        if (parsedUser && parsedUser.username) {\r\n          console.log('History: User found in localStorage:', parsedUser);\r\n          return parsedUser;\r\n        }\r\n      } catch (error) {\r\n        console.error('Error parsing stored user data:', error);\r\n        // Clear corrupted data\r\n        localStorage.removeItem('authToken');\r\n        localStorage.removeItem('userData');\r\n      }\r\n    }\r\n\r\n    console.log('History: No user found');\r\n    return null;\r\n  };\r\n\r\n  useEffect(() => {\r\n    const currentUser = detectUser();\r\n\r\n    // Simulate loading\r\n    setLoading(true);\r\n    setTimeout(() => {\r\n      if (currentUser) {\r\n        console.log('Setting mock history for user:', currentUser.username);\r\n        setHistory(mockHistory);\r\n      } else {\r\n        console.log('No user found, setting empty history');\r\n        setHistory([]);\r\n      }\r\n      setLoading(false);\r\n    }, 500);\r\n  }, [user, userCheckTime]); // Re-run when user prop changes or manual refresh\r\n\r\n  // Listen for localStorage changes (when user logs in/out in another tab)\r\n  useEffect(() => {\r\n    const handleStorageChange = (e) => {\r\n      if (e.key === 'authToken' || e.key === 'userData') {\r\n        console.log('Storage changed, refreshing user state');\r\n        setUserCheckTime(Date.now()); // Trigger re-render\r\n      }\r\n    };\r\n\r\n    window.addEventListener('storage', handleStorageChange);\r\n    return () => window.removeEventListener('storage', handleStorageChange);\r\n  }, []);\r\n\r\n  // Periodic check for user state changes (every 5 seconds)\r\n  useEffect(() => {\r\n    const interval = setInterval(() => {\r\n      const currentUser = detectUser();\r\n      const hasHistory = history.length > 0;\r\n\r\n      // If we have history but no user, or no history but have user, refresh\r\n      if ((hasHistory && !currentUser) || (!hasHistory && currentUser)) {\r\n        console.log('User state mismatch detected, refreshing');\r\n        setUserCheckTime(Date.now());\r\n      }\r\n    }, 5000);\r\n\r\n    return () => clearInterval(interval);\r\n  }, [history]);\r\n\r\n  const getFilteredAndSortedHistory = () => {\r\n    let filtered = history;\r\n\r\n    if (filter !== 'all') {\r\n      filtered = history.filter(item => item.contentType === filter);\r\n    }\r\n\r\n    return filtered.sort((a, b) => {\r\n      if (sortBy === 'newest') {\r\n        return new Date(b.createdAt) - new Date(a.createdAt);\r\n      } else if (sortBy === 'oldest') {\r\n        return new Date(a.createdAt) - new Date(b.createdAt);\r\n      } else if (sortBy === 'longest') {\r\n        return b.wordCount - a.wordCount;\r\n      } else if (sortBy === 'shortest') {\r\n        return a.wordCount - b.wordCount;\r\n      }\r\n      return 0;\r\n    });\r\n  };\r\n\r\n  const formatDate = (date) => {\r\n    return new Date(date).toLocaleDateString('en-US', {\r\n      year: 'numeric',\r\n      month: 'short',\r\n      day: 'numeric',\r\n      hour: '2-digit',\r\n      minute: '2-digit'\r\n    });\r\n  };\r\n\r\n  const getContentTypeIcon = (type) => {\r\n    switch (type) {\r\n      case 'blog': return '📝';\r\n      case 'social': return '📱';\r\n      case 'ad': return '📢';\r\n      default: return '📄';\r\n    }\r\n  };\r\n\r\n  const getToneColor = (tone) => {\r\n    switch (tone) {\r\n      case 'professional': return '#667eea';\r\n      case 'casual': return '#ff6b6b';\r\n      case 'friendly': return '#10b981';\r\n      default: return '#64748b';\r\n    }\r\n  };\r\n\r\n  const copyToClipboard = async (content) => {\r\n    try {\r\n      await navigator.clipboard.writeText(content);\r\n      // You could add a toast notification here\r\n    } catch (err) {\r\n      console.error('Failed to copy text: ', err);\r\n    }\r\n  };\r\n\r\n  // Use the enhanced user detection\r\n  const currentUser = detectUser();\r\n\r\n  if (!currentUser) {\r\n    console.log('History: No user found, showing demo with sign-up prompt');\r\n    return (\r\n      <div className=\"history-container\">\r\n        <div className=\"demo-banner\">\r\n          <div className=\"banner-content\">\r\n            <h3 className=\"banner-title\">\r\n              <span className=\"banner-icon\">🎯</span>\r\n              Preview Mode - Sign Up to Save Your Content!\r\n            </h3>\r\n            <p className=\"banner-subtitle\">\r\n              Create an account to automatically save and manage all your generated content\r\n            </p>\r\n            <button\r\n              className=\"banner-cta\"\r\n              onClick={() => {\r\n                console.log('Create account button clicked');\r\n                if (onNavigate) {\r\n                  onNavigate('auth');\r\n                } else {\r\n                  console.error('onNavigate function not available');\r\n                }\r\n              }}\r\n            >\r\n              <span className=\"cta-icon\">�</span>\r\n              Create Free Account\r\n            </button>\r\n          </div>\r\n        </div>\r\n\r\n        <div className=\"demo-content\">\r\n          <h4 className=\"demo-title\">\r\n            <span className=\"demo-icon\">👀</span>\r\n            Here's what your history will look like:\r\n          </h4>\r\n\r\n          <div className=\"history-grid\">\r\n            {mockHistory.slice(0, 2).map((item) => (\r\n              <div key={item.id} className=\"history-card demo-card\">\r\n                <div\r\n                  className=\"demo-overlay\"\r\n                  onClick={() => {\r\n                    console.log('Demo card lock clicked');\r\n                    if (onNavigate) {\r\n                      onNavigate('auth');\r\n                    }\r\n                  }}\r\n                >\r\n                  <div className=\"demo-lock\">\r\n                    <span className=\"lock-icon\">🔒</span>\r\n                    <span className=\"lock-text\">Sign up to unlock</span>\r\n                    <span className=\"lock-subtitle\">Click here to register</span>\r\n                  </div>\r\n                </div>\r\n\r\n                <div className=\"card-header\">\r\n                  <div className=\"card-type\">\r\n                    <span className=\"type-icon\">{getContentTypeIcon(item.contentType)}</span>\r\n                    <span className=\"type-text\">{item.contentType}</span>\r\n                  </div>\r\n                </div>\r\n\r\n                <div className=\"card-content\">\r\n                  <h4 className=\"content-topic\">{item.topic}</h4>\r\n                  <p className=\"content-preview\">\r\n                    {item.content.length > 100\r\n                      ? `${item.content.substring(0, 100)}...`\r\n                      : item.content\r\n                    }\r\n                  </p>\r\n                </div>\r\n\r\n                <div className=\"card-meta\">\r\n                  <div className=\"meta-tags\">\r\n                    <span\r\n                      className=\"tone-tag\"\r\n                      style={{ backgroundColor: getToneColor(item.tone) }}\r\n                    >\r\n                      {item.tone}\r\n                    </span>\r\n                    <span className=\"word-count\">\r\n                      {item.wordCount} words\r\n                    </span>\r\n                  </div>\r\n                  <div className=\"meta-date\">\r\n                    {formatDate(item.createdAt)}\r\n                  </div>\r\n                </div>\r\n              </div>\r\n            ))}\r\n          </div>\r\n        </div>\r\n      </div>\r\n    );\r\n  }\r\n\r\n  if (loading) {\r\n    return (\r\n      <div className=\"history-container\">\r\n        <div className=\"history-loading\">\r\n          <div className=\"loading-spinner\"></div>\r\n          <p className=\"loading-text\">Loading your creative history...</p>\r\n          <div className=\"loading-emojis\">\r\n            <span className=\"loading-emoji\">📝</span>\r\n            <span className=\"loading-emoji\">✨</span>\r\n            <span className=\"loading-emoji\">📚</span>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    );\r\n  }\r\n\r\n  const filteredHistory = getFilteredAndSortedHistory();\r\n\r\n  return (\r\n    <div className=\"history-container\">\r\n      {/* Debug Panel - Remove in production */}\r\n      {process.env.NODE_ENV === 'development' && (\r\n        <div style={{\r\n          background: '#f0f0f0',\r\n          padding: '1rem',\r\n          marginBottom: '1rem',\r\n          borderRadius: '8px',\r\n          fontSize: '0.8rem',\r\n          fontFamily: 'monospace'\r\n        }}>\r\n          <strong>Debug Info:</strong><br/>\r\n          User Prop: {user ? JSON.stringify(user) : 'null'}<br/>\r\n          Current User: {currentUser ? JSON.stringify(currentUser) : 'null'}<br/>\r\n          Auth Token: {localStorage.getItem('authToken') ? 'Present' : 'Missing'}<br/>\r\n          User Data: {localStorage.getItem('userData') ? 'Present' : 'Missing'}<br/>\r\n          History Length: {history.length}<br/>\r\n          Loading: {loading.toString()}\r\n        </div>\r\n      )}\r\n      <div className=\"history-header\">\r\n        <div className=\"history-stats\">\r\n          <div className=\"stat-item\">\r\n            <span className=\"stat-number\">{history.length}</span>\r\n            <span className=\"stat-label\">Total Created</span>\r\n          </div>\r\n          <div className=\"stat-item\">\r\n            <span className=\"stat-number\">{history.reduce((sum, item) => sum + item.wordCount, 0)}</span>\r\n            <span className=\"stat-label\">Words Written</span>\r\n          </div>\r\n          <div className=\"stat-item\">\r\n            <span className=\"stat-number\">{new Set(history.map(item => item.contentType)).size}</span>\r\n            <span className=\"stat-label\">Content Types</span>\r\n          </div>\r\n        </div>\r\n\r\n        <div className=\"history-controls\">\r\n          <div className=\"filter-group\">\r\n            <label className=\"control-label\">\r\n              <span className=\"label-icon\">🎯</span>\r\n              Filter by Type\r\n            </label>\r\n            <select\r\n              value={filter}\r\n              onChange={(e) => setFilter(e.target.value)}\r\n              className=\"control-select\"\r\n            >\r\n              <option value=\"all\">All Content</option>\r\n              <option value=\"blog\">📝 Blog Posts</option>\r\n              <option value=\"social\">📱 Social Posts</option>\r\n              <option value=\"ad\">📢 Advertisements</option>\r\n            </select>\r\n          </div>\r\n\r\n          <div className=\"filter-group\">\r\n            <label className=\"control-label\">\r\n              <span className=\"label-icon\">📊</span>\r\n              Sort by\r\n            </label>\r\n            <select\r\n              value={sortBy}\r\n              onChange={(e) => setSortBy(e.target.value)}\r\n              className=\"control-select\"\r\n            >\r\n              <option value=\"newest\">Newest First</option>\r\n              <option value=\"oldest\">Oldest First</option>\r\n              <option value=\"longest\">Longest First</option>\r\n              <option value=\"shortest\">Shortest First</option>\r\n            </select>\r\n          </div>\r\n        </div>\r\n      </div>\r\n\r\n      {filteredHistory.length === 0 ? (\r\n        <div className=\"history-empty\">\r\n          <div className=\"empty-illustration\">\r\n            <span className=\"empty-emoji\">📝</span>\r\n          </div>\r\n          <h3 className=\"empty-title\">No Content Yet</h3>\r\n          <p className=\"empty-subtitle\">\r\n            Start creating amazing content to see your history here!\r\n          </p>\r\n        </div>\r\n      ) : (\r\n        <div className=\"history-grid\">\r\n          {filteredHistory.map((item) => (\r\n            <div key={item.id} className=\"history-card\">\r\n              <div className=\"card-header\">\r\n                <div className=\"card-type\">\r\n                  <span className=\"type-icon\">{getContentTypeIcon(item.contentType)}</span>\r\n                  <span className=\"type-text\">{item.contentType}</span>\r\n                </div>\r\n                <div className=\"card-actions\">\r\n                  <button\r\n                    className=\"action-btn copy-btn\"\r\n                    onClick={() => copyToClipboard(item.content)}\r\n                    title=\"Copy content\"\r\n                  >\r\n                    📋\r\n                  </button>\r\n                </div>\r\n              </div>\r\n\r\n              <div className=\"card-content\">\r\n                <h4 className=\"content-topic\">{item.topic}</h4>\r\n                <p className=\"content-preview\">\r\n                  {item.content.length > 150\r\n                    ? `${item.content.substring(0, 150)}...`\r\n                    : item.content\r\n                  }\r\n                </p>\r\n              </div>\r\n\r\n              <div className=\"card-meta\">\r\n                <div className=\"meta-tags\">\r\n                  <span\r\n                    className=\"tone-tag\"\r\n                    style={{ backgroundColor: getToneColor(item.tone) }}\r\n                  >\r\n                    {item.tone}\r\n                  </span>\r\n                  <span className=\"word-count\">\r\n                    {item.wordCount} words\r\n                  </span>\r\n                </div>\r\n                <div className=\"meta-date\">\r\n                  {formatDate(item.createdAt)}\r\n                </div>\r\n              </div>\r\n            </div>\r\n          ))}\r\n        </div>\r\n      )}\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default History;"], "mappings": ";;AAAA,SAASA,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAC3C,OAAO,eAAe;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEvB,MAAMC,OAAO,GAAGA,CAAC;EAAEC,IAAI;EAAEC;AAAW,CAAC,KAAK;EAAAC,EAAA;EACxC,MAAM,CAACC,OAAO,EAAEC,UAAU,CAAC,GAAGT,QAAQ,CAAC,EAAE,CAAC;EAC1C,MAAM,CAACU,OAAO,EAAEC,UAAU,CAAC,GAAGX,QAAQ,CAAC,KAAK,CAAC;EAC7C,MAAM,CAACY,MAAM,EAAEC,SAAS,CAAC,GAAGb,QAAQ,CAAC,KAAK,CAAC;EAC3C,MAAM,CAACc,MAAM,EAAEC,SAAS,CAAC,GAAGf,QAAQ,CAAC,QAAQ,CAAC;EAC9C,MAAM,CAACgB,aAAa,EAAEC,gBAAgB,CAAC,GAAGjB,QAAQ,CAACkB,IAAI,CAACC,GAAG,CAAC,CAAC,CAAC;;EAE9D;EACA,MAAMC,WAAW,GAAG,CAClB;IACEC,EAAE,EAAE,CAAC;IACLC,WAAW,EAAE,MAAM;IACnBC,IAAI,EAAE,cAAc;IACpBC,MAAM,EAAE,GAAG;IACXC,KAAK,EAAE,kBAAkB;IACzBC,OAAO,EAAE,kPAAkP;IAC3PC,SAAS,EAAE,IAAIT,IAAI,CAAC,qBAAqB,CAAC;IAC1CU,SAAS,EAAE;EACb,CAAC,EACD;IACEP,EAAE,EAAE,CAAC;IACLC,WAAW,EAAE,QAAQ;IACrBC,IAAI,EAAE,QAAQ;IACdC,MAAM,EAAE,GAAG;IACXC,KAAK,EAAE,oBAAoB;IAC3BC,OAAO,EAAE,0MAA0M;IACnNC,SAAS,EAAE,IAAIT,IAAI,CAAC,qBAAqB,CAAC;IAC1CU,SAAS,EAAE;EACb,CAAC,EACD;IACEP,EAAE,EAAE,CAAC;IACLC,WAAW,EAAE,IAAI;IACjBC,IAAI,EAAE,UAAU;IAChBC,MAAM,EAAE,GAAG;IACXC,KAAK,EAAE,uBAAuB;IAC9BC,OAAO,EAAE,sQAAsQ;IAC/QC,SAAS,EAAE,IAAIT,IAAI,CAAC,qBAAqB,CAAC;IAC1CU,SAAS,EAAE;EACb,CAAC,EACD;IACEP,EAAE,EAAE,CAAC;IACLC,WAAW,EAAE,MAAM;IACnBC,IAAI,EAAE,QAAQ;IACdC,MAAM,EAAE,GAAG;IACXC,KAAK,EAAE,kBAAkB;IACzBC,OAAO,EAAE,wLAAwL;IACjMC,SAAS,EAAE,IAAIT,IAAI,CAAC,qBAAqB,CAAC;IAC1CU,SAAS,EAAE;EACb,CAAC,CACF;;EAED;EACA,MAAMC,UAAU,GAAGA,CAAA,KAAM;IACvB;IACA,IAAIxB,IAAI,IAAIA,IAAI,CAACyB,QAAQ,EAAE;MACzBC,OAAO,CAACC,GAAG,CAAC,+BAA+B,EAAE3B,IAAI,CAAC;MAClD,OAAOA,IAAI;IACb;;IAEA;IACA,MAAM4B,WAAW,GAAGC,YAAY,CAACC,OAAO,CAAC,WAAW,CAAC;IACrD,MAAMC,cAAc,GAAGF,YAAY,CAACC,OAAO,CAAC,UAAU,CAAC;IAEvD,IAAIF,WAAW,IAAIG,cAAc,EAAE;MACjC,IAAI;QACF,MAAMC,UAAU,GAAGC,IAAI,CAACC,KAAK,CAACH,cAAc,CAAC;QAC7C,IAAIC,UAAU,IAAIA,UAAU,CAACP,QAAQ,EAAE;UACrCC,OAAO,CAACC,GAAG,CAAC,sCAAsC,EAAEK,UAAU,CAAC;UAC/D,OAAOA,UAAU;QACnB;MACF,CAAC,CAAC,OAAOG,KAAK,EAAE;QACdT,OAAO,CAACS,KAAK,CAAC,iCAAiC,EAAEA,KAAK,CAAC;QACvD;QACAN,YAAY,CAACO,UAAU,CAAC,WAAW,CAAC;QACpCP,YAAY,CAACO,UAAU,CAAC,UAAU,CAAC;MACrC;IACF;IAEAV,OAAO,CAACC,GAAG,CAAC,wBAAwB,CAAC;IACrC,OAAO,IAAI;EACb,CAAC;EAED/B,SAAS,CAAC,MAAM;IACd,MAAMyC,WAAW,GAAGb,UAAU,CAAC,CAAC;;IAEhC;IACAlB,UAAU,CAAC,IAAI,CAAC;IAChBgC,UAAU,CAAC,MAAM;MACf,IAAID,WAAW,EAAE;QACfX,OAAO,CAACC,GAAG,CAAC,gCAAgC,EAAEU,WAAW,CAACZ,QAAQ,CAAC;QACnErB,UAAU,CAACW,WAAW,CAAC;MACzB,CAAC,MAAM;QACLW,OAAO,CAACC,GAAG,CAAC,sCAAsC,CAAC;QACnDvB,UAAU,CAAC,EAAE,CAAC;MAChB;MACAE,UAAU,CAAC,KAAK,CAAC;IACnB,CAAC,EAAE,GAAG,CAAC;EACT,CAAC,EAAE,CAACN,IAAI,EAAEW,aAAa,CAAC,CAAC,CAAC,CAAC;;EAE3B;EACAf,SAAS,CAAC,MAAM;IACd,MAAM2C,mBAAmB,GAAIC,CAAC,IAAK;MACjC,IAAIA,CAAC,CAACC,GAAG,KAAK,WAAW,IAAID,CAAC,CAACC,GAAG,KAAK,UAAU,EAAE;QACjDf,OAAO,CAACC,GAAG,CAAC,wCAAwC,CAAC;QACrDf,gBAAgB,CAACC,IAAI,CAACC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;MAChC;IACF,CAAC;IAED4B,MAAM,CAACC,gBAAgB,CAAC,SAAS,EAAEJ,mBAAmB,CAAC;IACvD,OAAO,MAAMG,MAAM,CAACE,mBAAmB,CAAC,SAAS,EAAEL,mBAAmB,CAAC;EACzE,CAAC,EAAE,EAAE,CAAC;;EAEN;EACA3C,SAAS,CAAC,MAAM;IACd,MAAMiD,QAAQ,GAAGC,WAAW,CAAC,MAAM;MACjC,MAAMT,WAAW,GAAGb,UAAU,CAAC,CAAC;MAChC,MAAMuB,UAAU,GAAG5C,OAAO,CAACgB,MAAM,GAAG,CAAC;;MAErC;MACA,IAAK4B,UAAU,IAAI,CAACV,WAAW,IAAM,CAACU,UAAU,IAAIV,WAAY,EAAE;QAChEX,OAAO,CAACC,GAAG,CAAC,0CAA0C,CAAC;QACvDf,gBAAgB,CAACC,IAAI,CAACC,GAAG,CAAC,CAAC,CAAC;MAC9B;IACF,CAAC,EAAE,IAAI,CAAC;IAER,OAAO,MAAMkC,aAAa,CAACH,QAAQ,CAAC;EACtC,CAAC,EAAE,CAAC1C,OAAO,CAAC,CAAC;EAEb,MAAM8C,2BAA2B,GAAGA,CAAA,KAAM;IACxC,IAAIC,QAAQ,GAAG/C,OAAO;IAEtB,IAAII,MAAM,KAAK,KAAK,EAAE;MACpB2C,QAAQ,GAAG/C,OAAO,CAACI,MAAM,CAAC4C,IAAI,IAAIA,IAAI,CAAClC,WAAW,KAAKV,MAAM,CAAC;IAChE;IAEA,OAAO2C,QAAQ,CAACE,IAAI,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAK;MAC7B,IAAI7C,MAAM,KAAK,QAAQ,EAAE;QACvB,OAAO,IAAII,IAAI,CAACyC,CAAC,CAAChC,SAAS,CAAC,GAAG,IAAIT,IAAI,CAACwC,CAAC,CAAC/B,SAAS,CAAC;MACtD,CAAC,MAAM,IAAIb,MAAM,KAAK,QAAQ,EAAE;QAC9B,OAAO,IAAII,IAAI,CAACwC,CAAC,CAAC/B,SAAS,CAAC,GAAG,IAAIT,IAAI,CAACyC,CAAC,CAAChC,SAAS,CAAC;MACtD,CAAC,MAAM,IAAIb,MAAM,KAAK,SAAS,EAAE;QAC/B,OAAO6C,CAAC,CAAC/B,SAAS,GAAG8B,CAAC,CAAC9B,SAAS;MAClC,CAAC,MAAM,IAAId,MAAM,KAAK,UAAU,EAAE;QAChC,OAAO4C,CAAC,CAAC9B,SAAS,GAAG+B,CAAC,CAAC/B,SAAS;MAClC;MACA,OAAO,CAAC;IACV,CAAC,CAAC;EACJ,CAAC;EAED,MAAMgC,UAAU,GAAIC,IAAI,IAAK;IAC3B,OAAO,IAAI3C,IAAI,CAAC2C,IAAI,CAAC,CAACC,kBAAkB,CAAC,OAAO,EAAE;MAChDC,IAAI,EAAE,SAAS;MACfC,KAAK,EAAE,OAAO;MACdC,GAAG,EAAE,SAAS;MACdC,IAAI,EAAE,SAAS;MACfC,MAAM,EAAE;IACV,CAAC,CAAC;EACJ,CAAC;EAED,MAAMC,kBAAkB,GAAIC,IAAI,IAAK;IACnC,QAAQA,IAAI;MACV,KAAK,MAAM;QAAE,OAAO,IAAI;MACxB,KAAK,QAAQ;QAAE,OAAO,IAAI;MAC1B,KAAK,IAAI;QAAE,OAAO,IAAI;MACtB;QAAS,OAAO,IAAI;IACtB;EACF,CAAC;EAED,MAAMC,YAAY,GAAI/C,IAAI,IAAK;IAC7B,QAAQA,IAAI;MACV,KAAK,cAAc;QAAE,OAAO,SAAS;MACrC,KAAK,QAAQ;QAAE,OAAO,SAAS;MAC/B,KAAK,UAAU;QAAE,OAAO,SAAS;MACjC;QAAS,OAAO,SAAS;IAC3B;EACF,CAAC;EAED,MAAMgD,eAAe,GAAG,MAAO7C,OAAO,IAAK;IACzC,IAAI;MACF,MAAM8C,SAAS,CAACC,SAAS,CAACC,SAAS,CAAChD,OAAO,CAAC;MAC5C;IACF,CAAC,CAAC,OAAOiD,GAAG,EAAE;MACZ5C,OAAO,CAACS,KAAK,CAAC,uBAAuB,EAAEmC,GAAG,CAAC;IAC7C;EACF,CAAC;;EAED;EACA,MAAMjC,WAAW,GAAGb,UAAU,CAAC,CAAC;EAEhC,IAAI,CAACa,WAAW,EAAE;IAChBX,OAAO,CAACC,GAAG,CAAC,0DAA0D,CAAC;IACvE,oBACE7B,OAAA;MAAKyE,SAAS,EAAC,mBAAmB;MAAAC,QAAA,gBAChC1E,OAAA;QAAKyE,SAAS,EAAC,aAAa;QAAAC,QAAA,eAC1B1E,OAAA;UAAKyE,SAAS,EAAC,gBAAgB;UAAAC,QAAA,gBAC7B1E,OAAA;YAAIyE,SAAS,EAAC,cAAc;YAAAC,QAAA,gBAC1B1E,OAAA;cAAMyE,SAAS,EAAC,aAAa;cAAAC,QAAA,EAAC;YAAE;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,gDAEzC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACL9E,OAAA;YAAGyE,SAAS,EAAC,iBAAiB;YAAAC,QAAA,EAAC;UAE/B;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC,eACJ9E,OAAA;YACEyE,SAAS,EAAC,YAAY;YACtBM,OAAO,EAAEA,CAAA,KAAM;cACbnD,OAAO,CAACC,GAAG,CAAC,+BAA+B,CAAC;cAC5C,IAAI1B,UAAU,EAAE;gBACdA,UAAU,CAAC,MAAM,CAAC;cACpB,CAAC,MAAM;gBACLyB,OAAO,CAACS,KAAK,CAAC,mCAAmC,CAAC;cACpD;YACF,CAAE;YAAAqC,QAAA,gBAEF1E,OAAA;cAAMyE,SAAS,EAAC,UAAU;cAAAC,QAAA,EAAC;YAAC;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,uBAErC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAEN9E,OAAA;QAAKyE,SAAS,EAAC,cAAc;QAAAC,QAAA,gBAC3B1E,OAAA;UAAIyE,SAAS,EAAC,YAAY;UAAAC,QAAA,gBACxB1E,OAAA;YAAMyE,SAAS,EAAC,WAAW;YAAAC,QAAA,EAAC;UAAE;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,4CAEvC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eAEL9E,OAAA;UAAKyE,SAAS,EAAC,cAAc;UAAAC,QAAA,EAC1BzD,WAAW,CAAC+D,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAACC,GAAG,CAAE5B,IAAI,iBAChCrD,OAAA;YAAmByE,SAAS,EAAC,wBAAwB;YAAAC,QAAA,gBACnD1E,OAAA;cACEyE,SAAS,EAAC,cAAc;cACxBM,OAAO,EAAEA,CAAA,KAAM;gBACbnD,OAAO,CAACC,GAAG,CAAC,wBAAwB,CAAC;gBACrC,IAAI1B,UAAU,EAAE;kBACdA,UAAU,CAAC,MAAM,CAAC;gBACpB;cACF,CAAE;cAAAuE,QAAA,eAEF1E,OAAA;gBAAKyE,SAAS,EAAC,WAAW;gBAAAC,QAAA,gBACxB1E,OAAA;kBAAMyE,SAAS,EAAC,WAAW;kBAAAC,QAAA,EAAC;gBAAE;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,eACrC9E,OAAA;kBAAMyE,SAAS,EAAC,WAAW;kBAAAC,QAAA,EAAC;gBAAiB;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,eACpD9E,OAAA;kBAAMyE,SAAS,EAAC,eAAe;kBAAAC,QAAA,EAAC;gBAAsB;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC1D;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eAEN9E,OAAA;cAAKyE,SAAS,EAAC,aAAa;cAAAC,QAAA,eAC1B1E,OAAA;gBAAKyE,SAAS,EAAC,WAAW;gBAAAC,QAAA,gBACxB1E,OAAA;kBAAMyE,SAAS,EAAC,WAAW;kBAAAC,QAAA,EAAET,kBAAkB,CAACZ,IAAI,CAAClC,WAAW;gBAAC;kBAAAwD,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eACzE9E,OAAA;kBAAMyE,SAAS,EAAC,WAAW;kBAAAC,QAAA,EAAErB,IAAI,CAAClC;gBAAW;kBAAAwD,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAClD;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eAEN9E,OAAA;cAAKyE,SAAS,EAAC,cAAc;cAAAC,QAAA,gBAC3B1E,OAAA;gBAAIyE,SAAS,EAAC,eAAe;gBAAAC,QAAA,EAAErB,IAAI,CAAC/B;cAAK;gBAAAqD,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,eAC/C9E,OAAA;gBAAGyE,SAAS,EAAC,iBAAiB;gBAAAC,QAAA,EAC3BrB,IAAI,CAAC9B,OAAO,CAACF,MAAM,GAAG,GAAG,GACtB,GAAGgC,IAAI,CAAC9B,OAAO,CAAC2D,SAAS,CAAC,CAAC,EAAE,GAAG,CAAC,KAAK,GACtC7B,IAAI,CAAC9B;cAAO;gBAAAoD,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAEf,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACD,CAAC,eAEN9E,OAAA;cAAKyE,SAAS,EAAC,WAAW;cAAAC,QAAA,gBACxB1E,OAAA;gBAAKyE,SAAS,EAAC,WAAW;gBAAAC,QAAA,gBACxB1E,OAAA;kBACEyE,SAAS,EAAC,UAAU;kBACpBU,KAAK,EAAE;oBAAEC,eAAe,EAAEjB,YAAY,CAACd,IAAI,CAACjC,IAAI;kBAAE,CAAE;kBAAAsD,QAAA,EAEnDrB,IAAI,CAACjC;gBAAI;kBAAAuD,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACN,CAAC,eACP9E,OAAA;kBAAMyE,SAAS,EAAC,YAAY;kBAAAC,QAAA,GACzBrB,IAAI,CAAC5B,SAAS,EAAC,QAClB;gBAAA;kBAAAkD,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACJ,CAAC,eACN9E,OAAA;gBAAKyE,SAAS,EAAC,WAAW;gBAAAC,QAAA,EACvBjB,UAAU,CAACJ,IAAI,CAAC7B,SAAS;cAAC;gBAAAmD,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACxB,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC;UAAA,GAjDEzB,IAAI,CAACnC,EAAE;YAAAyD,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAkDZ,CACN;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAEV;EAEA,IAAIvE,OAAO,EAAE;IACX,oBACEP,OAAA;MAAKyE,SAAS,EAAC,mBAAmB;MAAAC,QAAA,eAChC1E,OAAA;QAAKyE,SAAS,EAAC,iBAAiB;QAAAC,QAAA,gBAC9B1E,OAAA;UAAKyE,SAAS,EAAC;QAAiB;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,eACvC9E,OAAA;UAAGyE,SAAS,EAAC,cAAc;UAAAC,QAAA,EAAC;QAAgC;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC,eAChE9E,OAAA;UAAKyE,SAAS,EAAC,gBAAgB;UAAAC,QAAA,gBAC7B1E,OAAA;YAAMyE,SAAS,EAAC,eAAe;YAAAC,QAAA,EAAC;UAAE;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eACzC9E,OAAA;YAAMyE,SAAS,EAAC,eAAe;YAAAC,QAAA,EAAC;UAAC;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eACxC9E,OAAA;YAAMyE,SAAS,EAAC,eAAe;YAAAC,QAAA,EAAC;UAAE;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACtC,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAEV;EAEA,MAAMO,eAAe,GAAGlC,2BAA2B,CAAC,CAAC;EAErD,oBACEnD,OAAA;IAAKyE,SAAS,EAAC,mBAAmB;IAAAC,QAAA,GAE/BY,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,aAAa,iBACrCxF,OAAA;MAAKmF,KAAK,EAAE;QACVM,UAAU,EAAE,SAAS;QACrBC,OAAO,EAAE,MAAM;QACfC,YAAY,EAAE,MAAM;QACpBC,YAAY,EAAE,KAAK;QACnBC,QAAQ,EAAE,QAAQ;QAClBC,UAAU,EAAE;MACd,CAAE;MAAApB,QAAA,gBACA1E,OAAA;QAAA0E,QAAA,EAAQ;MAAW;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC,eAAA9E,OAAA;QAAA2E,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eACtB,EAAC5E,IAAI,GAAGiC,IAAI,CAAC4D,SAAS,CAAC7F,IAAI,CAAC,GAAG,MAAM,eAACF,OAAA;QAAA2E,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,kBACxC,EAACvC,WAAW,GAAGJ,IAAI,CAAC4D,SAAS,CAACxD,WAAW,CAAC,GAAG,MAAM,eAACvC,OAAA;QAAA2E,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,gBAC3D,EAAC/C,YAAY,CAACC,OAAO,CAAC,WAAW,CAAC,GAAG,SAAS,GAAG,SAAS,eAAChC,OAAA;QAAA2E,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eACjE,EAAC/C,YAAY,CAACC,OAAO,CAAC,UAAU,CAAC,GAAG,SAAS,GAAG,SAAS,eAAChC,OAAA;QAAA2E,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,oBAC1D,EAACzE,OAAO,CAACgB,MAAM,eAACrB,OAAA;QAAA2E,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,aAC5B,EAACvE,OAAO,CAACyF,QAAQ,CAAC,CAAC;IAAA;MAAArB,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACzB,CACN,eACD9E,OAAA;MAAKyE,SAAS,EAAC,gBAAgB;MAAAC,QAAA,gBAC7B1E,OAAA;QAAKyE,SAAS,EAAC,eAAe;QAAAC,QAAA,gBAC5B1E,OAAA;UAAKyE,SAAS,EAAC,WAAW;UAAAC,QAAA,gBACxB1E,OAAA;YAAMyE,SAAS,EAAC,aAAa;YAAAC,QAAA,EAAErE,OAAO,CAACgB;UAAM;YAAAsD,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC,eACrD9E,OAAA;YAAMyE,SAAS,EAAC,YAAY;YAAAC,QAAA,EAAC;UAAa;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC9C,CAAC,eACN9E,OAAA;UAAKyE,SAAS,EAAC,WAAW;UAAAC,QAAA,gBACxB1E,OAAA;YAAMyE,SAAS,EAAC,aAAa;YAAAC,QAAA,EAAErE,OAAO,CAAC4F,MAAM,CAAC,CAACC,GAAG,EAAE7C,IAAI,KAAK6C,GAAG,GAAG7C,IAAI,CAAC5B,SAAS,EAAE,CAAC;UAAC;YAAAkD,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC,eAC7F9E,OAAA;YAAMyE,SAAS,EAAC,YAAY;YAAAC,QAAA,EAAC;UAAa;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC9C,CAAC,eACN9E,OAAA;UAAKyE,SAAS,EAAC,WAAW;UAAAC,QAAA,gBACxB1E,OAAA;YAAMyE,SAAS,EAAC,aAAa;YAAAC,QAAA,EAAE,IAAIyB,GAAG,CAAC9F,OAAO,CAAC4E,GAAG,CAAC5B,IAAI,IAAIA,IAAI,CAAClC,WAAW,CAAC,CAAC,CAACiF;UAAI;YAAAzB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC,eAC1F9E,OAAA;YAAMyE,SAAS,EAAC,YAAY;YAAAC,QAAA,EAAC;UAAa;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC9C,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAEN9E,OAAA;QAAKyE,SAAS,EAAC,kBAAkB;QAAAC,QAAA,gBAC/B1E,OAAA;UAAKyE,SAAS,EAAC,cAAc;UAAAC,QAAA,gBAC3B1E,OAAA;YAAOyE,SAAS,EAAC,eAAe;YAAAC,QAAA,gBAC9B1E,OAAA;cAAMyE,SAAS,EAAC,YAAY;cAAAC,QAAA,EAAC;YAAE;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,kBAExC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC,eACR9E,OAAA;YACEqG,KAAK,EAAE5F,MAAO;YACd6F,QAAQ,EAAG5D,CAAC,IAAKhC,SAAS,CAACgC,CAAC,CAAC6D,MAAM,CAACF,KAAK,CAAE;YAC3C5B,SAAS,EAAC,gBAAgB;YAAAC,QAAA,gBAE1B1E,OAAA;cAAQqG,KAAK,EAAC,KAAK;cAAA3B,QAAA,EAAC;YAAW;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eACxC9E,OAAA;cAAQqG,KAAK,EAAC,MAAM;cAAA3B,QAAA,EAAC;YAAa;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eAC3C9E,OAAA;cAAQqG,KAAK,EAAC,QAAQ;cAAA3B,QAAA,EAAC;YAAe;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eAC/C9E,OAAA;cAAQqG,KAAK,EAAC,IAAI;cAAA3B,QAAA,EAAC;YAAiB;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACvC,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC,eAEN9E,OAAA;UAAKyE,SAAS,EAAC,cAAc;UAAAC,QAAA,gBAC3B1E,OAAA;YAAOyE,SAAS,EAAC,eAAe;YAAAC,QAAA,gBAC9B1E,OAAA;cAAMyE,SAAS,EAAC,YAAY;cAAAC,QAAA,EAAC;YAAE;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,WAExC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC,eACR9E,OAAA;YACEqG,KAAK,EAAE1F,MAAO;YACd2F,QAAQ,EAAG5D,CAAC,IAAK9B,SAAS,CAAC8B,CAAC,CAAC6D,MAAM,CAACF,KAAK,CAAE;YAC3C5B,SAAS,EAAC,gBAAgB;YAAAC,QAAA,gBAE1B1E,OAAA;cAAQqG,KAAK,EAAC,QAAQ;cAAA3B,QAAA,EAAC;YAAY;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eAC5C9E,OAAA;cAAQqG,KAAK,EAAC,QAAQ;cAAA3B,QAAA,EAAC;YAAY;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eAC5C9E,OAAA;cAAQqG,KAAK,EAAC,SAAS;cAAA3B,QAAA,EAAC;YAAa;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eAC9C9E,OAAA;cAAQqG,KAAK,EAAC,UAAU;cAAA3B,QAAA,EAAC;YAAc;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC1C,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,EAELO,eAAe,CAAChE,MAAM,KAAK,CAAC,gBAC3BrB,OAAA;MAAKyE,SAAS,EAAC,eAAe;MAAAC,QAAA,gBAC5B1E,OAAA;QAAKyE,SAAS,EAAC,oBAAoB;QAAAC,QAAA,eACjC1E,OAAA;UAAMyE,SAAS,EAAC,aAAa;UAAAC,QAAA,EAAC;QAAE;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACpC,CAAC,eACN9E,OAAA;QAAIyE,SAAS,EAAC,aAAa;QAAAC,QAAA,EAAC;MAAc;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eAC/C9E,OAAA;QAAGyE,SAAS,EAAC,gBAAgB;QAAAC,QAAA,EAAC;MAE9B;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAG,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACD,CAAC,gBAEN9E,OAAA;MAAKyE,SAAS,EAAC,cAAc;MAAAC,QAAA,EAC1BW,eAAe,CAACJ,GAAG,CAAE5B,IAAI,iBACxBrD,OAAA;QAAmByE,SAAS,EAAC,cAAc;QAAAC,QAAA,gBACzC1E,OAAA;UAAKyE,SAAS,EAAC,aAAa;UAAAC,QAAA,gBAC1B1E,OAAA;YAAKyE,SAAS,EAAC,WAAW;YAAAC,QAAA,gBACxB1E,OAAA;cAAMyE,SAAS,EAAC,WAAW;cAAAC,QAAA,EAAET,kBAAkB,CAACZ,IAAI,CAAClC,WAAW;YAAC;cAAAwD,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eACzE9E,OAAA;cAAMyE,SAAS,EAAC,WAAW;cAAAC,QAAA,EAAErB,IAAI,CAAClC;YAAW;cAAAwD,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAClD,CAAC,eACN9E,OAAA;YAAKyE,SAAS,EAAC,cAAc;YAAAC,QAAA,eAC3B1E,OAAA;cACEyE,SAAS,EAAC,qBAAqB;cAC/BM,OAAO,EAAEA,CAAA,KAAMX,eAAe,CAACf,IAAI,CAAC9B,OAAO,CAAE;cAC7CiF,KAAK,EAAC,cAAc;cAAA9B,QAAA,EACrB;YAED;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAEN9E,OAAA;UAAKyE,SAAS,EAAC,cAAc;UAAAC,QAAA,gBAC3B1E,OAAA;YAAIyE,SAAS,EAAC,eAAe;YAAAC,QAAA,EAAErB,IAAI,CAAC/B;UAAK;YAAAqD,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC,eAC/C9E,OAAA;YAAGyE,SAAS,EAAC,iBAAiB;YAAAC,QAAA,EAC3BrB,IAAI,CAAC9B,OAAO,CAACF,MAAM,GAAG,GAAG,GACtB,GAAGgC,IAAI,CAAC9B,OAAO,CAAC2D,SAAS,CAAC,CAAC,EAAE,GAAG,CAAC,KAAK,GACtC7B,IAAI,CAAC9B;UAAO;YAAAoD,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAEf,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACD,CAAC,eAEN9E,OAAA;UAAKyE,SAAS,EAAC,WAAW;UAAAC,QAAA,gBACxB1E,OAAA;YAAKyE,SAAS,EAAC,WAAW;YAAAC,QAAA,gBACxB1E,OAAA;cACEyE,SAAS,EAAC,UAAU;cACpBU,KAAK,EAAE;gBAAEC,eAAe,EAAEjB,YAAY,CAACd,IAAI,CAACjC,IAAI;cAAE,CAAE;cAAAsD,QAAA,EAEnDrB,IAAI,CAACjC;YAAI;cAAAuD,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACN,CAAC,eACP9E,OAAA;cAAMyE,SAAS,EAAC,YAAY;cAAAC,QAAA,GACzBrB,IAAI,CAAC5B,SAAS,EAAC,QAClB;YAAA;cAAAkD,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACJ,CAAC,eACN9E,OAAA;YAAKyE,SAAS,EAAC,WAAW;YAAAC,QAAA,EACvBjB,UAAU,CAACJ,IAAI,CAAC7B,SAAS;UAAC;YAAAmD,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACxB,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA,GA1CEzB,IAAI,CAACnC,EAAE;QAAAyD,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OA2CZ,CACN;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC,CACN;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACE,CAAC;AAEV,CAAC;AAAC1E,EAAA,CAvbIH,OAAO;AAAAwG,EAAA,GAAPxG,OAAO;AAybb,eAAeA,OAAO;AAAC,IAAAwG,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}