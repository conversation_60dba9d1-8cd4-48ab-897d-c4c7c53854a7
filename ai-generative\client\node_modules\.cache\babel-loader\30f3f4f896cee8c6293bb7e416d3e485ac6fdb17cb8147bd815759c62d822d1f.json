{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\vedika\\\\ai-generative\\\\client\\\\src\\\\components\\\\ContentTypeSelector.jsx\";\nimport React from 'react';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst ContentTypeSelector = ({\n  value,\n  onChange\n}) => /*#__PURE__*/_jsxDEV(\"div\", {\n  children: [/*#__PURE__*/_jsxDEV(\"label\", {\n    children: \"Content Type: \"\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 5,\n    columnNumber: 5\n  }, this), /*#__PURE__*/_jsxDEV(\"select\", {\n    value: value,\n    onChange: e => onChange && onChange(e.target.value),\n    children: [/*#__PURE__*/_jsxDEV(\"option\", {\n      value: \"blog\",\n      children: \"Blog\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 7,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n      value: \"ad\",\n      children: \"Ad\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 8,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n      value: \"social\",\n      children: \"Social Post\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 9,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 6,\n    columnNumber: 5\n  }, this)]\n}, void 0, true, {\n  fileName: _jsxFileName,\n  lineNumber: 4,\n  columnNumber: 3\n}, this);\n_c = ContentTypeSelector;\nexport default ContentTypeSelector;\nvar _c;\n$RefreshReg$(_c, \"ContentTypeSelector\");", "map": {"version": 3, "names": ["React", "jsxDEV", "_jsxDEV", "ContentTypeSelector", "value", "onChange", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "e", "target", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/vedika/ai-generative/client/src/components/ContentTypeSelector.jsx"], "sourcesContent": ["import React from 'react';\r\n\r\nconst ContentTypeSelector = ({ value, onChange }) => (\r\n  <div>\r\n    <label>Content Type: </label>\r\n    <select value={value} onChange={e => onChange && onChange(e.target.value)}>\r\n      <option value=\"blog\">Blog</option>\r\n      <option value=\"ad\">Ad</option>\r\n      <option value=\"social\">Social Post</option>\r\n    </select>\r\n  </div>\r\n);\r\n\r\nexport default ContentTypeSelector; "], "mappings": ";AAAA,OAAOA,KAAK,MAAM,OAAO;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE1B,MAAMC,mBAAmB,GAAGA,CAAC;EAAEC,KAAK;EAAEC;AAAS,CAAC,kBAC9CH,OAAA;EAAAI,QAAA,gBACEJ,OAAA;IAAAI,QAAA,EAAO;EAAc;IAAAC,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAAO,CAAC,eAC7BR,OAAA;IAAQE,KAAK,EAAEA,KAAM;IAACC,QAAQ,EAAEM,CAAC,IAAIN,QAAQ,IAAIA,QAAQ,CAACM,CAAC,CAACC,MAAM,CAACR,KAAK,CAAE;IAAAE,QAAA,gBACxEJ,OAAA;MAAQE,KAAK,EAAC,MAAM;MAAAE,QAAA,EAAC;IAAI;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAQ,CAAC,eAClCR,OAAA;MAAQE,KAAK,EAAC,IAAI;MAAAE,QAAA,EAAC;IAAE;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAQ,CAAC,eAC9BR,OAAA;MAAQE,KAAK,EAAC,QAAQ;MAAAE,QAAA,EAAC;IAAW;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAQ,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACrC,CAAC;AAAA;EAAAH,QAAA,EAAAC,YAAA;EAAAC,UAAA;EAAAC,YAAA;AAAA,OACN,CACN;AAACG,EAAA,GATIV,mBAAmB;AAWzB,eAAeA,mBAAmB;AAAC,IAAAU,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}