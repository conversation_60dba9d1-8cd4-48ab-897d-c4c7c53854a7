.length-selector {
  margin-bottom: 1.5rem;
}

.selector-label {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-size: 1.1rem;
  font-weight: 600;
  color: #1e293b;
  margin-bottom: 1rem;
}

.label-icon {
  font-size: 1.3rem;
  animation: bounce 2s ease-in-out infinite;
}

.length-presets {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 0.75rem;
  margin-bottom: 1.5rem;
}

.length-preset {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 0.5rem;
  padding: 1rem;
  background: white;
  border: 2px solid #e2e8f0;
  border-radius: 12px;
  cursor: pointer;
  transition: all 0.3s ease;
  text-align: center;
  position: relative;
  overflow: hidden;
}

.length-preset::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(52, 211, 153, 0.1), transparent);
  transition: left 0.5s ease;
}

.length-preset:hover::before {
  left: 100%;
}

.length-preset:hover {
  border-color: #34d399;
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(52, 211, 153, 0.15);
}

.length-preset.active {
  border-color: #34d399;
  background: linear-gradient(135deg, #34d399 0%, #10b981 100%);
  color: white;
  transform: scale(1.05);
  box-shadow: 0 10px 30px rgba(52, 211, 153, 0.3);
}

.length-preset.active .preset-title,
.length-preset.active .preset-description {
  color: white;
}

.preset-icon {
  font-size: 2rem;
  animation: wiggle 3s ease-in-out infinite;
  filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.1));
}

.length-preset.active .preset-icon {
  animation: grow 2s ease-in-out infinite;
  filter: drop-shadow(0 0 10px rgba(255, 255, 255, 0.5));
}

.preset-content {
  flex: 1;
}

.preset-title {
  font-size: 1rem;
  font-weight: 600;
  color: #1e293b;
  margin-bottom: 0.25rem;
  transition: color 0.3s ease;
}

.preset-description {
  font-size: 0.8rem;
  color: #64748b;
  transition: color 0.3s ease;
}

/* Custom Length Section */
.custom-length {
  background: rgba(255, 255, 255, 0.8);
  border-radius: 12px;
  padding: 1.5rem;
  border: 2px solid #e2e8f0;
  position: relative;
  overflow: hidden;
}

.custom-length::before {
  content: '🎨';
  position: absolute;
  top: 10px;
  right: 10px;
  font-size: 1.2rem;
  opacity: 0.3;
  animation: twinkle 3s ease-in-out infinite;
}

.custom-label {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-size: 1rem;
  font-weight: 600;
  color: #1e293b;
  margin-bottom: 1rem;
}

.custom-icon {
  font-size: 1.2rem;
  animation: pulse 2s ease-in-out infinite;
}

.custom-input-container {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.length-slider {
  width: 100%;
  height: 8px;
  border-radius: 4px;
  background: #e2e8f0;
  outline: none;
  cursor: pointer;
  transition: all 0.3s ease;
}

.length-slider::-webkit-slider-thumb {
  appearance: none;
  width: 20px;
  height: 20px;
  border-radius: 50%;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  cursor: pointer;
  box-shadow: 0 2px 8px rgba(102, 126, 234, 0.3);
  transition: all 0.3s ease;
}

.length-slider::-webkit-slider-thumb:hover {
  transform: scale(1.2);
  box-shadow: 0 4px 15px rgba(102, 126, 234, 0.5);
}

.length-slider::-moz-range-thumb {
  width: 20px;
  height: 20px;
  border-radius: 50%;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  cursor: pointer;
  border: none;
  box-shadow: 0 2px 8px rgba(102, 126, 234, 0.3);
}

.length-display {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  justify-content: center;
}

.length-input {
  width: 80px;
  padding: 0.5rem;
  border: 2px solid #e2e8f0;
  border-radius: 8px;
  text-align: center;
  font-weight: 600;
  color: #1e293b;
  transition: border-color 0.3s ease;
}

.length-input:focus {
  outline: none;
  border-color: #667eea;
  box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

.length-unit {
  font-size: 0.9rem;
  color: #64748b;
  font-weight: 500;
}

/* Responsive design */
@media (max-width: 768px) {
  .length-presets {
    grid-template-columns: 1fr;
  }
  
  .length-preset {
    flex-direction: row;
    text-align: left;
    gap: 1rem;
  }
  
  .preset-icon {
    font-size: 2.5rem;
  }
}

/* Animation keyframes */
@keyframes wiggle {
  0%, 100% {
    transform: rotate(0deg);
  }
  25% {
    transform: rotate(3deg);
  }
  75% {
    transform: rotate(-3deg);
  }
}

@keyframes grow {
  0%, 100% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.1);
  }
}

@keyframes twinkle {
  0%, 100% {
    opacity: 0.3;
    transform: scale(0.8);
  }
  50% {
    opacity: 0.8;
    transform: scale(1.2);
  }
}

@keyframes pulse {
  0%, 100% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.1);
  }
}

@keyframes bounce {
  0%, 100% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-5px);
  }
}
