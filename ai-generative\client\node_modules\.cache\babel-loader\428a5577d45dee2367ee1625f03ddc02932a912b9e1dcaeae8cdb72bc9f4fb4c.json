{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\vedika\\\\ai-generative\\\\client\\\\src\\\\components\\\\Editor.jsx\";\nimport React from 'react';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst Editor = ({\n  value,\n  onChange,\n  onGenerate\n}) => /*#__PURE__*/_jsxDEV(\"div\", {\n  children: [/*#__PURE__*/_jsxDEV(\"textarea\", {\n    rows: \"10\",\n    cols: \"60\",\n    placeholder: \"Your generated content will appear here...\",\n    value: value,\n    onChange: e => onChange && onChange(e.target.value)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 5,\n    columnNumber: 5\n  }, this), /*#__PURE__*/_jsxDEV(\"br\", {}, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 12,\n    columnNumber: 5\n  }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n    onClick: onGenerate,\n    children: \"Generate\"\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 13,\n    columnNumber: 5\n  }, this)]\n}, void 0, true, {\n  fileName: _jsxFileName,\n  lineNumber: 4,\n  columnNumber: 3\n}, this);\n_c = Editor;\nexport default Editor;\nvar _c;\n$RefreshReg$(_c, \"Editor\");", "map": {"version": 3, "names": ["React", "jsxDEV", "_jsxDEV", "Editor", "value", "onChange", "onGenerate", "children", "rows", "cols", "placeholder", "e", "target", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "onClick", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/vedika/ai-generative/client/src/components/Editor.jsx"], "sourcesContent": ["import React from 'react';\r\n\r\nconst Editor = ({ value, onChange, onGenerate }) => (\r\n  <div>\r\n    <textarea\r\n      rows=\"10\"\r\n      cols=\"60\"\r\n      placeholder=\"Your generated content will appear here...\"\r\n      value={value}\r\n      onChange={e => onChange && onChange(e.target.value)}\r\n    />\r\n    <br />\r\n    <button onClick={onGenerate}>Generate</button>\r\n  </div>\r\n);\r\n\r\nexport default Editor; "], "mappings": ";AAAA,OAAOA,KAAK,MAAM,OAAO;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE1B,MAAMC,MAAM,GAAGA,CAAC;EAAEC,KAAK;EAAEC,QAAQ;EAAEC;AAAW,CAAC,kBAC7CJ,OAAA;EAAAK,QAAA,gBACEL,OAAA;IACEM,IAAI,EAAC,IAAI;IACTC,IAAI,EAAC,IAAI;IACTC,WAAW,EAAC,4CAA4C;IACxDN,KAAK,EAAEA,KAAM;IACbC,QAAQ,EAAEM,CAAC,IAAIN,QAAQ,IAAIA,QAAQ,CAACM,CAAC,CAACC,MAAM,CAACR,KAAK;EAAE;IAAAS,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACrD,CAAC,eACFd,OAAA;IAAAW,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAAK,CAAC,eACNd,OAAA;IAAQe,OAAO,EAAEX,UAAW;IAAAC,QAAA,EAAC;EAAQ;IAAAM,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAAQ,CAAC;AAAA;EAAAH,QAAA,EAAAC,YAAA;EAAAC,UAAA;EAAAC,YAAA;AAAA,OAC3C,CACN;AAACE,EAAA,GAZIf,MAAM;AAcZ,eAAeA,MAAM;AAAC,IAAAe,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}