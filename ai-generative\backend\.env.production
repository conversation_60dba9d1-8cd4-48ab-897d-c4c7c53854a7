# Production Environment Variables
# Copy this file to .env and update with your actual values

# OpenAI Configuration
OPENAI_API_KEY=your_production_openai_api_key_here

# JWT Configuration
JWT_SECRET=your_very_strong_jwt_secret_here_minimum_32_characters

# Server Configuration
NODE_ENV=production
PORT=5000

# CORS Configuration (update with your frontend domain)
FRONTEND_URL=https://your-frontend-domain.com

# Rate Limiting (requests per minute per IP)
RATE_LIMIT=100

# Security Headers
HELMET_ENABLED=true

# Logging
LOG_LEVEL=info
LOG_FILE=./logs/app.log

# Database Configuration (if using persistent storage)
# DATABASE_URL=your_database_connection_string_here

# Redis Configuration (if using for sessions/caching)
# REDIS_URL=your_redis_connection_string_here

# Monitoring (if using services like Sentry)
# SENTRY_DSN=your_sentry_dsn_here

# Email Configuration (if implementing email features)
# SMTP_HOST=your_smtp_host
# SMTP_PORT=587
# SMTP_USER=your_smtp_username
# SMTP_PASS=your_smtp_password
