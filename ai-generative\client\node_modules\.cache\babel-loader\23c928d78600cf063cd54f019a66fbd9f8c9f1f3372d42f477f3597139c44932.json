{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\vedika\\\\ai-generative\\\\client\\\\src\\\\components\\\\AuthForm.jsx\",\n  _s = $RefreshSig$();\nimport { useState } from 'react';\nimport './AuthForm.css';\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst AuthForm = ({\n  user,\n  setUser\n}) => {\n  _s();\n  const [isLogin, setIsLogin] = useState(true);\n  const [formData, setFormData] = useState({\n    username: '',\n    email: '',\n    password: '',\n    confirmPassword: ''\n  });\n  const [loading, setLoading] = useState(false);\n  const [message, setMessage] = useState('');\n  const [messageType, setMessageType] = useState(''); // 'success' or 'error'\n\n  // Get API base URL\n  const getApiUrl = () => {\n    return process.env.NODE_ENV === 'production' ? '/api' : 'http://localhost:5000/api';\n  };\n  const handleInputChange = e => {\n    setFormData({\n      ...formData,\n      [e.target.name]: e.target.value\n    });\n  };\n  const showMessage = (text, type) => {\n    setMessage(text);\n    setMessageType(type);\n    setTimeout(() => {\n      setMessage('');\n      setMessageType('');\n    }, 5000);\n  };\n  const handleSubmit = async e => {\n    e.preventDefault();\n    setLoading(true);\n    try {\n      if (!isLogin) {\n        // Registration validation\n        if (formData.password !== formData.confirmPassword) {\n          showMessage('Passwords do not match!', 'error');\n          setLoading(false);\n          return;\n        }\n        if (formData.password.length < 6) {\n          showMessage('Password must be at least 6 characters long!', 'error');\n          setLoading(false);\n          return;\n        }\n      }\n      const endpoint = isLogin ? '/auth/login' : '/auth/register';\n      const payload = isLogin ? {\n        email: formData.email,\n        password: formData.password\n      } : {\n        username: formData.username,\n        email: formData.email,\n        password: formData.password\n      };\n      const response = await fetch(`${getApiUrl()}${endpoint}`, {\n        method: 'POST',\n        headers: {\n          'Content-Type': 'application/json'\n        },\n        body: JSON.stringify(payload)\n      });\n      const data = await response.json();\n      if (response.ok) {\n        // Success\n        localStorage.setItem('authToken', data.token);\n        localStorage.setItem('userData', JSON.stringify(data.user));\n        setUser(data.user);\n        showMessage(isLogin ? `Welcome back, ${data.user.username}! 🎉` : `Account created successfully! Welcome, ${data.user.username}! 🎉`, 'success');\n\n        // Clear form\n        setFormData({\n          username: '',\n          email: '',\n          password: '',\n          confirmPassword: ''\n        });\n      } else {\n        // Error\n        showMessage(data.error || 'Something went wrong!', 'error');\n      }\n    } catch (error) {\n      showMessage('Unable to connect to the server. Please try again later.', 'error');\n    } finally {\n      setLoading(false);\n    }\n  };\n  const toggleMode = () => {\n    setIsLogin(!isLogin);\n    setMessage('');\n    setFormData({\n      username: '',\n      email: '',\n      password: '',\n      confirmPassword: ''\n    });\n  };\n  if (user) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"auth-container\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"user-profile\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"profile-header\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"profile-avatar\",\n            children: /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"avatar-emoji\",\n              children: \"\\uD83D\\uDC64\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 124,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 123,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"profile-info\",\n            children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n              className: \"profile-name\",\n              children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"welcome-emoji\",\n                children: \"\\uD83D\\uDC4B\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 128,\n                columnNumber: 17\n              }, this), \"Welcome, \", user.username, \"!\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 127,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"profile-email\",\n              children: user.email\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 131,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 126,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 122,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"profile-stats\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"stat-card\",\n            children: [/*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"stat-icon\",\n              children: \"\\uD83D\\uDCDD\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 137,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"stat-content\",\n              children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n                children: \"Content Generated\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 139,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                children: \"Ready to create!\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 140,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 138,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 136,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"stat-card\",\n            children: [/*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"stat-icon\",\n              children: \"\\u2B50\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 144,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"stat-content\",\n              children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n                children: \"Account Status\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 146,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                children: \"Active Member\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 147,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 145,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 143,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 135,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"profile-actions\",\n          children: [/*#__PURE__*/_jsxDEV(\"button\", {\n            className: \"profile-btn generate-btn\",\n            children: [/*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"btn-icon\",\n              children: \"\\uD83D\\uDE80\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 154,\n              columnNumber: 15\n            }, this), \"Start Creating\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 153,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            className: \"profile-btn history-btn\",\n            children: [/*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"btn-icon\",\n              children: \"\\uD83D\\uDCDA\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 158,\n              columnNumber: 15\n            }, this), \"View History\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 157,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 152,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 121,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 120,\n      columnNumber: 7\n    }, this);\n  }\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"auth-container\",\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"auth-form-wrapper\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"auth-header\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"auth-illustration\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"magic-circle\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"orbit orbit-1\",\n              children: /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"orbit-emoji\",\n                children: \"\\uD83C\\uDFA8\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 174,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 173,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"orbit orbit-2\",\n              children: /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"orbit-emoji\",\n                children: \"\\u2728\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 177,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 176,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"orbit orbit-3\",\n              children: /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"orbit-emoji\",\n                children: \"\\uD83D\\uDE80\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 180,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 179,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"center-logo\",\n              children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"logo-main\",\n                children: \"\\uD83E\\uDD16\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 183,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"energy-rings\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"ring ring-1\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 185,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"ring ring-2\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 186,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"ring ring-3\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 187,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 184,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 182,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 172,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 171,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"title-container\",\n          children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n            className: \"auth-title\",\n            children: [/*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"title-text\",\n              children: isLogin ? 'Welcome Back, Creator!' : 'Unleash Your Creative Power!'\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 195,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"title-effects\",\n              children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"title-decoration explosive\",\n                children: isLogin ? '⚡' : '💥'\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 199,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"sparkle-trail\",\n                children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"sparkle\",\n                  children: \"\\u2728\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 203,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"sparkle\",\n                  children: \"\\u2B50\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 204,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"sparkle\",\n                  children: \"\\uD83D\\uDCAB\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 205,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 202,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 198,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 194,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"subtitle-container\",\n            children: [/*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"auth-subtitle\",\n              children: isLogin ? '🎯 Ready to create mind-blowing content with AI superpowers?' : '🌟 Join thousands of creators transforming ideas into reality!'\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 211,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"impact-stats\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"stat-bubble\",\n                children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"stat-number\",\n                  children: \"10K+\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 219,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"stat-label\",\n                  children: \"Creators\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 220,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 218,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"stat-bubble\",\n                children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"stat-number\",\n                  children: \"1M+\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 223,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"stat-label\",\n                  children: \"Content\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 224,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 222,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"stat-bubble\",\n                children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"stat-number\",\n                  children: \"\\u221E\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 227,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"stat-label\",\n                  children: \"Possibilities\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 228,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 226,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 217,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 210,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 193,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 170,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"form\", {\n        onSubmit: handleSubmit,\n        className: \"auth-form\",\n        children: [!isLogin && /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"form-group\",\n          children: [/*#__PURE__*/_jsxDEV(\"label\", {\n            className: \"form-label\",\n            children: [/*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"label-icon\",\n              children: \"\\uD83D\\uDC64\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 239,\n              columnNumber: 17\n            }, this), \"Username\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 238,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n            type: \"text\",\n            name: \"username\",\n            value: formData.username,\n            onChange: handleInputChange,\n            placeholder: \"Choose a creative username\",\n            className: \"form-input\",\n            required: !isLogin,\n            disabled: loading\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 242,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 237,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"form-group\",\n          children: [/*#__PURE__*/_jsxDEV(\"label\", {\n            className: \"form-label\",\n            children: [/*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"label-icon\",\n              children: \"\\uD83D\\uDCE7\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 257,\n              columnNumber: 15\n            }, this), \"Email\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 256,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n            type: \"email\",\n            name: \"email\",\n            value: formData.email,\n            onChange: handleInputChange,\n            placeholder: \"<EMAIL>\",\n            className: \"form-input\",\n            required: true,\n            disabled: loading\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 260,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 255,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"form-group\",\n          children: [/*#__PURE__*/_jsxDEV(\"label\", {\n            className: \"form-label\",\n            children: [/*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"label-icon\",\n              children: \"\\uD83D\\uDD12\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 274,\n              columnNumber: 15\n            }, this), \"Password\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 273,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n            type: \"password\",\n            name: \"password\",\n            value: formData.password,\n            onChange: handleInputChange,\n            placeholder: isLogin ? \"Enter your password\" : \"Create a secure password (6+ characters)\",\n            className: \"form-input\",\n            required: true,\n            disabled: loading,\n            minLength: isLogin ? undefined : 6\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 277,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 272,\n          columnNumber: 11\n        }, this), !isLogin && /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"form-group\",\n          children: [/*#__PURE__*/_jsxDEV(\"label\", {\n            className: \"form-label\",\n            children: [/*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"label-icon\",\n              children: \"\\uD83D\\uDD10\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 293,\n              columnNumber: 17\n            }, this), \"Confirm Password\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 292,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n            type: \"password\",\n            name: \"confirmPassword\",\n            value: formData.confirmPassword,\n            onChange: handleInputChange,\n            placeholder: \"Confirm your password\",\n            className: \"form-input\",\n            required: !isLogin,\n            disabled: loading\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 296,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 291,\n          columnNumber: 13\n        }, this), message && /*#__PURE__*/_jsxDEV(\"div\", {\n          className: `message ${messageType}`,\n          children: [/*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"message-icon\",\n            children: messageType === 'success' ? '✅' : '❌'\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 311,\n            columnNumber: 15\n          }, this), message]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 310,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          type: \"submit\",\n          className: \"auth-submit-btn mega-button\",\n          disabled: loading,\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"button-bg-effect\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 323,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"button-content\",\n            children: loading ? /*#__PURE__*/_jsxDEV(_Fragment, {\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"loading-animation\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"loading-orb\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 328,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"loading-particles\",\n                  children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"particle\",\n                    children: \"\\u2728\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 330,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"particle\",\n                    children: \"\\u2B50\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 331,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"particle\",\n                    children: \"\\uD83D\\uDCAB\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 332,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 329,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 327,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"loading-text\",\n                children: isLogin ? '🔮 Authenticating Magic...' : '🚀 Launching Your Journey...'\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 335,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true) : /*#__PURE__*/_jsxDEV(_Fragment, {\n              children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"btn-icon mega-icon\",\n                children: isLogin ? '⚡' : '�'\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 341,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"btn-text\",\n                children: isLogin ? 'UNLEASH CREATIVITY' : 'START MY JOURNEY'\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 344,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"button-particles\",\n                children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"btn-particle\",\n                  children: \"\\u2728\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 348,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"btn-particle\",\n                  children: \"\\uD83D\\uDCA5\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 349,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"btn-particle\",\n                  children: \"\\uD83D\\uDE80\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 350,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 347,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 324,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 318,\n          columnNumber: 11\n        }, this), !isLogin && /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"power-features\",\n          children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n            className: \"features-title\",\n            children: [/*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"features-icon\",\n              children: \"\\u26A1\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 360,\n              columnNumber: 17\n            }, this), \"Unlock These Superpowers\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 359,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"features-grid\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"feature-item\",\n              children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"feature-emoji\",\n                children: \"\\uD83E\\uDDE0\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 365,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"feature-text\",\n                children: \"AI Brain Power\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 366,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 364,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"feature-item\",\n              children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"feature-emoji\",\n                children: \"\\u26A1\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 369,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"feature-text\",\n                children: \"Lightning Speed\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 370,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 368,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"feature-item\",\n              children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"feature-emoji\",\n                children: \"\\uD83C\\uDFA8\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 373,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"feature-text\",\n                children: \"Unlimited Creativity\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 374,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 372,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"feature-item\",\n              children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"feature-emoji\",\n                children: \"\\uD83D\\uDE80\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 377,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"feature-text\",\n                children: \"Instant Results\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 378,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 376,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 363,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 358,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 235,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"auth-footer\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"switch-container\",\n          children: [/*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"auth-switch-text\",\n            children: isLogin ? \"🌟 Ready to join the revolution?\" : \"🚀 Already part of the magic?\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 387,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            type: \"button\",\n            onClick: toggleMode,\n            className: \"auth-switch-btn impact-switch\",\n            disabled: loading,\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"switch-bg-effect\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 396,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"switch-content\",\n              children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"switch-icon explosive\",\n                children: isLogin ? '💥' : '⚡'\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 398,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"switch-text\",\n                children: isLogin ? 'JOIN THE REVOLUTION' : 'ENTER THE MATRIX'\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 401,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 397,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 390,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 386,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"trust-indicators\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"trust-item\",\n            children: [/*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"trust-icon\",\n              children: \"\\uD83D\\uDD12\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 410,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"trust-text\",\n              children: \"Bank-Level Security\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 411,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 409,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"trust-item\",\n            children: [/*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"trust-icon\",\n              children: \"\\u26A1\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 414,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"trust-text\",\n              children: \"Instant Access\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 415,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 413,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"trust-item\",\n            children: [/*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"trust-icon\",\n              children: \"\\uD83C\\uDF0D\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 418,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"trust-text\",\n              children: \"Global Community\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 419,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 417,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 408,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 385,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 169,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 168,\n    columnNumber: 5\n  }, this);\n};\n_s(AuthForm, \"FzuKKKgrko/pj98k0+aL3Zo/XfA=\");\n_c = AuthForm;\nexport default AuthForm;\nvar _c;\n$RefreshReg$(_c, \"AuthForm\");", "map": {"version": 3, "names": ["useState", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "AuthForm", "user", "setUser", "_s", "is<PERSON>ogin", "setIsLogin", "formData", "setFormData", "username", "email", "password", "confirmPassword", "loading", "setLoading", "message", "setMessage", "messageType", "setMessageType", "getApiUrl", "process", "env", "NODE_ENV", "handleInputChange", "e", "target", "name", "value", "showMessage", "text", "type", "setTimeout", "handleSubmit", "preventDefault", "length", "endpoint", "payload", "response", "fetch", "method", "headers", "body", "JSON", "stringify", "data", "json", "ok", "localStorage", "setItem", "token", "error", "toggleMode", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "onSubmit", "onChange", "placeholder", "required", "disabled", "<PERSON><PERSON><PERSON><PERSON>", "undefined", "onClick", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/vedika/ai-generative/client/src/components/AuthForm.jsx"], "sourcesContent": ["import { useState } from 'react';\r\nimport './AuthForm.css';\r\n\r\nconst AuthForm = ({ user, setUser }) => {\r\n  const [isLogin, setIsLogin] = useState(true);\r\n  const [formData, setFormData] = useState({\r\n    username: '',\r\n    email: '',\r\n    password: '',\r\n    confirmPassword: ''\r\n  });\r\n  const [loading, setLoading] = useState(false);\r\n  const [message, setMessage] = useState('');\r\n  const [messageType, setMessageType] = useState(''); // 'success' or 'error'\r\n\r\n  // Get API base URL\r\n  const getApiUrl = () => {\r\n    return process.env.NODE_ENV === 'production'\r\n      ? '/api'\r\n      : 'http://localhost:5000/api';\r\n  };\r\n\r\n  const handleInputChange = (e) => {\r\n    setFormData({\r\n      ...formData,\r\n      [e.target.name]: e.target.value\r\n    });\r\n  };\r\n\r\n  const showMessage = (text, type) => {\r\n    setMessage(text);\r\n    setMessageType(type);\r\n    setTimeout(() => {\r\n      setMessage('');\r\n      setMessageType('');\r\n    }, 5000);\r\n  };\r\n\r\n  const handleSubmit = async (e) => {\r\n    e.preventDefault();\r\n    setLoading(true);\r\n\r\n    try {\r\n      if (!isLogin) {\r\n        // Registration validation\r\n        if (formData.password !== formData.confirmPassword) {\r\n          showMessage('Passwords do not match!', 'error');\r\n          setLoading(false);\r\n          return;\r\n        }\r\n        if (formData.password.length < 6) {\r\n          showMessage('Password must be at least 6 characters long!', 'error');\r\n          setLoading(false);\r\n          return;\r\n        }\r\n      }\r\n\r\n      const endpoint = isLogin ? '/auth/login' : '/auth/register';\r\n      const payload = isLogin\r\n        ? { email: formData.email, password: formData.password }\r\n        : {\r\n            username: formData.username,\r\n            email: formData.email,\r\n            password: formData.password\r\n          };\r\n\r\n      const response = await fetch(`${getApiUrl()}${endpoint}`, {\r\n        method: 'POST',\r\n        headers: {\r\n          'Content-Type': 'application/json',\r\n        },\r\n        body: JSON.stringify(payload),\r\n      });\r\n\r\n      const data = await response.json();\r\n\r\n      if (response.ok) {\r\n        // Success\r\n        localStorage.setItem('authToken', data.token);\r\n        localStorage.setItem('userData', JSON.stringify(data.user));\r\n        setUser(data.user);\r\n        showMessage(\r\n          isLogin\r\n            ? `Welcome back, ${data.user.username}! 🎉`\r\n            : `Account created successfully! Welcome, ${data.user.username}! 🎉`,\r\n          'success'\r\n        );\r\n\r\n        // Clear form\r\n        setFormData({\r\n          username: '',\r\n          email: '',\r\n          password: '',\r\n          confirmPassword: ''\r\n        });\r\n      } else {\r\n        // Error\r\n        showMessage(data.error || 'Something went wrong!', 'error');\r\n      }\r\n    } catch (error) {\r\n      showMessage('Unable to connect to the server. Please try again later.', 'error');\r\n    } finally {\r\n      setLoading(false);\r\n    }\r\n  };\r\n\r\n  const toggleMode = () => {\r\n    setIsLogin(!isLogin);\r\n    setMessage('');\r\n    setFormData({\r\n      username: '',\r\n      email: '',\r\n      password: '',\r\n      confirmPassword: ''\r\n    });\r\n  };\r\n\r\n  if (user) {\r\n    return (\r\n      <div className=\"auth-container\">\r\n        <div className=\"user-profile\">\r\n          <div className=\"profile-header\">\r\n            <div className=\"profile-avatar\">\r\n              <span className=\"avatar-emoji\">👤</span>\r\n            </div>\r\n            <div className=\"profile-info\">\r\n              <h3 className=\"profile-name\">\r\n                <span className=\"welcome-emoji\">👋</span>\r\n                Welcome, {user.username}!\r\n              </h3>\r\n              <p className=\"profile-email\">{user.email}</p>\r\n            </div>\r\n          </div>\r\n\r\n          <div className=\"profile-stats\">\r\n            <div className=\"stat-card\">\r\n              <span className=\"stat-icon\">📝</span>\r\n              <div className=\"stat-content\">\r\n                <h4>Content Generated</h4>\r\n                <p>Ready to create!</p>\r\n              </div>\r\n            </div>\r\n            <div className=\"stat-card\">\r\n              <span className=\"stat-icon\">⭐</span>\r\n              <div className=\"stat-content\">\r\n                <h4>Account Status</h4>\r\n                <p>Active Member</p>\r\n              </div>\r\n            </div>\r\n          </div>\r\n\r\n          <div className=\"profile-actions\">\r\n            <button className=\"profile-btn generate-btn\">\r\n              <span className=\"btn-icon\">🚀</span>\r\n              Start Creating\r\n            </button>\r\n            <button className=\"profile-btn history-btn\">\r\n              <span className=\"btn-icon\">📚</span>\r\n              View History\r\n            </button>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    );\r\n  }\r\n\r\n  return (\r\n    <div className=\"auth-container\">\r\n      <div className=\"auth-form-wrapper\">\r\n        <div className=\"auth-header\">\r\n          <div className=\"auth-illustration\">\r\n            <div className=\"magic-circle\">\r\n              <div className=\"orbit orbit-1\">\r\n                <span className=\"orbit-emoji\">🎨</span>\r\n              </div>\r\n              <div className=\"orbit orbit-2\">\r\n                <span className=\"orbit-emoji\">✨</span>\r\n              </div>\r\n              <div className=\"orbit orbit-3\">\r\n                <span className=\"orbit-emoji\">🚀</span>\r\n              </div>\r\n              <div className=\"center-logo\">\r\n                <span className=\"logo-main\">🤖</span>\r\n                <div className=\"energy-rings\">\r\n                  <div className=\"ring ring-1\"></div>\r\n                  <div className=\"ring ring-2\"></div>\r\n                  <div className=\"ring ring-3\"></div>\r\n                </div>\r\n              </div>\r\n            </div>\r\n          </div>\r\n\r\n          <div className=\"title-container\">\r\n            <h2 className=\"auth-title\">\r\n              <span className=\"title-text\">\r\n                {isLogin ? 'Welcome Back, Creator!' : 'Unleash Your Creative Power!'}\r\n              </span>\r\n              <div className=\"title-effects\">\r\n                <span className=\"title-decoration explosive\">\r\n                  {isLogin ? '⚡' : '💥'}\r\n                </span>\r\n                <div className=\"sparkle-trail\">\r\n                  <span className=\"sparkle\">✨</span>\r\n                  <span className=\"sparkle\">⭐</span>\r\n                  <span className=\"sparkle\">💫</span>\r\n                </div>\r\n              </div>\r\n            </h2>\r\n\r\n            <div className=\"subtitle-container\">\r\n              <p className=\"auth-subtitle\">\r\n                {isLogin\r\n                  ? '🎯 Ready to create mind-blowing content with AI superpowers?'\r\n                  : '🌟 Join thousands of creators transforming ideas into reality!'\r\n                }\r\n              </p>\r\n              <div className=\"impact-stats\">\r\n                <div className=\"stat-bubble\">\r\n                  <span className=\"stat-number\">10K+</span>\r\n                  <span className=\"stat-label\">Creators</span>\r\n                </div>\r\n                <div className=\"stat-bubble\">\r\n                  <span className=\"stat-number\">1M+</span>\r\n                  <span className=\"stat-label\">Content</span>\r\n                </div>\r\n                <div className=\"stat-bubble\">\r\n                  <span className=\"stat-number\">∞</span>\r\n                  <span className=\"stat-label\">Possibilities</span>\r\n                </div>\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </div>\r\n\r\n        <form onSubmit={handleSubmit} className=\"auth-form\">\r\n          {!isLogin && (\r\n            <div className=\"form-group\">\r\n              <label className=\"form-label\">\r\n                <span className=\"label-icon\">👤</span>\r\n                Username\r\n              </label>\r\n              <input\r\n                type=\"text\"\r\n                name=\"username\"\r\n                value={formData.username}\r\n                onChange={handleInputChange}\r\n                placeholder=\"Choose a creative username\"\r\n                className=\"form-input\"\r\n                required={!isLogin}\r\n                disabled={loading}\r\n              />\r\n            </div>\r\n          )}\r\n\r\n          <div className=\"form-group\">\r\n            <label className=\"form-label\">\r\n              <span className=\"label-icon\">📧</span>\r\n              Email\r\n            </label>\r\n            <input\r\n              type=\"email\"\r\n              name=\"email\"\r\n              value={formData.email}\r\n              onChange={handleInputChange}\r\n              placeholder=\"<EMAIL>\"\r\n              className=\"form-input\"\r\n              required\r\n              disabled={loading}\r\n            />\r\n          </div>\r\n\r\n          <div className=\"form-group\">\r\n            <label className=\"form-label\">\r\n              <span className=\"label-icon\">🔒</span>\r\n              Password\r\n            </label>\r\n            <input\r\n              type=\"password\"\r\n              name=\"password\"\r\n              value={formData.password}\r\n              onChange={handleInputChange}\r\n              placeholder={isLogin ? \"Enter your password\" : \"Create a secure password (6+ characters)\"}\r\n              className=\"form-input\"\r\n              required\r\n              disabled={loading}\r\n              minLength={isLogin ? undefined : 6}\r\n            />\r\n          </div>\r\n\r\n          {!isLogin && (\r\n            <div className=\"form-group\">\r\n              <label className=\"form-label\">\r\n                <span className=\"label-icon\">🔐</span>\r\n                Confirm Password\r\n              </label>\r\n              <input\r\n                type=\"password\"\r\n                name=\"confirmPassword\"\r\n                value={formData.confirmPassword}\r\n                onChange={handleInputChange}\r\n                placeholder=\"Confirm your password\"\r\n                className=\"form-input\"\r\n                required={!isLogin}\r\n                disabled={loading}\r\n              />\r\n            </div>\r\n          )}\r\n\r\n          {message && (\r\n            <div className={`message ${messageType}`}>\r\n              <span className=\"message-icon\">\r\n                {messageType === 'success' ? '✅' : '❌'}\r\n              </span>\r\n              {message}\r\n            </div>\r\n          )}\r\n\r\n          <button\r\n            type=\"submit\"\r\n            className=\"auth-submit-btn mega-button\"\r\n            disabled={loading}\r\n          >\r\n            <div className=\"button-bg-effect\"></div>\r\n            <div className=\"button-content\">\r\n              {loading ? (\r\n                <>\r\n                  <div className=\"loading-animation\">\r\n                    <div className=\"loading-orb\"></div>\r\n                    <div className=\"loading-particles\">\r\n                      <span className=\"particle\">✨</span>\r\n                      <span className=\"particle\">⭐</span>\r\n                      <span className=\"particle\">💫</span>\r\n                    </div>\r\n                  </div>\r\n                  <span className=\"loading-text\">\r\n                    {isLogin ? '🔮 Authenticating Magic...' : '🚀 Launching Your Journey...'}\r\n                  </span>\r\n                </>\r\n              ) : (\r\n                <>\r\n                  <span className=\"btn-icon mega-icon\">\r\n                    {isLogin ? '⚡' : '�'}\r\n                  </span>\r\n                  <span className=\"btn-text\">\r\n                    {isLogin ? 'UNLEASH CREATIVITY' : 'START MY JOURNEY'}\r\n                  </span>\r\n                  <div className=\"button-particles\">\r\n                    <span className=\"btn-particle\">✨</span>\r\n                    <span className=\"btn-particle\">💥</span>\r\n                    <span className=\"btn-particle\">🚀</span>\r\n                  </div>\r\n                </>\r\n              )}\r\n            </div>\r\n          </button>\r\n\r\n          {!isLogin && (\r\n            <div className=\"power-features\">\r\n              <h4 className=\"features-title\">\r\n                <span className=\"features-icon\">⚡</span>\r\n                Unlock These Superpowers\r\n              </h4>\r\n              <div className=\"features-grid\">\r\n                <div className=\"feature-item\">\r\n                  <span className=\"feature-emoji\">🧠</span>\r\n                  <span className=\"feature-text\">AI Brain Power</span>\r\n                </div>\r\n                <div className=\"feature-item\">\r\n                  <span className=\"feature-emoji\">⚡</span>\r\n                  <span className=\"feature-text\">Lightning Speed</span>\r\n                </div>\r\n                <div className=\"feature-item\">\r\n                  <span className=\"feature-emoji\">🎨</span>\r\n                  <span className=\"feature-text\">Unlimited Creativity</span>\r\n                </div>\r\n                <div className=\"feature-item\">\r\n                  <span className=\"feature-emoji\">🚀</span>\r\n                  <span className=\"feature-text\">Instant Results</span>\r\n                </div>\r\n              </div>\r\n            </div>\r\n          )}\r\n        </form>\r\n\r\n        <div className=\"auth-footer\">\r\n          <div className=\"switch-container\">\r\n            <p className=\"auth-switch-text\">\r\n              {isLogin ? \"🌟 Ready to join the revolution?\" : \"🚀 Already part of the magic?\"}\r\n            </p>\r\n            <button\r\n              type=\"button\"\r\n              onClick={toggleMode}\r\n              className=\"auth-switch-btn impact-switch\"\r\n              disabled={loading}\r\n            >\r\n              <div className=\"switch-bg-effect\"></div>\r\n              <span className=\"switch-content\">\r\n                <span className=\"switch-icon explosive\">\r\n                  {isLogin ? '💥' : '⚡'}\r\n                </span>\r\n                <span className=\"switch-text\">\r\n                  {isLogin ? 'JOIN THE REVOLUTION' : 'ENTER THE MATRIX'}\r\n                </span>\r\n              </span>\r\n            </button>\r\n          </div>\r\n\r\n          <div className=\"trust-indicators\">\r\n            <div className=\"trust-item\">\r\n              <span className=\"trust-icon\">🔒</span>\r\n              <span className=\"trust-text\">Bank-Level Security</span>\r\n            </div>\r\n            <div className=\"trust-item\">\r\n              <span className=\"trust-icon\">⚡</span>\r\n              <span className=\"trust-text\">Instant Access</span>\r\n            </div>\r\n            <div className=\"trust-item\">\r\n              <span className=\"trust-icon\">🌍</span>\r\n              <span className=\"trust-text\">Global Community</span>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default AuthForm;"], "mappings": ";;AAAA,SAASA,QAAQ,QAAQ,OAAO;AAChC,OAAO,gBAAgB;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAExB,MAAMC,QAAQ,GAAGA,CAAC;EAAEC,IAAI;EAAEC;AAAQ,CAAC,KAAK;EAAAC,EAAA;EACtC,MAAM,CAACC,OAAO,EAAEC,UAAU,CAAC,GAAGV,QAAQ,CAAC,IAAI,CAAC;EAC5C,MAAM,CAACW,QAAQ,EAAEC,WAAW,CAAC,GAAGZ,QAAQ,CAAC;IACvCa,QAAQ,EAAE,EAAE;IACZC,KAAK,EAAE,EAAE;IACTC,QAAQ,EAAE,EAAE;IACZC,eAAe,EAAE;EACnB,CAAC,CAAC;EACF,MAAM,CAACC,OAAO,EAAEC,UAAU,CAAC,GAAGlB,QAAQ,CAAC,KAAK,CAAC;EAC7C,MAAM,CAACmB,OAAO,EAAEC,UAAU,CAAC,GAAGpB,QAAQ,CAAC,EAAE,CAAC;EAC1C,MAAM,CAACqB,WAAW,EAAEC,cAAc,CAAC,GAAGtB,QAAQ,CAAC,EAAE,CAAC,CAAC,CAAC;;EAEpD;EACA,MAAMuB,SAAS,GAAGA,CAAA,KAAM;IACtB,OAAOC,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,GACxC,MAAM,GACN,2BAA2B;EACjC,CAAC;EAED,MAAMC,iBAAiB,GAAIC,CAAC,IAAK;IAC/BhB,WAAW,CAAC;MACV,GAAGD,QAAQ;MACX,CAACiB,CAAC,CAACC,MAAM,CAACC,IAAI,GAAGF,CAAC,CAACC,MAAM,CAACE;IAC5B,CAAC,CAAC;EACJ,CAAC;EAED,MAAMC,WAAW,GAAGA,CAACC,IAAI,EAAEC,IAAI,KAAK;IAClCd,UAAU,CAACa,IAAI,CAAC;IAChBX,cAAc,CAACY,IAAI,CAAC;IACpBC,UAAU,CAAC,MAAM;MACff,UAAU,CAAC,EAAE,CAAC;MACdE,cAAc,CAAC,EAAE,CAAC;IACpB,CAAC,EAAE,IAAI,CAAC;EACV,CAAC;EAED,MAAMc,YAAY,GAAG,MAAOR,CAAC,IAAK;IAChCA,CAAC,CAACS,cAAc,CAAC,CAAC;IAClBnB,UAAU,CAAC,IAAI,CAAC;IAEhB,IAAI;MACF,IAAI,CAACT,OAAO,EAAE;QACZ;QACA,IAAIE,QAAQ,CAACI,QAAQ,KAAKJ,QAAQ,CAACK,eAAe,EAAE;UAClDgB,WAAW,CAAC,yBAAyB,EAAE,OAAO,CAAC;UAC/Cd,UAAU,CAAC,KAAK,CAAC;UACjB;QACF;QACA,IAAIP,QAAQ,CAACI,QAAQ,CAACuB,MAAM,GAAG,CAAC,EAAE;UAChCN,WAAW,CAAC,8CAA8C,EAAE,OAAO,CAAC;UACpEd,UAAU,CAAC,KAAK,CAAC;UACjB;QACF;MACF;MAEA,MAAMqB,QAAQ,GAAG9B,OAAO,GAAG,aAAa,GAAG,gBAAgB;MAC3D,MAAM+B,OAAO,GAAG/B,OAAO,GACnB;QAAEK,KAAK,EAAEH,QAAQ,CAACG,KAAK;QAAEC,QAAQ,EAAEJ,QAAQ,CAACI;MAAS,CAAC,GACtD;QACEF,QAAQ,EAAEF,QAAQ,CAACE,QAAQ;QAC3BC,KAAK,EAAEH,QAAQ,CAACG,KAAK;QACrBC,QAAQ,EAAEJ,QAAQ,CAACI;MACrB,CAAC;MAEL,MAAM0B,QAAQ,GAAG,MAAMC,KAAK,CAAC,GAAGnB,SAAS,CAAC,CAAC,GAAGgB,QAAQ,EAAE,EAAE;QACxDI,MAAM,EAAE,MAAM;QACdC,OAAO,EAAE;UACP,cAAc,EAAE;QAClB,CAAC;QACDC,IAAI,EAAEC,IAAI,CAACC,SAAS,CAACP,OAAO;MAC9B,CAAC,CAAC;MAEF,MAAMQ,IAAI,GAAG,MAAMP,QAAQ,CAACQ,IAAI,CAAC,CAAC;MAElC,IAAIR,QAAQ,CAACS,EAAE,EAAE;QACf;QACAC,YAAY,CAACC,OAAO,CAAC,WAAW,EAAEJ,IAAI,CAACK,KAAK,CAAC;QAC7CF,YAAY,CAACC,OAAO,CAAC,UAAU,EAAEN,IAAI,CAACC,SAAS,CAACC,IAAI,CAAC1C,IAAI,CAAC,CAAC;QAC3DC,OAAO,CAACyC,IAAI,CAAC1C,IAAI,CAAC;QAClB0B,WAAW,CACTvB,OAAO,GACH,iBAAiBuC,IAAI,CAAC1C,IAAI,CAACO,QAAQ,MAAM,GACzC,0CAA0CmC,IAAI,CAAC1C,IAAI,CAACO,QAAQ,MAAM,EACtE,SACF,CAAC;;QAED;QACAD,WAAW,CAAC;UACVC,QAAQ,EAAE,EAAE;UACZC,KAAK,EAAE,EAAE;UACTC,QAAQ,EAAE,EAAE;UACZC,eAAe,EAAE;QACnB,CAAC,CAAC;MACJ,CAAC,MAAM;QACL;QACAgB,WAAW,CAACgB,IAAI,CAACM,KAAK,IAAI,uBAAuB,EAAE,OAAO,CAAC;MAC7D;IACF,CAAC,CAAC,OAAOA,KAAK,EAAE;MACdtB,WAAW,CAAC,0DAA0D,EAAE,OAAO,CAAC;IAClF,CAAC,SAAS;MACRd,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;EAED,MAAMqC,UAAU,GAAGA,CAAA,KAAM;IACvB7C,UAAU,CAAC,CAACD,OAAO,CAAC;IACpBW,UAAU,CAAC,EAAE,CAAC;IACdR,WAAW,CAAC;MACVC,QAAQ,EAAE,EAAE;MACZC,KAAK,EAAE,EAAE;MACTC,QAAQ,EAAE,EAAE;MACZC,eAAe,EAAE;IACnB,CAAC,CAAC;EACJ,CAAC;EAED,IAAIV,IAAI,EAAE;IACR,oBACEJ,OAAA;MAAKsD,SAAS,EAAC,gBAAgB;MAAAC,QAAA,eAC7BvD,OAAA;QAAKsD,SAAS,EAAC,cAAc;QAAAC,QAAA,gBAC3BvD,OAAA;UAAKsD,SAAS,EAAC,gBAAgB;UAAAC,QAAA,gBAC7BvD,OAAA;YAAKsD,SAAS,EAAC,gBAAgB;YAAAC,QAAA,eAC7BvD,OAAA;cAAMsD,SAAS,EAAC,cAAc;cAAAC,QAAA,EAAC;YAAE;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACrC,CAAC,eACN3D,OAAA;YAAKsD,SAAS,EAAC,cAAc;YAAAC,QAAA,gBAC3BvD,OAAA;cAAIsD,SAAS,EAAC,cAAc;cAAAC,QAAA,gBAC1BvD,OAAA;gBAAMsD,SAAS,EAAC,eAAe;gBAAAC,QAAA,EAAC;cAAE;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,aAChC,EAACvD,IAAI,CAACO,QAAQ,EAAC,GAC1B;YAAA;cAAA6C,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACL3D,OAAA;cAAGsD,SAAS,EAAC,eAAe;cAAAC,QAAA,EAAEnD,IAAI,CAACQ;YAAK;cAAA4C,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC1C,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAEN3D,OAAA;UAAKsD,SAAS,EAAC,eAAe;UAAAC,QAAA,gBAC5BvD,OAAA;YAAKsD,SAAS,EAAC,WAAW;YAAAC,QAAA,gBACxBvD,OAAA;cAAMsD,SAAS,EAAC,WAAW;cAAAC,QAAA,EAAC;YAAE;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eACrC3D,OAAA;cAAKsD,SAAS,EAAC,cAAc;cAAAC,QAAA,gBAC3BvD,OAAA;gBAAAuD,QAAA,EAAI;cAAiB;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eAC1B3D,OAAA;gBAAAuD,QAAA,EAAG;cAAgB;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACpB,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eACN3D,OAAA;YAAKsD,SAAS,EAAC,WAAW;YAAAC,QAAA,gBACxBvD,OAAA;cAAMsD,SAAS,EAAC,WAAW;cAAAC,QAAA,EAAC;YAAC;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eACpC3D,OAAA;cAAKsD,SAAS,EAAC,cAAc;cAAAC,QAAA,gBAC3BvD,OAAA;gBAAAuD,QAAA,EAAI;cAAc;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACvB3D,OAAA;gBAAAuD,QAAA,EAAG;cAAa;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACjB,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAEN3D,OAAA;UAAKsD,SAAS,EAAC,iBAAiB;UAAAC,QAAA,gBAC9BvD,OAAA;YAAQsD,SAAS,EAAC,0BAA0B;YAAAC,QAAA,gBAC1CvD,OAAA;cAAMsD,SAAS,EAAC,UAAU;cAAAC,QAAA,EAAC;YAAE;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,kBAEtC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eACT3D,OAAA;YAAQsD,SAAS,EAAC,yBAAyB;YAAAC,QAAA,gBACzCvD,OAAA;cAAMsD,SAAS,EAAC,UAAU;cAAAC,QAAA,EAAC;YAAE;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,gBAEtC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAEV;EAEA,oBACE3D,OAAA;IAAKsD,SAAS,EAAC,gBAAgB;IAAAC,QAAA,eAC7BvD,OAAA;MAAKsD,SAAS,EAAC,mBAAmB;MAAAC,QAAA,gBAChCvD,OAAA;QAAKsD,SAAS,EAAC,aAAa;QAAAC,QAAA,gBAC1BvD,OAAA;UAAKsD,SAAS,EAAC,mBAAmB;UAAAC,QAAA,eAChCvD,OAAA;YAAKsD,SAAS,EAAC,cAAc;YAAAC,QAAA,gBAC3BvD,OAAA;cAAKsD,SAAS,EAAC,eAAe;cAAAC,QAAA,eAC5BvD,OAAA;gBAAMsD,SAAS,EAAC,aAAa;gBAAAC,QAAA,EAAC;cAAE;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACpC,CAAC,eACN3D,OAAA;cAAKsD,SAAS,EAAC,eAAe;cAAAC,QAAA,eAC5BvD,OAAA;gBAAMsD,SAAS,EAAC,aAAa;gBAAAC,QAAA,EAAC;cAAC;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACnC,CAAC,eACN3D,OAAA;cAAKsD,SAAS,EAAC,eAAe;cAAAC,QAAA,eAC5BvD,OAAA;gBAAMsD,SAAS,EAAC,aAAa;gBAAAC,QAAA,EAAC;cAAE;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACpC,CAAC,eACN3D,OAAA;cAAKsD,SAAS,EAAC,aAAa;cAAAC,QAAA,gBAC1BvD,OAAA;gBAAMsD,SAAS,EAAC,WAAW;gBAAAC,QAAA,EAAC;cAAE;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,eACrC3D,OAAA;gBAAKsD,SAAS,EAAC,cAAc;gBAAAC,QAAA,gBAC3BvD,OAAA;kBAAKsD,SAAS,EAAC;gBAAa;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,eACnC3D,OAAA;kBAAKsD,SAAS,EAAC;gBAAa;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,eACnC3D,OAAA;kBAAKsD,SAAS,EAAC;gBAAa;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAChC,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAEN3D,OAAA;UAAKsD,SAAS,EAAC,iBAAiB;UAAAC,QAAA,gBAC9BvD,OAAA;YAAIsD,SAAS,EAAC,YAAY;YAAAC,QAAA,gBACxBvD,OAAA;cAAMsD,SAAS,EAAC,YAAY;cAAAC,QAAA,EACzBhD,OAAO,GAAG,wBAAwB,GAAG;YAA8B;cAAAiD,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAChE,CAAC,eACP3D,OAAA;cAAKsD,SAAS,EAAC,eAAe;cAAAC,QAAA,gBAC5BvD,OAAA;gBAAMsD,SAAS,EAAC,4BAA4B;gBAAAC,QAAA,EACzChD,OAAO,GAAG,GAAG,GAAG;cAAI;gBAAAiD,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACjB,CAAC,eACP3D,OAAA;gBAAKsD,SAAS,EAAC,eAAe;gBAAAC,QAAA,gBAC5BvD,OAAA;kBAAMsD,SAAS,EAAC,SAAS;kBAAAC,QAAA,EAAC;gBAAC;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,eAClC3D,OAAA;kBAAMsD,SAAS,EAAC,SAAS;kBAAAC,QAAA,EAAC;gBAAC;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,eAClC3D,OAAA;kBAAMsD,SAAS,EAAC,SAAS;kBAAAC,QAAA,EAAC;gBAAE;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAChC,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACJ,CAAC,eAEL3D,OAAA;YAAKsD,SAAS,EAAC,oBAAoB;YAAAC,QAAA,gBACjCvD,OAAA;cAAGsD,SAAS,EAAC,eAAe;cAAAC,QAAA,EACzBhD,OAAO,GACJ,8DAA8D,GAC9D;YAAgE;cAAAiD,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAEnE,CAAC,eACJ3D,OAAA;cAAKsD,SAAS,EAAC,cAAc;cAAAC,QAAA,gBAC3BvD,OAAA;gBAAKsD,SAAS,EAAC,aAAa;gBAAAC,QAAA,gBAC1BvD,OAAA;kBAAMsD,SAAS,EAAC,aAAa;kBAAAC,QAAA,EAAC;gBAAI;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,eACzC3D,OAAA;kBAAMsD,SAAS,EAAC,YAAY;kBAAAC,QAAA,EAAC;gBAAQ;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACzC,CAAC,eACN3D,OAAA;gBAAKsD,SAAS,EAAC,aAAa;gBAAAC,QAAA,gBAC1BvD,OAAA;kBAAMsD,SAAS,EAAC,aAAa;kBAAAC,QAAA,EAAC;gBAAG;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,eACxC3D,OAAA;kBAAMsD,SAAS,EAAC,YAAY;kBAAAC,QAAA,EAAC;gBAAO;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACxC,CAAC,eACN3D,OAAA;gBAAKsD,SAAS,EAAC,aAAa;gBAAAC,QAAA,gBAC1BvD,OAAA;kBAAMsD,SAAS,EAAC,aAAa;kBAAAC,QAAA,EAAC;gBAAC;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,eACtC3D,OAAA;kBAAMsD,SAAS,EAAC,YAAY;kBAAAC,QAAA,EAAC;gBAAa;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC9C,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAEN3D,OAAA;QAAM4D,QAAQ,EAAE1B,YAAa;QAACoB,SAAS,EAAC,WAAW;QAAAC,QAAA,GAChD,CAAChD,OAAO,iBACPP,OAAA;UAAKsD,SAAS,EAAC,YAAY;UAAAC,QAAA,gBACzBvD,OAAA;YAAOsD,SAAS,EAAC,YAAY;YAAAC,QAAA,gBAC3BvD,OAAA;cAAMsD,SAAS,EAAC,YAAY;cAAAC,QAAA,EAAC;YAAE;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,YAExC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC,eACR3D,OAAA;YACEgC,IAAI,EAAC,MAAM;YACXJ,IAAI,EAAC,UAAU;YACfC,KAAK,EAAEpB,QAAQ,CAACE,QAAS;YACzBkD,QAAQ,EAAEpC,iBAAkB;YAC5BqC,WAAW,EAAC,4BAA4B;YACxCR,SAAS,EAAC,YAAY;YACtBS,QAAQ,EAAE,CAACxD,OAAQ;YACnByD,QAAQ,EAAEjD;UAAQ;YAAAyC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACnB,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CACN,eAED3D,OAAA;UAAKsD,SAAS,EAAC,YAAY;UAAAC,QAAA,gBACzBvD,OAAA;YAAOsD,SAAS,EAAC,YAAY;YAAAC,QAAA,gBAC3BvD,OAAA;cAAMsD,SAAS,EAAC,YAAY;cAAAC,QAAA,EAAC;YAAE;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,SAExC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC,eACR3D,OAAA;YACEgC,IAAI,EAAC,OAAO;YACZJ,IAAI,EAAC,OAAO;YACZC,KAAK,EAAEpB,QAAQ,CAACG,KAAM;YACtBiD,QAAQ,EAAEpC,iBAAkB;YAC5BqC,WAAW,EAAC,wBAAwB;YACpCR,SAAS,EAAC,YAAY;YACtBS,QAAQ;YACRC,QAAQ,EAAEjD;UAAQ;YAAAyC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACnB,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC,eAEN3D,OAAA;UAAKsD,SAAS,EAAC,YAAY;UAAAC,QAAA,gBACzBvD,OAAA;YAAOsD,SAAS,EAAC,YAAY;YAAAC,QAAA,gBAC3BvD,OAAA;cAAMsD,SAAS,EAAC,YAAY;cAAAC,QAAA,EAAC;YAAE;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,YAExC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC,eACR3D,OAAA;YACEgC,IAAI,EAAC,UAAU;YACfJ,IAAI,EAAC,UAAU;YACfC,KAAK,EAAEpB,QAAQ,CAACI,QAAS;YACzBgD,QAAQ,EAAEpC,iBAAkB;YAC5BqC,WAAW,EAAEvD,OAAO,GAAG,qBAAqB,GAAG,0CAA2C;YAC1F+C,SAAS,EAAC,YAAY;YACtBS,QAAQ;YACRC,QAAQ,EAAEjD,OAAQ;YAClBkD,SAAS,EAAE1D,OAAO,GAAG2D,SAAS,GAAG;UAAE;YAAAV,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACpC,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC,EAEL,CAACpD,OAAO,iBACPP,OAAA;UAAKsD,SAAS,EAAC,YAAY;UAAAC,QAAA,gBACzBvD,OAAA;YAAOsD,SAAS,EAAC,YAAY;YAAAC,QAAA,gBAC3BvD,OAAA;cAAMsD,SAAS,EAAC,YAAY;cAAAC,QAAA,EAAC;YAAE;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,oBAExC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC,eACR3D,OAAA;YACEgC,IAAI,EAAC,UAAU;YACfJ,IAAI,EAAC,iBAAiB;YACtBC,KAAK,EAAEpB,QAAQ,CAACK,eAAgB;YAChC+C,QAAQ,EAAEpC,iBAAkB;YAC5BqC,WAAW,EAAC,uBAAuB;YACnCR,SAAS,EAAC,YAAY;YACtBS,QAAQ,EAAE,CAACxD,OAAQ;YACnByD,QAAQ,EAAEjD;UAAQ;YAAAyC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACnB,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CACN,EAEA1C,OAAO,iBACNjB,OAAA;UAAKsD,SAAS,EAAE,WAAWnC,WAAW,EAAG;UAAAoC,QAAA,gBACvCvD,OAAA;YAAMsD,SAAS,EAAC,cAAc;YAAAC,QAAA,EAC3BpC,WAAW,KAAK,SAAS,GAAG,GAAG,GAAG;UAAG;YAAAqC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAClC,CAAC,EACN1C,OAAO;QAAA;UAAAuC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACL,CACN,eAED3D,OAAA;UACEgC,IAAI,EAAC,QAAQ;UACbsB,SAAS,EAAC,6BAA6B;UACvCU,QAAQ,EAAEjD,OAAQ;UAAAwC,QAAA,gBAElBvD,OAAA;YAAKsD,SAAS,EAAC;UAAkB;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eACxC3D,OAAA;YAAKsD,SAAS,EAAC,gBAAgB;YAAAC,QAAA,EAC5BxC,OAAO,gBACNf,OAAA,CAAAE,SAAA;cAAAqD,QAAA,gBACEvD,OAAA;gBAAKsD,SAAS,EAAC,mBAAmB;gBAAAC,QAAA,gBAChCvD,OAAA;kBAAKsD,SAAS,EAAC;gBAAa;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,eACnC3D,OAAA;kBAAKsD,SAAS,EAAC,mBAAmB;kBAAAC,QAAA,gBAChCvD,OAAA;oBAAMsD,SAAS,EAAC,UAAU;oBAAAC,QAAA,EAAC;kBAAC;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC,eACnC3D,OAAA;oBAAMsD,SAAS,EAAC,UAAU;oBAAAC,QAAA,EAAC;kBAAC;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC,eACnC3D,OAAA;oBAAMsD,SAAS,EAAC,UAAU;oBAAAC,QAAA,EAAC;kBAAE;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACjC,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,eACN3D,OAAA;gBAAMsD,SAAS,EAAC,cAAc;gBAAAC,QAAA,EAC3BhD,OAAO,GAAG,4BAA4B,GAAG;cAA8B;gBAAAiD,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACpE,CAAC;YAAA,eACP,CAAC,gBAEH3D,OAAA,CAAAE,SAAA;cAAAqD,QAAA,gBACEvD,OAAA;gBAAMsD,SAAS,EAAC,oBAAoB;gBAAAC,QAAA,EACjChD,OAAO,GAAG,GAAG,GAAG;cAAG;gBAAAiD,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAChB,CAAC,eACP3D,OAAA;gBAAMsD,SAAS,EAAC,UAAU;gBAAAC,QAAA,EACvBhD,OAAO,GAAG,oBAAoB,GAAG;cAAkB;gBAAAiD,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAChD,CAAC,eACP3D,OAAA;gBAAKsD,SAAS,EAAC,kBAAkB;gBAAAC,QAAA,gBAC/BvD,OAAA;kBAAMsD,SAAS,EAAC,cAAc;kBAAAC,QAAA,EAAC;gBAAC;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,eACvC3D,OAAA;kBAAMsD,SAAS,EAAC,cAAc;kBAAAC,QAAA,EAAC;gBAAE;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,eACxC3D,OAAA;kBAAMsD,SAAS,EAAC,cAAc;kBAAAC,QAAA,EAAC;gBAAE;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACrC,CAAC;YAAA,eACN;UACH;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACA,CAAC,EAER,CAACpD,OAAO,iBACPP,OAAA;UAAKsD,SAAS,EAAC,gBAAgB;UAAAC,QAAA,gBAC7BvD,OAAA;YAAIsD,SAAS,EAAC,gBAAgB;YAAAC,QAAA,gBAC5BvD,OAAA;cAAMsD,SAAS,EAAC,eAAe;cAAAC,QAAA,EAAC;YAAC;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,4BAE1C;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACL3D,OAAA;YAAKsD,SAAS,EAAC,eAAe;YAAAC,QAAA,gBAC5BvD,OAAA;cAAKsD,SAAS,EAAC,cAAc;cAAAC,QAAA,gBAC3BvD,OAAA;gBAAMsD,SAAS,EAAC,eAAe;gBAAAC,QAAA,EAAC;cAAE;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,eACzC3D,OAAA;gBAAMsD,SAAS,EAAC,cAAc;gBAAAC,QAAA,EAAC;cAAc;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACjD,CAAC,eACN3D,OAAA;cAAKsD,SAAS,EAAC,cAAc;cAAAC,QAAA,gBAC3BvD,OAAA;gBAAMsD,SAAS,EAAC,eAAe;gBAAAC,QAAA,EAAC;cAAC;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,eACxC3D,OAAA;gBAAMsD,SAAS,EAAC,cAAc;gBAAAC,QAAA,EAAC;cAAe;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAClD,CAAC,eACN3D,OAAA;cAAKsD,SAAS,EAAC,cAAc;cAAAC,QAAA,gBAC3BvD,OAAA;gBAAMsD,SAAS,EAAC,eAAe;gBAAAC,QAAA,EAAC;cAAE;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,eACzC3D,OAAA;gBAAMsD,SAAS,EAAC,cAAc;gBAAAC,QAAA,EAAC;cAAoB;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACvD,CAAC,eACN3D,OAAA;cAAKsD,SAAS,EAAC,cAAc;cAAAC,QAAA,gBAC3BvD,OAAA;gBAAMsD,SAAS,EAAC,eAAe;gBAAAC,QAAA,EAAC;cAAE;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,eACzC3D,OAAA;gBAAMsD,SAAS,EAAC,cAAc;gBAAAC,QAAA,EAAC;cAAe;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAClD,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CACN;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACG,CAAC,eAEP3D,OAAA;QAAKsD,SAAS,EAAC,aAAa;QAAAC,QAAA,gBAC1BvD,OAAA;UAAKsD,SAAS,EAAC,kBAAkB;UAAAC,QAAA,gBAC/BvD,OAAA;YAAGsD,SAAS,EAAC,kBAAkB;YAAAC,QAAA,EAC5BhD,OAAO,GAAG,kCAAkC,GAAG;UAA+B;YAAAiD,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC9E,CAAC,eACJ3D,OAAA;YACEgC,IAAI,EAAC,QAAQ;YACbmC,OAAO,EAAEd,UAAW;YACpBC,SAAS,EAAC,+BAA+B;YACzCU,QAAQ,EAAEjD,OAAQ;YAAAwC,QAAA,gBAElBvD,OAAA;cAAKsD,SAAS,EAAC;YAAkB;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eACxC3D,OAAA;cAAMsD,SAAS,EAAC,gBAAgB;cAAAC,QAAA,gBAC9BvD,OAAA;gBAAMsD,SAAS,EAAC,uBAAuB;gBAAAC,QAAA,EACpChD,OAAO,GAAG,IAAI,GAAG;cAAG;gBAAAiD,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACjB,CAAC,eACP3D,OAAA;gBAAMsD,SAAS,EAAC,aAAa;gBAAAC,QAAA,EAC1BhD,OAAO,GAAG,qBAAqB,GAAG;cAAkB;gBAAAiD,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACjD,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACD,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC,eAEN3D,OAAA;UAAKsD,SAAS,EAAC,kBAAkB;UAAAC,QAAA,gBAC/BvD,OAAA;YAAKsD,SAAS,EAAC,YAAY;YAAAC,QAAA,gBACzBvD,OAAA;cAAMsD,SAAS,EAAC,YAAY;cAAAC,QAAA,EAAC;YAAE;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eACtC3D,OAAA;cAAMsD,SAAS,EAAC,YAAY;cAAAC,QAAA,EAAC;YAAmB;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACpD,CAAC,eACN3D,OAAA;YAAKsD,SAAS,EAAC,YAAY;YAAAC,QAAA,gBACzBvD,OAAA;cAAMsD,SAAS,EAAC,YAAY;cAAAC,QAAA,EAAC;YAAC;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eACrC3D,OAAA;cAAMsD,SAAS,EAAC,YAAY;cAAAC,QAAA,EAAC;YAAc;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC/C,CAAC,eACN3D,OAAA;YAAKsD,SAAS,EAAC,YAAY;YAAAC,QAAA,gBACzBvD,OAAA;cAAMsD,SAAS,EAAC,YAAY;cAAAC,QAAA,EAAC;YAAE;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eACtC3D,OAAA;cAAMsD,SAAS,EAAC,YAAY;cAAAC,QAAA,EAAC;YAAgB;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACjD,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAACrD,EAAA,CAtaIH,QAAQ;AAAAiE,EAAA,GAARjE,QAAQ;AAwad,eAAeA,QAAQ;AAAC,IAAAiE,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}