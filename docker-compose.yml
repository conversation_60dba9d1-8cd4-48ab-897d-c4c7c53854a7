version: '3.8'

services:
  # Backend API service
  backend:
    build:
      context: ./ai-generative/backend
      dockerfile: Dockerfile
    container_name: ai-content-backend
    ports:
      - "5000:5000"
    environment:
      - NODE_ENV=production
      - PORT=5000
      - OPENAI_API_KEY=${OPENAI_API_KEY}
      - JWT_SECRET=${JWT_SECRET}
    volumes:
      - ./logs:/app/logs
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "node", "healthcheck.js"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s
    networks:
      - ai-content-network

  # Frontend React service
  frontend:
    build:
      context: ./ai-generative/client
      dockerfile: Dockerfile
    container_name: ai-content-frontend
    ports:
      - "80:80"
    depends_on:
      - backend
    restart: unless-stopped
    networks:
      - ai-content-network

  # Optional: Redis for caching (uncomment if needed)
  # redis:
  #   image: redis:7-alpine
  #   container_name: ai-content-redis
  #   ports:
  #     - "6379:6379"
  #   volumes:
  #     - redis_data:/data
  #   restart: unless-stopped
  #   networks:
  #     - ai-content-network

  # Optional: PostgreSQL database (uncomment if needed)
  # postgres:
  #   image: postgres:15-alpine
  #   container_name: ai-content-postgres
  #   environment:
  #     - POSTGRES_DB=ai_content
  #     - POSTGRES_USER=${DB_USER:-postgres}
  #     - POSTGRES_PASSWORD=${DB_PASSWORD}
  #   volumes:
  #     - postgres_data:/var/lib/postgresql/data
  #   ports:
  #     - "5432:5432"
  #   restart: unless-stopped
  #   networks:
  #     - ai-content-network

networks:
  ai-content-network:
    driver: bridge

volumes:
  # Uncomment if using database
  # postgres_data:
  # redis_data:
  logs:
