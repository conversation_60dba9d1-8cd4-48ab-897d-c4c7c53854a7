
import './ContentTypeSelector.css';

const ContentTypeSelector = ({ value, onChange }) => {
  const contentTypes = [
    { value: 'blog', label: 'Blog Post', icon: '📝', description: 'Long-form articles and posts' },
    { value: 'social', label: 'Social Post', icon: '📱', description: 'Social media content' },
    { value: 'ad', label: 'Advertisement', icon: '📢', description: 'Marketing and promotional copy' }
  ];

  return (
    <div className="content-type-selector">
      <label className="selector-label">
        <span className="label-icon">🎯</span>
        Content Type
      </label>
      <div className="content-type-grid">
        {contentTypes.map((type) => (
          <button
            key={type.value}
            className={`content-type-card ${value === type.value ? 'active' : ''}`}
            onClick={() => onChange && onChange(type.value)}
          >
            <div className="card-icon">{type.icon}</div>
            <div className="card-content">
              <h4 className="card-title">{type.label}</h4>
              <p className="card-description">{type.description}</p>
            </div>
          </button>
        ))}
      </div>
    </div>
  );
};

export default ContentTypeSelector;